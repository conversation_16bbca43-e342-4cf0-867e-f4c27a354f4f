import { useRef } from 'react';

/**
 * Custom hook that provides stable keys for list items,
 * It is useful when id/unique key is not available in JSON.
 * @param {string} prefix - Optional prefix for the generated keys (default: 'item')
 * @returns {Function} A function that takes an item and returns a stable key for it
 */
function useStableKeys(prefix = 'item') {
  const itemKeysRef = useRef(new Map());

  const getStableKey = (item) => {
    if (!itemKeysRef.current.has(item)) {
      itemKeysRef.current.set(item, `${prefix}_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`);
    }
    return itemKeysRef.current.get(item);
  };

  return getStableKey;
}

export default useStableKeys;

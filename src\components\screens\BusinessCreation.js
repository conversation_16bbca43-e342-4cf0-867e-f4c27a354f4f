import React, { useState, useEffect } from 'react';
import { Form, Button, Dropdown } from 'semantic-ui-react';
import { useFormik } from 'formik';
import * as Yup from 'yup';
import style from './scss/businessCreation.module.scss';
import NavigationBar from './NavigationBar';
import Header from '../global/Header';
import GlobalService from '../services/GlobalServices';
import { resturls } from '../utils/apiurls';
import { useNavigate } from 'react-router-dom';

const BusinessCreation = ({ editMode, data }) => {
  const [usersList, setUserList] = useState();
  const navigate = useNavigate();

  const validationSchema = Yup.object({
    name: Yup.string().required('This field is required.'),
    gst: Yup.string().required('This field is required.'),
    email: Yup.string().email('Invalid email format').required('This field is required.'),
    phone: Yup.string()
      .matches(/^[0-9]{10}$/, 'Must be a valid 10-digit phone number')
      .required('This field is required.'),
    address: Yup.string().required('This field is required.'),
    business_superuser: Yup.string().required('Please selected business superuser.'),
    // google_map_link: Yup.string().url('Must be a valid URL').required('This field is required.'),
    business_superuser: Yup.string().required('This field is required.'),
  });
  console.log(data, 'data');

  const formik = useFormik({
    initialValues: {
      name: data?.business_name || '',
      gst: '',
      email: '',
      phone: '',
      address: '',
      google_map_link: '',
      business_superuser: data?.business_superuser_id || '',
      logo: '',
    },
    validationSchema,
    onSubmit: (values) => {
      GlobalService.generalSelect(
        (respdata) => {
          navigate('/');
        },
        `${resturls.createBusiness}`,
        values,
        'POST'
      );
    },
  });

  useEffect(() => {
    GlobalService.generalSelect(
      (respdata) => {
        const { data } = respdata;
        setUserList(
          data.map((user) => ({
            key: user.user_id,
            value: user?.user_id,
            text: user.full_name?.length === 0 ? user?.email : user?.full_name,
          }))
        );
      },
      `${resturls.obtainCategoryWiseUser}?user_type=business_users`,
      {},
      'GET'
    );
  }, []);

  if (editMode) {
    return (
      <div className={style.formWrapper}>
        <Form onSubmit={formik.handleSubmit}>
          <div className={style.formContainer}>
            <Form.Field className={style.formField}>
              <label>Business Name</label>
              <Form.Input
                id="name"
                name="name"
                placeholder="Enter business name"
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
                value={formik.values.name}
                error={
                  formik.touched.name && formik.errors.name ? { content: formik.errors.name, pointing: 'below' } : null
                }
              />
            </Form.Field>

            <Form.Field className={style.formField}>
              <label>GST</label>
              <Form.Input
                id="gst"
                name="gst"
                placeholder="Enter GST"
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
                value={formik.values.gst}
                error={
                  formik.touched.gst && formik.errors.gst ? { content: formik.errors.gst, pointing: 'below' } : null
                }
              />
            </Form.Field>

            <Form.Field className={style.formField}>
              <label>Email</label>
              <Form.Input
                id="email"
                name="email"
                type="email"
                placeholder="Enter email"
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
                value={formik.values.email}
                error={
                  formik.touched.email && formik.errors.email
                    ? { content: formik.errors.email, pointing: 'below' }
                    : null
                }
              />
            </Form.Field>

            <Form.Field className={style.formField}>
              <label>Logo</label>
              <Form.Input
                type="file"
                id="logo"
                name="logo"
                onChange={(event) => formik.setFieldValue('logo', event.target.files[0])}
                error={
                  formik.touched.profile_image && formik.errors.profile_image
                    ? { content: formik.errors.profile_image, pointing: 'below' }
                    : null
                }
              />
            </Form.Field>

            <Form.Field className={style.formField}>
              <label>Phone</label>
              <Form.Input
                id="phone"
                name="phone"
                placeholder="Enter phone number"
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
                value={formik.values.phone}
                error={
                  formik.touched.phone && formik.errors.phone
                    ? { content: formik.errors.phone, pointing: 'below' }
                    : null
                }
              />
            </Form.Field>

            <Form.Field className={style.formField}>
              <label>Address</label>
              <Form.TextArea
                id="address"
                name="address"
                placeholder="Enter address"
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
                value={formik.values.address}
                error={
                  formik.touched.address && formik.errors.address
                    ? { content: formik.errors.address, pointing: 'below' }
                    : null
                }
              />
            </Form.Field>

            <Form.Field className={style.formField}>
              <label>Google Map Link</label>
              <Form.Input
                id="google_map_link"
                name="google_map_link"
                placeholder="Enter Google Map link"
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
                value={formik.values.google_map_link}
                error={
                  formik.touched.google_map_link && formik.errors.google_map_link
                    ? { content: formik.errors.google_map_link, pointing: 'below' }
                    : null
                }
              />
            </Form.Field>

            <Form.Field className={style.formField}>
              <label>Business Superuser</label>
              <Form.Select
                id="business_superuser"
                name="business_superuser"
                placeholder="Select business superuser"
                fluid
                search
                selection
                className="customDropdown"
                options={usersList}
                onChange={(e, { value }) => formik.setFieldValue('business_superuser', value)}
                onBlur={() => formik.setFieldTouched('business_superuser', true)}
                value={formik.values.business_superuser}
                error={
                  formik.touched.business_superuser && formik.errors.business_superuser
                    ? { content: formik.errors.business_superuser, pointing: 'below' }
                    : null
                }
              />
            </Form.Field>
          </div>
          <div className={style.submitBtn}>
            <Button type="submit" primary>
              Submit
            </Button>
          </div>
        </Form>
      </div>
    );
  }

  return (
    <>
      <Header />
      <div className={style.bussinessCreateScreen}>
        <div className={style.navigationWrapper}>
          <NavigationBar disable />
        </div>
        <div className={style.rightContentWrapper}>
          <h4>Create Business</h4>
          <hr />
          <div className={style.formWrapper}>
            <Form onSubmit={formik.handleSubmit}>
              <div className={style.formContainer}>
                <Form.Field className={style.formField}>
                  <label>Business Name</label>
                  <Form.Input
                    id="name"
                    name="name"
                    placeholder="Enter business name"
                    onChange={formik.handleChange}
                    onBlur={formik.handleBlur}
                    value={formik.values.name}
                    error={
                      formik.touched.name && formik.errors.name
                        ? { content: formik.errors.name, pointing: 'below' }
                        : null
                    }
                  />
                </Form.Field>

                <Form.Field className={style.formField}>
                  <label>GST</label>
                  <Form.Input
                    id="gst"
                    name="gst"
                    placeholder="Enter GST"
                    onChange={formik.handleChange}
                    onBlur={formik.handleBlur}
                    value={formik.values.gst}
                    error={
                      formik.touched.gst && formik.errors.gst ? { content: formik.errors.gst, pointing: 'below' } : null
                    }
                  />
                </Form.Field>

                <Form.Field className={style.formField}>
                  <label>Email</label>
                  <Form.Input
                    id="email"
                    name="email"
                    type="email"
                    placeholder="Enter email"
                    onChange={formik.handleChange}
                    onBlur={formik.handleBlur}
                    value={formik.values.email}
                    error={
                      formik.touched.email && formik.errors.email
                        ? { content: formik.errors.email, pointing: 'below' }
                        : null
                    }
                  />
                </Form.Field>

                <Form.Field className={style.formField}>
                  <label>Logo</label>
                  <Form.Input
                    type="file"
                    id="logo"
                    name="logo"
                    onChange={(event) => formik.setFieldValue('logo', event.target.files[0])}
                    error={
                      formik.touched.profile_image && formik.errors.profile_image
                        ? { content: formik.errors.profile_image, pointing: 'below' }
                        : null
                    }
                  />
                </Form.Field>

                <Form.Field className={style.formField}>
                  <label>Phone</label>
                  <Form.Input
                    id="phone"
                    name="phone"
                    placeholder="Enter phone number"
                    onChange={formik.handleChange}
                    onBlur={formik.handleBlur}
                    value={formik.values.phone}
                    error={
                      formik.touched.phone && formik.errors.phone
                        ? { content: formik.errors.phone, pointing: 'below' }
                        : null
                    }
                  />
                </Form.Field>

                <Form.Field className={style.formField}>
                  <label>Address</label>
                  <Form.TextArea
                    id="address"
                    name="address"
                    placeholder="Enter address"
                    onChange={formik.handleChange}
                    onBlur={formik.handleBlur}
                    value={formik.values.address}
                    error={
                      formik.touched.address && formik.errors.address
                        ? { content: formik.errors.address, pointing: 'below' }
                        : null
                    }
                  />
                </Form.Field>

                <Form.Field className={style.formField}>
                  <label>Google Map Link</label>
                  <Form.Input
                    id="google_map_link"
                    name="google_map_link"
                    placeholder="Enter Google Map link"
                    onChange={formik.handleChange}
                    onBlur={formik.handleBlur}
                    value={formik.values.google_map_link}
                    error={
                      formik.touched.google_map_link && formik.errors.google_map_link
                        ? { content: formik.errors.google_map_link, pointing: 'below' }
                        : null
                    }
                  />
                </Form.Field>

                <Form.Field className={style.formField}>
                  <label>Business Superuser</label>
                  <Form.Select
                    id="business_superuser"
                    name="business_superuser"
                    placeholder="Select business superuser"
                    fluid
                    search
                    selection
                    className="customDropdown"
                    options={usersList}
                    onChange={(e, { value }) => formik.setFieldValue('business_superuser', value)}
                    onBlur={() => formik.setFieldTouched('business_superuser', true)}
                    value={formik.values.business_superuser}
                    error={
                      formik.touched.business_superuser && formik.errors.business_superuser
                        ? { content: formik.errors.business_superuser, pointing: 'below' }
                        : null
                    }
                  />
                </Form.Field>
              </div>
              <div className={style.submitBtn}>
                <Button type="submit" primary>
                  Submit
                </Button>
              </div>
            </Form>
          </div>
        </div>
      </div>
    </>
  );
};

export default BusinessCreation;

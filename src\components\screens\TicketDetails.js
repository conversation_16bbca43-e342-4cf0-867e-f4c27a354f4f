import React, { useEffect, useState, useRef, useCallback } from 'react';
import style from './scss/bussinessDetailed.module.scss';
import Header from '../global/Header';
import {
  backIcon,
  copyIcon,
  DropdownIcon,
  downloadIcon,
  orgIcon,
  threeDotIcon,
  closeIcon,
  removeUser,
  eye,
  closedEye,
  ticketLogIcon,
} from '../global/Icons';
import { Image, Dropdown, Table, Pagination, Popup, Button, Modal, Form } from 'semantic-ui-react';
import sampleImg from '../../assets/Images/sampleImg.png';
import { useFormik } from 'formik';
import GlobalService from '../services/GlobalServices';
import { resturls } from '../utils/apiurls';
import { useParams, useNavigate } from 'react-router-dom';
import infoIcon from '../../assets/Images/infoIcon.png';
import { toast } from 'react-toastify';

const TicketDetails = () => {
  const [activeMenu, setActiveMenu] = useState('detail');
  const [activeModal, setActiveModal] = useState();
  const [selectedUsers, setSelectedUsers] = useState([]);
  const [gstCopied, setGstCopied] = useState(false);
  const [emailCopied, setEmailCopied] = useState(false);
  const [filesList, setFilesList] = useState([]);
  const [FileCount, setFileCount] = useState(0);
  const [activePopup, setActivePopup] = useState(false);
  const [FetchUserList, setFetchUserList] = useState([]);
  const [page, setPage] = useState(1);
  const [togleEye, setToggleEye] = useState(true);
  const [downloadLink, setDownloadLink] = useState('');
  const [timeLineList, setTimeLineList] = useState();
  const [paginationInfo, setPaginationInfo] = useState();
  const [activePage, setActivePage] = useState(1);

  const observer = useRef();

  const TicketId = useParams();

  const [TicketData, setTicketData] = useState({});

  const copyToClipboard = (text, setCopiedState) => {
    // Check if the Clipboard API is available
    if (navigator.clipboard && window.isSecureContext) {
      navigator.clipboard
        .writeText(text)
        .then(() => {
          setCopiedState(true);
          setTimeout(() => setCopiedState(false), 2000);
        })
        .catch((err) => {
          console.error('Failed to copy via Clipboard API:', err);
        });
    } else {
      // Fallback for HTTP or older browsers
      const textArea = document.createElement('textarea');
      textArea.value = text;
      textArea.style.position = 'fixed'; // Prevent scrolling
      textArea.style.left = '-9999px'; // Hide element
      document.body.appendChild(textArea);
      textArea.focus();
      textArea.select();
      try {
        document.execCommand('copy');
        setCopiedState(true);
        setTimeout(() => setCopiedState(false), 2000);
      } catch (err) {
        console.error('Fallback: Unable to copy', err);
      }
      document.body.removeChild(textArea);
    }
  };

  const tickIcon = () => (
    <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        d="M13.3333 4L6 11.3333L2.66667 8"
        stroke="#10B981"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );

  const formatDate = (isoDateString) => {
    const date = new Date(isoDateString);
    return new Intl.DateTimeFormat('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    }).format(date);
  };

  const obtainDownloadLink = () => {
    GlobalService.generalSelect(
      (respdata) => {
        setDownloadLink(respdata?.download_link);
      },
      `${resturls.obtainTicketDownloadLink}?ticket_id=${TicketId.ticketId}`,
      'GET'
    );
  };

  const obtainTimelineList = () => {
    GlobalService.generalSelect(
      (respdata) => {
        const { results } = respdata;
        setTimeLineList(results);
        setPaginationInfo(respdata);
      },
      `${resturls.obtainTimelineList}?search=${TicketId.ticketId}`,
      'GET'
    );
  };

  useEffect(() => {
    obtainDownloadLink();
  }, [TicketId.ticketId]);

  const FetchFilesSpecificBuisness = () => {
    GlobalService.generalSelect(
      (respdata) => {
        setFilesList(respdata.results);
        setFileCount(respdata.count);
      },
      `${resturls.file}?ticket_id=${TicketId.ticketId}`,
      'GET'
    );
  };

  const fetchallbuisnessUsers = () => {
    GlobalService.generalSelect(
      (respdata) => {
        setFetchUserList(respdata.data);
        // setMemberCount(respdata.count);
      },
      `${resturls.obtainCategoryWiseUser}?user_type=business_users`,
      {},
      'GET'
    );
  };

  const deleteBuisness = () => {
    GlobalService.generalSelect(
      (respdata) => {
        setTicketData(respdata);
        navigate('/businessList');
      },
      `${resturls.createBusiness}${TicketId.TicketId}/`,
      {},
      'DELETE'
    );
  };

  useEffect(() => {
    if (activeMenu === 'files') {
      FetchFilesSpecificBuisness();
    }
    if (activeMenu === 'member') {
      setFilesList([]);
      obtainTimelineList();
    }
  }, [activeMenu]);

  useEffect(() => {
    if (!TicketId?.ticketId) return;
    fetchTicketData(TicketId?.ticketId);
    FetchFilesSpecificBuisness();
  }, [TicketId?.TicketId]);

  const fetchTicketData = (id) => {
    GlobalService.generalSelect(
      (respdata) => {
        setTicketData(respdata);
        setToggleEye(respdata?.is_active);
      },
      `${resturls.ticketList}${TicketId.ticketId}/`,
      {},
      'GET'
    );
  };

  useEffect(() => {
    fetchallbuisnessUsers();
  }, [TicketData]);

  const getStatusClassNew = (status) => {
    switch (status) {
      case 'Open':
        return style.openStatus; // Class for open status
      case 'Pending':
        return style.pendingStatus; // Class for pending status
      case 'Closed':
        return style.closedStatus; // Class for closed status
      case 'Verified':
        return style.verifiedStatus; // Class for verified status
      case 'Deleted':
        return style.deletedStatus; // Class for deleted status
      default:
        return ''; // No status class if undefined or empty
    }
  };

  const options = FetchUserList.map((user) => ({
    key: user.full_name,
    value: user.user_id,
    text: user.full_name,
  }));

  const navigate = useNavigate();

  const goBack = () => {
    navigate(-1);
  };

  const handleChange = (e, { value }) => {
    setSelectedUsers(value);
    formik.setFieldValue('users', value);
  };
  const formik = useFormik({
    initialValues: {
      server: '',
      email: '',
      password: '',
    },
    // validationSchema,
    onSubmit: (values) => {
      GlobalService.generalSelect(
        (respdata) => {
          // navigate('/')
        },
        `${resturls.createBusiness}`,
        values,
        'POST'
      );
    },
  });

  const TicketDetail = {
    logo: null,
    name: TicketData.subject,
    status: TicketData.status,
    isActive: TicketData?.is_active,
    id: TicketData?.id,
  };

  const renderMenu = () => {
    const list = [
      { name: 'Details', key: 'detail' },
      { name: 'Timeline ', key: 'member', count: paginationInfo?.count },
      { name: 'Files ', key: 'files', count: FileCount },
    ];
    return list?.map((info) => {
      return (
        <div
          onClickCapture={() => setActiveMenu(info?.key)}
          className={`${info?.key === activeMenu ? style.activeMenu : ''} ${style.menuItem}`}
        >
          {info?.name}
          {info?.count && <span>{info?.count}</span>}
        </div>
      );
    });
  };

  const handleDeleteFile = (info) => {
    GlobalService.generalSelect(
      (respdata) => {
        FetchFilesSpecificBuisness();
      },
      `${resturls.uploadFile}${info?.value}/`,
      {},
      'DELETE'
    );
  };

  const renderPopupContent = (info) => {
    const list = [
      {
        name: 'Remove file',
        icon: removeUser(),
        key: 'removeUser',
        value: info?.id,
      },
    ];
    return (
      <div className={style.popupList}>
        {list?.map((item) => {
          return (
            <div
              key={item.key}
              className={style.popupItem}
              style={{ display: 'flex', gap: '1em', cursor: 'pointer' }}
              onClickCapture={() => handleDeleteFile(item)}
            >
              {item?.icon} <p>{item?.name}</p>
            </div>
          );
        })}
      </div>
    );
  };

  const handleHideComment = (info) => {
    const id = info?.commentId;
    const obj = {
      is_active: info?.key,
    };
    GlobalService.generalSelect(
      (respdata) => {
        if (info?.key === 'True') {
          toast.success(`Comment with id : ${id}  hidden successfully`);
        } else {
          toast.success(`Comment with id : ${id}  unhidden successfully`);
        }
        window.location.reload();
      },
      `${resturls.addComment}${id}/`,
      obj,
      'PATCH'
    );
  };

  const renderTimelinePopupContent = (info) => {
    const text = info?.details;
    const actionType = info?.action_type;

    // Match the comment ID if it exists in the text
    const match = text?.match(/Comment '(\d+)'/);
    let commentId;
    if (match) {
      commentId = match[1];
    }

    // Regex to check if actionType contains the word "comment" (case insensitive)
    const isCommentAction = /comment/i.test(actionType);

    // Check if "is_active: 'True' to 'False'" is present in the text
    const isHidden = text?.includes("is_active: 'True' to 'False'");

    const list = [];

    if (isCommentAction) {
      // Proceed only if actionType contains "comment"

      // If actionType is a comment-related action (e.g., "Comment updation", "comment addition")
      if (commentId) {
        if (isHidden) {
          const unHide = {
            name: 'Unhide comment',
            icon: closedEye(),
            key: true,
            value: info?.id,
            commentId,
          };
          list.push(unHide);
        } else {
          const hide = {
            name: 'Hide comment',
            icon: eye(),
            key: false,
            value: info?.id,
            commentId,
          };
          list.push(hide);
        }
      }
    }

    // Print the list

    return (
      <div className={style.popupList}>
        {list?.map((item) => {
          return (
            <div
              key={item.key}
              className={`${style.popupItem} ${style.hideItems}`}
              style={{ display: 'flex', gap: '1em', cursor: 'pointer' }}
              onClickCapture={() => handleHideComment(item)}
            >
              {item?.icon} <p>{item?.name}</p>
            </div>
          );
        })}
      </div>
    );
  };

  const details = {
    gstNumber: TicketData.gst,
    email: TicketData.email,
    associatedSP: null,
    createdOn: TicketData.created_at,
    updatedOn: TicketData.updated_at,
    address: TicketData.address,
    accountant: TicketData.accountant?.user_full_name || TicketData.business?.accountant?.full_name,
    manager: TicketData.manager?.user_full_name,
    business_type: TicketData.business_type?.name,
    supportEmail: TicketData.email,
    id: TicketData.id,
    category: TicketData.category?.name,
    subCategory: TicketData.sub_category?.name,
    priority: TicketData.priority,
    Organization: TicketData.business?.business_name,
    CreatedBy: TicketData.created_by,
    EmailCreation: TicketData.from_email,
    account_manager: TicketData.business?.account_manager?.full_name,
  };

  const renderDetails = () => {
    return (
      <>
        <div className={style.detailPart}>
          <p className={style.heading}>Ticket Details</p>
          <div className={style.detail}>
            <div>
              <label>Ticket ID</label>
              <p>
                {details?.id}
                <span onClick={() => copyToClipboard(details?.id, setGstCopied)} style={{ cursor: 'pointer' }}>
                  {gstCopied ? tickIcon() : copyIcon()}
                </span>
              </p>
            </div>
            <div>
              <label>Category</label>
              <p>
                {details?.category ? (
                  <>
                    {details.category}
                    <span
                      onClick={() => copyToClipboard(details.category, setEmailCopied)}
                      style={{ cursor: 'pointer' }}
                    >
                      {emailCopied ? tickIcon() : copyIcon()}
                    </span>
                  </>
                ) : (
                  '-'
                )}
              </p>
            </div>
            <div>
              <label>Sub category</label>
              <p>{details?.subCategory || '-'}</p>
            </div>

            <div>
              <label>Priority</label>
              <p>{details?.priority}</p>
            </div>
            <div>
              <label>Created On</label>
              <p>{details?.createdOn && formatDate(details?.createdOn)}</p>
            </div>

            <div>
              <label>Created By</label>
              <p>{details?.CreatedBy ? details.CreatedBy : details?.EmailCreation ? 'Email forwarding' : '-'}</p>
            </div>

            <div>
              <label>Last Updated On</label>
              <p>{details?.updatedOn && formatDate(details?.updatedOn)}</p>
            </div>

            <div>
              <label>Account Manager</label>
              <p>{details?.account_manager || '-'}</p>
            </div>
          </div>
        </div>
        <div className={style.detailPart}>
          <p className={style.heading}>Organization</p>
          <div className={style.detail}>
            <div>
              <label>Organization</label>
              <p>{details?.Organization}</p>
            </div>
            <div>
              <label> Manager</label>
              <p>{details?.manager || '-'}</p>
            </div>
            <div>
              <label>Accountant (Default)</label>
              <p>{details?.accountant || '-'}</p>
            </div>
          </div>
        </div>
      </>
    );
  };

  const renderTimeLineList = () => {
    const itemsPerPage = 10;
    const totalPages = Math.ceil(paginationInfo?.count / itemsPerPage);
    const handlePaginationChange = (e, { activePage }) => {
      setActivePage(activePage);
      const endpoint = `${resturls.obtainTimelineList}?page=${activePage}&search=${TicketData?.id}`;
      GlobalService.generalSelect(
        (respdata) => {
          if (respdata && respdata.results) {
            setTimeLineList(respdata.results);
            setPaginationInfo(respdata);
          } else {
            console.warn('No results found in response:', respdata);
            setTimeLineList([]);
          }
        },
        endpoint,
        {},
        'GET'
      );
    };

    if (timeLineList?.length === 0) {
      return (
        <div className={style.emptyTable}>
          No Timeline is there : <span>{TicketDetail?.name}</span>
        </div>
      );
    }
    return (
      <>
        <Table basic="very" className={style.timelineTable}>
          <Table.Header>
            <Table.Row>
              <Table.HeaderCell className={style.subjectheaderRow}>Timestamp</Table.HeaderCell>
              <Table.HeaderCell>Type</Table.HeaderCell>
              <Table.HeaderCell>Sender/Initiator</Table.HeaderCell>
              <Table.HeaderCell>Status</Table.HeaderCell>
              <Table.HeaderCell>Details</Table.HeaderCell>
              <Table.HeaderCell></Table.HeaderCell>
            </Table.Row>
          </Table.Header>

          <Table.Body>
            {timeLineList?.map((data, index) => (
              <Table.Row key={data.id} ref={timeLineList.length === index + 1 ? lastElementRef : null}>
                <Table.Cell>{formatDate(data?.created_at)}</Table.Cell>
                <Table.Cell>{data?.action_type}</Table.Cell>
                <Table.Cell>{data?.user?.full_name || '-'}</Table.Cell>
                <Table.Cell>
                  <span
                    className={`${style.timelineStatus} ${getStatusClassNew(
                      data?.is_active ? data?.status : 'Closed'
                    )}`}
                  >
                    {data?.is_active ? data?.status : 'Hidden'}
                  </span>
                </Table.Cell>
                <Table.Cell>{data?.details}</Table.Cell>
                <Table.Cell>
                  <div className={style.dotContainer}>
                    {/* Directly check if the action_type contains 'comment' */}
                    {/comment/i.test(data?.action_type) && (
                      <Popup
                        content={renderTimelinePopupContent(data)}
                        on="click"
                        pinned
                        position="bottom right"
                        trigger={threeDotIcon()}
                        onOpen={() => {
                          setActivePopup(data?.id);
                        }}
                        onClose={() => setActivePopup(false)}
                        className={style.popup}
                        open={activePopup === data?.id}
                      />
                    )}
                  </div>
                </Table.Cell>
              </Table.Row>
            ))}
          </Table.Body>
        </Table>
        <div className={style.paginationWrapper}>
          {paginationInfo?.count > 10 && timeLineList?.length > 0 && (
            <Pagination activePage={activePage} totalPages={totalPages} onPageChange={handlePaginationChange} />
          )}
        </div>
      </>
    );
  };
  const extractFileName = (url) => {
    return url.split('/').pop();
  };

  const renderFilesList = () => {
    return filesList?.length > 0 ? (
      <Table basic="very">
        <Table.Header>
          <Table.Row>
            <Table.HeaderCell className={style.subjectheaderRow}>File Name</Table.HeaderCell>
            <Table.HeaderCell>Ticket ID</Table.HeaderCell>
            <Table.HeaderCell>Category</Table.HeaderCell>
            <Table.HeaderCell>Uploaded by</Table.HeaderCell>
            <Table.HeaderCell>Uploaded on</Table.HeaderCell>
            <Table.HeaderCell></Table.HeaderCell>
          </Table.Row>
        </Table.Header>
        <Table.Body>
          {filesList.map((data) => (
            <Table.Row key={data.id}>
              <Table.Cell className={style.subjectRow}>
                <div className={style.fileWrapper}>
                  {(() => {
                    const fileName = extractFileName(data?.file);
                    const fileExt = fileName.split('.').pop()?.toLowerCase();
                    const imageExtensions = ['png', 'jpg', 'jpeg', 'gif', 'svg', 'webp', 'bmp'];

                    // SVG Icons
                    const getSvgIcon = (type) => {
                      switch (type) {
                        case 'pdf':
                          return (
                            <svg
                              xmlns="http://www.w3.org/2000/svg"
                              width="50"
                              height="50"
                              viewBox="0 0 24 24"
                              fill="none"
                              stroke="red"
                              strokeWidth="1.5"
                              className="text-red-500"
                            >
                              <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
                              <polyline points="14 2 14 8 20 8"></polyline>
                              <line x1="12" y1="10" x2="12" y2="16"></line>
                              <line x1="9" y1="13" x2="15" y2="13"></line>
                            </svg>
                          );
                        case 'xlsx':
                        case 'xls':
                          return (
                            <svg
                              xmlns="http://www.w3.org/2000/svg"
                              width="50"
                              height="50"
                              viewBox="0 0 24 24"
                              fill="none"
                              stroke="green"
                              strokeWidth="1.5"
                              className="text-green-500"
                            >
                              <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
                              <polyline points="14 2 14 8 20 8"></polyline>
                              <path d="M10.5 15.5L8 12.5l2.5-3" />
                              <path d="M16 12.5h-6" />
                            </svg>
                          );
                        case 'doc':
                        case 'docx':
                          return (
                            <svg
                              xmlns="http://www.w3.org/2000/svg"
                              width="50"
                              height="50"
                              viewBox="0 0 24 24"
                              fill="none"
                              stroke="blue"
                              strokeWidth="1.5"
                              className="text-blue-500"
                            >
                              <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
                              <polyline points="14 2 14 8 20 8"></polyline>
                              <line x1="9" y1="14" x2="15" y2="14"></line>
                              <line x1="9" y1="17" x2="15" y2="17"></line>
                            </svg>
                          );
                        default:
                          return (
                            <svg
                              xmlns="http://www.w3.org/2000/svg"
                              width="50"
                              height="50"
                              viewBox="0 0 24 24"
                              fill="none"
                              stroke="gray"
                              strokeWidth="1.5"
                              className="text-gray-500"
                            >
                              <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
                              <polyline points="14 2 14 8 20 8"></polyline>
                              <line x1="12" y1="12" x2="12" y2="16"></line>
                              <line x1="12" y1="16" x2="12.01" y2="16"></line>
                            </svg>
                          );
                      }
                    };

                    if (imageExtensions.includes(fileExt)) {
                      return (
                        <div className={style.fileWrapper}>
                          <Image
                            className="preview_files"
                            src={data?.file || sampleImg}
                            alt={fileName}
                            width={50}
                            height={50}
                            style={{ objectFit: 'cover' }}
                          />
                          <a
                            href={data?.file}
                            target="_blank"
                            rel="noopener noreferrer"
                            download={fileName}
                            className={style.fileName}
                          >
                            {fileName}
                          </a>
                        </div>
                      );
                    }

                    return (
                      <div className={style.fileWrapper}>
                        {getSvgIcon(fileExt)}
                        <a
                          href={data?.file}
                          target="_blank"
                          rel="noopener noreferrer"
                          download={fileName}
                          className={style.fileName}
                        >
                          {fileName}
                        </a>
                      </div>
                    );
                  })()}
                </div>
              </Table.Cell>
              <Table.Cell className="centerAlign">
                <p className={style.id}>{data?.ticket_id}</p>
              </Table.Cell>
              <Table.Cell>
                <p className={data?.ticket_category ? style.category : ''}>
                  {data?.ticket_category ? data?.ticket_category : <center>-</center>}
                </p>
              </Table.Cell>
              <Table.Cell>
                <p className={style.uploadInfo}>{data?.uploaded_by?.full_name}</p>
                <p className={style.uploadRole}>{data?.uploaded_by?.role}</p>
              </Table.Cell>
              <Table.Cell>
                <p>{formatDate(data?.created_at)}</p>
              </Table.Cell>
              <Table.Cell>
                <div className={style.dotContainer}>
                  <Popup
                    content={renderPopupContent(data)}
                    on="click"
                    pinned
                    position="bottom right"
                    trigger={threeDotIcon()}
                    onOpen={() => setActivePopup(data?.id)}
                    onClose={() => setActivePopup(false)}
                    className={style.popup}
                    open={activePopup === data?.id}
                  />
                </div>
              </Table.Cell>
            </Table.Row>
          ))}
        </Table.Body>
      </Table>
    ) : (
      <div
        style={{
          fontSize: '18px',
          color: '#999',
          textAlign: 'center',
          marginTop: '20px',
          padding: '20px',
          borderRadius: '25px',
          maxWidth: '500px',
          margin: '20px auto',
        }}
      >
        No files in this Ticket: <strong style={{ color: '#333', fontWeight: 'bold' }}>{TicketDetail?.name}</strong>
      </div>
    );
  };

  const renderContent = () => {
    if (activeMenu === 'member') {
      return (
        <div className={style.memebersList}>
          <div className={style.inputWrapper}>
            {timeLineList?.length > 0 && (
              <a href={downloadLink}>
                <div className={style.downloadBtn2}>Download {downloadIcon()}</div>
              </a>
            )}
          </div>
          <div className={style.tableWrapper}>{renderTimeLineList()}</div>
        </div>
      );
    }
    if (activeMenu === 'files') {
      return (
        <div className={style.memebersList}>
          <div className={style.tableWrapper}>{renderFilesList()}</div>
        </div>
      );
    }
    return <div className={style.detailView}>{renderDetails()}</div>;
  };

  const renderReconfigForm = () => (
    <div className={style.formContainer}>
      <Form.Field className={style.formField}>
        <label>Server</label>
        <Form.Input
          id="server"
          name="server"
          placeholder="Enter server address"
          onChange={formik.handleChange}
          onBlur={formik.handleBlur}
          value={formik.values.server}
          error={
            formik.touched.server && formik.errors.server ? { content: formik.errors.server, pointing: 'below' } : null
          }
        />
      </Form.Field>

      <Form.Field className={style.formField}>
        <label>Email</label>
        <Form.Input
          id="email"
          name="email"
          placeholder="Enter email"
          onChange={formik.handleChange}
          onBlur={formik.handleBlur}
          value={formik.values.email}
          error={
            formik.touched.email && formik.errors.email ? { content: formik.errors.email, pointing: 'below' } : null
          }
        />
      </Form.Field>

      <Form.Field className={style.formField}>
        <label>Password</label>
        <Form.Input
          id="password"
          name="password"
          placeholder="Enter Password"
          onChange={formik.handleChange}
          onBlur={formik.handleBlur}
          value={formik.values.password}
          type="password"
          error={
            formik.touched.password && formik.errors.password
              ? { content: formik.errors.password, pointing: 'below' }
              : null
          }
        />
      </Form.Field>
    </div>
  );

  const renderMemberAddForm = () => (
    <div className={style.formContainer}>
      <Form.Field className={style.formField}>
        <label>Users</label>
        <Dropdown
          id="users"
          name="users"
          placeholder="Select users"
          className="customDropdown4"
          fluid
          multiple
          search
          selection
          icon={<DropdownIcon />}
          options={options}
          onChange={handleChange}
          onBlur={formik.handleBlur}
          renderLabel={() => null}
          error={
            formik.touched.users && formik.errors.users ? { content: formik.errors.users, pointing: 'below' } : null
          }
        />
      </Form.Field>
      <div className={style.selctedUsers}>
        {selectedUsers?.map((userId) => {
          const user = FetchUserList.find((u) => u.user_id === userId); // Find user details from users array
          return (
            <p key={userId} className={style.user}>
              {user?.full_name}
              {closeIcon()}
            </p>
          );
        })}
      </div>
    </div>
  );

  const renderButtons = () => {
    return (
      <div className={style.buttonWrapper}>
        <Button type="button" className={style.cancel} onClick={() => setActiveModal(false)}>
          Cancel
        </Button>
        <Button type="submit" className={style.nextBtn}>
          Save Details
        </Button>
      </div>
    );
  };

  const AddUser = () => {
    const payload = { add_business_users: selectedUsers };
    GlobalService.generalSelect(
      (respdata) => {
        setActiveModal(false);
      },
      `${resturls.createBusiness}${TicketId.TicketId}/`,
      payload,
      'PATCH'
    );
  };

  const lastElementRef = useCallback((node) => {
    if (observer.current) observer.current.disconnect();

    observer.current = new IntersectionObserver((entries) => {
      if (entries[0].isIntersecting) {
        setPage((prevPage) => prevPage + 1);
      }
    });

    if (node) observer.current.observe(node);
  }, []);

  const renderModalContent = () => {
    if (activeModal === 'reconfig') {
      return (
        <div className={style.modalContentdelete}>
          <h5>Reconfigure</h5>
          <Form className={style.formWrapper} onSubmit={formik.handleSubmit}>
            {renderReconfigForm()}
            {renderButtons()}
          </Form>
        </div>
      );
    }

    if (activeModal === 'addMember') {
      return (
        <div className={`${style.memberModal} ${style.modalContentdelete}`}>
          <h5>Add User</h5>
          <Form className={style.formWrapper} onSubmit={() => AddUser()}>
            {renderMemberAddForm()}
            {renderButtons()}
          </Form>
        </div>
      );
    }

    if (activeModal === 'deleteOrganisation') {
      return (
        <div className={`${style.autoHeight} ${style.modalContentdelete}`}>
          <div className={style.imgWrapper}>
            <Image src={infoIcon} />
          </div>
          <div className={style.descContent}>
            <h5>Are you sure you want to delete Buisness: {TicketData?.name}</h5>
            <p>.</p>
          </div>
          <div className={style.buttonWrapper}>
            <Button type="button" className={style.cancel} onClick={() => setActiveModal(false)}>
              Cancel
            </Button>
            <Button type="submit" className={style.redBtn} onClick={() => deleteBuisness()}>
              {' '}
              Delete
            </Button>
          </div>
        </div>
      );
    }
  };

  const handleUsereactive = (id, active) => {
    const payload = {
      is_active: !togleEye,
    };
    GlobalService.generalSelect(
      (respdata) => {
        setTicketData(respdata);
        setToggleEye(respdata?.is_active);
      },
      `${resturls.ticketList}${id}/`,
      payload,
      'PATCH'
    );
  };

  return (
    <>
      <Header />
      <div className={style.businessDetails}>
        <div className={style.backIconWrapper} onClick={goBack}>
          {backIcon()}
        </div>
        <div className={style.headerContainer}>
          <div className={style.ticketInfoHeader}>
            <div className={style.icon}>{ticketLogIcon()}</div>
            <div>
              <h5>{TicketDetail?.name}</h5>
              <span
                className={`${style.dropdown} ${
                  style.statusDropdown
                } custom-dropdown2 ${getStatusClassNew(TicketDetail?.status)}`}
              >
                {TicketDetail?.status}
              </span>
            </div>
          </div>
          <div className={style.rightContent}>
            <div className={style.eyeBtn} onClick={() => handleUsereactive(TicketDetail?.id, TicketDetail?.isActive)}>
              {togleEye ? eye() : closedEye()}
              <span>{togleEye ? 'Hide Ticket' : 'Unhide Ticket'}</span>
            </div>
          </div>
        </div>

        <div className={style.contentWrapper}>
          <div className={style.menuList}>{renderMenu()}</div>
          {renderContent()}
        </div>
      </div>
      <>
        <Modal basic size="small" open={activeModal} onClose={() => setActiveModal(false)}>
          {renderModalContent()}
        </Modal>
      </>
    </>
  );
};

export default TicketDetails;

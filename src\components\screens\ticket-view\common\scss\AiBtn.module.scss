@import '../../../../../assets/scss/main.scss';

.extractionBtn {
  max-height: 2.5em;
  min-width: 12em;
  background-color: $accentColor2;
  color: $primaryBgColor;
  font-weight: $bold;
  padding: 0.5rem 1rem;
  border-radius: 9999px;
  transition: background-color 0.2s;
  cursor: pointer;
  border: 1px solid $accentBorder2;
  outline: none;
  font-size: 0.9em;
  white-space: nowrap;
  display: flex;
  justify-content: center;
  align-items: center;
  overflow: hidden;

  &:hover:not(:disabled) {
    background-color: $accentHover2;
  }

  &:focus-visible {
    outline: 2px solid $blueBorder;
    outline-offset: 2px;
  }

  &:disabled {
    cursor: not-allowed;
    opacity: 0.7 !important;
  }
}

import React, { useCallback, useMemo, useState } from 'react';
import <PERSON>Field from './components/AiField';
import { getFieldAlertObject, getSuggestion, recommendedOptionsMapper } from '../../../../components/utils/aiUtils';
import ModalDropdown from './components/ModalDropdown';
import { useParams } from 'react-router-dom';
import AddBusinessButton from './components/AddBusinessButton';
import useRealTimeValidation from './hook/useRealTimeValidation';
import { resturls } from '../../../utils/apiurls';
import AddSupplierForm from './components/AddSupplierForm';

const SECTION = 'supplier_details';

function SupplierDetailsSection({ formData, isReadOnly, invoiceType, formAction, setFormData }) {
  const { businessId } = useParams();
  const data = formData[SECTION];
  const isGSTEmpty = useMemo(() => !data?.supplier_gst_no, [data]);
  const isZoho = useMemo(() => formData?.accounting_platform?.toLowerCase() === 'zoho', []);
  const [isAddSupplierModalOpen, setIsAddSupplierModalOpen] = useState(false);
  const [handleValidate, suggestions] = useRealTimeValidation({
    data,
    formAction,
    defaultSuggestion: getSuggestion(data?.recommended_fields),
    isInventoryEnabled: formData?.is_inventory_enabled,
  });

  const isShowSuggestedDropDown = useMemo(() => {
    const recommendedField = data?.recommended_fields?.[0] || {};
    return 'similarity_score' in recommendedField;
  }, [data]);

  const handleDropDownUpdate = useCallback(
    (option) => {
      const latestSection = formAction('UPDATE_SECTION', SECTION, null, {
        supplier_name: option.label,
        supplier_id: String(option.key),
      });
      handleValidate(SECTION, invoiceType, latestSection);
    },
    [data]
  );

  const handleOnPaste = useCallback((copyText, field) => {
    setFormData((prevData) => {
      const prevSection = prevData[SECTION] || {};
      const updatedSection = { ...prevSection, [field]: copyText };
      handleValidate(SECTION, invoiceType, updatedSection);
      return {
        ...prevData,
        [SECTION]: updatedSection,
      };
    });
  }, []);

  return (
    <div className="form-grid">
      {/* GST No. */}
      <div className="only-1-column w-full">
        <AiField
          label="GST No."
          isExactMatch={data?.exact_match?.supplier_gst_no}
          className="flex-grow"
          type="text"
          placeholder="No GST Found"
          name="supplier_gst_no"
          id="supplier_gst_no"
          value={data?.supplier_gst_no ?? ''}
          onBlur={() => handleValidate(SECTION, invoiceType)}
          copyText={suggestions?.supplier_gst_no}
          onPaste={handleOnPaste}
          disabled={isReadOnly}
          alertObject={getFieldAlertObject(data, 'supplier_gst_no')}
          onChange={(e) => {
            formAction('FIELD_CHANGE', SECTION, 'supplier_gst_no', e.target.value);
          }}
        />

        {data?.error?.supplier_gst_no?.short_message?.trim().toLowerCase() === 'supplier gst number not found' && (
          <AddBusinessButton formData={formData} />
        )}
      </div>
      {/* Name */}
      <div className="only-1-column">
        <AiField
          label="Name"
          isExactMatch={data?.exact_match?.supplier_name}
          required
          className=""
          copyText={suggestions?.supplier_name}
          value={data?.supplier_name ?? ''}
          placeholder="Select or add a supplier"
          disabled={isReadOnly}
          name="supplier_name"
          id="supplier_name"
          alertObject={getFieldAlertObject(data, 'supplier_name')}
          onBlur={() => handleValidate(SECTION, invoiceType)}
          onPaste={handleOnPaste}
          onChange={(e) => {
            formAction('FIELD_CHANGE', SECTION, 'supplier_name', e.target.value);
          }}
        />
        {isGSTEmpty &&
          (isShowSuggestedDropDown ? (
            <ModalDropdown
              label="Select Supplier"
              options={recommendedOptionsMapper(data, 'supplier_name')}
              onSelect={handleDropDownUpdate}
              disabled={isReadOnly}
              showMatchesFound={true}
              url={`${resturls.getPurchaseLedger}/search?business_id=${businessId}&ledger_type=vendor`}
              searchParamName="ledger_name"
              transformOptionsObj={{
                key: 'master_id',
                label: 'ledger_name',
                value: 'master_id',
              }}
              onAdd={() => setIsAddSupplierModalOpen(true)}
              isShowRefreshButton={isZoho}
            />
          ) : (
            <ModalDropdown
              label="View all Supplier"
              onSelect={handleDropDownUpdate}
              disabled={isReadOnly}
              url={`${resturls.getPurchaseLedger}/search?business_id=${businessId}&ledger_type=vendor`}
              searchParamName="ledger_name"
              transformOptionsObj={{
                key: 'master_id',
                label: 'ledger_name',
                value: 'master_id',
              }}
              onAdd={() => setIsAddSupplierModalOpen(true)}
              isShowRefreshButton={isZoho}
            />
          ))}
      </div>
      {/* Address */}
      <AiField
        label="Address"
        isExactMatch={data?.exact_match?.supplier_address}
        alertObject={getFieldAlertObject(data, 'supplier_address')}
        className="only-1-column"
        copyText={suggestions?.supplier_address}
        Element="textarea"
        value={data?.supplier_address ?? ''}
        disabled={isReadOnly}
        name="supplier_address"
        id="supplier_address"
        onBlur={() => handleValidate(SECTION, invoiceType)}
        onPaste={handleOnPaste}
        onChange={(e) => {
          formAction('FIELD_CHANGE', SECTION, 'supplier_address', e.target.value);
        }}
      />
      {/* Three fields in one row, State Name, State Code, PAN No. */}
      <div className="only-3-column">
        {/* State Name */}
        <AiField
          label="State Name"
          isExactMatch={data?.exact_match?.supplier_state_name}
          copyText={suggestions?.supplier_state_name}
          type="text"
          value={data?.supplier_state_name ?? ''}
          disabled={isReadOnly}
          name="supplier_state_name"
          id="supplier_state_name"
          onBlur={() => handleValidate(SECTION, invoiceType)}
          alertObject={getFieldAlertObject(data, 'supplier_state_name')}
          onPaste={handleOnPaste}
          onChange={(e) => {
            formAction('FIELD_CHANGE', SECTION, 'supplier_state_name', e.target.value);
          }}
        />

        {/* State Code */}
        <AiField
          label="State Code"
          isExactMatch={data?.exact_match?.supplier_state_code}
          copyText={suggestions?.supplier_state_code}
          type="text"
          value={data?.supplier_state_code ?? ''}
          disabled={isReadOnly}
          name="supplier_state_code"
          id="supplier_state_code"
          onBlur={() => handleValidate(SECTION, invoiceType)}
          alertObject={getFieldAlertObject(data, 'supplier_state_code')}
          onPaste={handleOnPaste}
          onChange={(e) => {
            formAction('FIELD_CHANGE', SECTION, 'supplier_state_code', e.target.value);
          }}
        />

        {/* PAN No. */}
        <AiField
          label="Supplier PAN"
          isExactMatch={data?.exact_match?.supplier_pan_no}
          copyText={suggestions?.supplier_pan_no}
          type="text"
          value={data?.supplier_pan_no ?? ''}
          disabled={isReadOnly}
          name="supplier_pan_no"
          id="supplier_pan_no"
          onBlur={() => handleValidate(SECTION, invoiceType)}
          alertObject={getFieldAlertObject(data, 'supplier_pan_no')}
          onPaste={handleOnPaste}
          onChange={(e) => {
            formAction('FIELD_CHANGE', SECTION, 'supplier_pan_no', e.target.value);
          }}
        />
      </div>
      {formData?.accounting_platform?.toLowerCase() === 'zoho' && (
        <AddSupplierForm
          formData={formData}
          open={isAddSupplierModalOpen}
          onClose={() => setIsAddSupplierModalOpen(false)}
        />
      )}
    </div>
  );
}

export default SupplierDetailsSection;

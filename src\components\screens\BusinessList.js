import React, { useEffect, useState, useCallback, useRef } from 'react';
import { useNavigate } from 'react-router-dom';
import style from './scss/businessList.module.scss';
import NavigationBar from './NavigationBar';
import Header from '../global/Header';
import { Image, Table, Card, Pagination, Input, Popup, Dropdown, Modal, Form, Button, Loader } from 'semantic-ui-react';
import GlobalService from '../services/GlobalServices';
import { resturls } from '../utils/apiurls';
import { mediaBreakpoint } from '../global/MediaBreakPointes';
import { DropdownIcon, mailIcon, phoneIcon } from '../global/Icons';
import { useFormik } from 'formik';
import * as Yup from 'yup';
import debounce from 'lodash/debounce';

const BusinessList = () => {
  const [businessList, setBusinessList] = useState();
  const [activeModal, setActiveModal] = useState();
  const [BuisnessSuperUserList, setBuisnessSuperUserList] = useState([]);
  const [BuisnesCount, setBuisnessCount] = useState(0);
  const [searchQuery, setSearchQuery] = useState('');
  const [step, setStep] = useState({
    step1: true,
    step2: false,
    step3: false,
    active: 'step1',
  });
  const [businessId, setBusinessId] = useState(null);
  const [paginationInfo, setPaginationInfo] = useState();
  const [activePage, setActivePage] = useState(1);
  const [isLoading, setIsLoading] = useState(1);
  const [dropdownItems, setDropdownItems] = useState([]);
  const [nextPageUrl, setNextPageUrl] = useState(null);
  const [isFetching, setIsFetching] = useState(false);
  const dropdownRef = useRef(null);

  const isResponsive = mediaBreakpoint?.mobile > window.innerWidth;
  const navigate = useNavigate();

  const OrganisationSearch = (isActiveStatus) => {
    setIsLoading(true);
    GlobalService.generalSelect(
      (respdata) => {
        const { results } = respdata;
        setBusinessList([]);
        setBuisnessCount(respdata.count); // Update the business count
        setBusinessList(results); // Update the business list
        setPaginationInfo(respdata);
        setIsLoading(false);
      },
      `${resturls.getBusinesses}?business_name=${isActiveStatus}`,
      {},
      'GET'
    );
  };
  const handleSearch = useCallback(
    debounce((query) => {
      OrganisationSearch(query);
    }, 500),
    []
  );

  const handleInputChange = (e) => {
    const query = e.target.value;
    setSearchQuery(query);
    handleSearch(query);
  };

  useEffect(() => {
    setIsLoading(true);
    GlobalService.generalSelect(
      (respdata) => {
        const { results } = respdata;
        setBuisnessCount(respdata.count);
        setBusinessList(results);
        setPaginationInfo(respdata);
        setIsLoading(false);
      },
      `${resturls.getBusinesses}`,
      {},
      'GET'
    );
  }, []);

  useEffect(() => {
    GlobalService.generalSelect(
      (respdata) => {
        setBuisnessSuperUserList(respdata.data);
      },
      `${resturls.obtainCategoryWiseUser}?user_type=business_superusers`,
      {},
      'GET'
    );
  }, []);

  const validationSchema = Yup.object({
    name: Yup.string().required('Name is required').max(50, 'Name must be 50 characters or less'),

    gst: Yup.string()
      .required('GST number is required')
      .matches(/^[A-Za-z0-9]{15}$/, 'GST number must be exactly 15 characters'),

    email: Yup.string().required('Email is required').email('Invalid email address'),

    phone: Yup.string()
      .required('Phone number is required')
      .matches(
        /^\+?[1-9]\d{1,3}[\s-]?\(?\d{1,4}?\)?[\s-]?\d{1,4}[\s-]?\d{1,4}[\s-]?\d{1,4}$/,
        'Enter a valid phone number'
      )
      .test('is-valid-phone', 'Phone number must be valid', (value) => {
        const phoneNumber = value; // Remove non-digit and non-plus characters

        // Case 1: If the number has a country code (+91) and 12 digits total
        if (phoneNumber.startsWith('+') && phoneNumber.length === 13) {
          // Ensure the number starts with a valid digit (6-9) after the country code
          return /^[6-9]/.test(phoneNumber.slice(3));
        }

        // Case 2: If it's a 10-digit number starting with a valid digit (6-9)
        return phoneNumber.length === 10 && /^[6-9]/.test(phoneNumber);
      }),

    address: Yup.string().required('Address is required').max(200, 'Address must be 200 characters or less'),

    business_type: Yup.string().required('Business type is required'),

    primary_contact_name: Yup.string().required('Primary contact is required'),
    business_superuser: Yup.string().required('Business superuser is required'),
    server_name: Yup.string().required('Server name is required'),
    email_host: Yup.string()
      .required('Email host is required')
      .matches(/^smtp\.[a-zA-Z0-9.-]+$/, 'Invalid SMTP host format'), // Example: smtp.gmail.com

    smtp_port: Yup.number().required('SMTP port is required').oneOf([25, 587, 465], 'Invalid SMTP port'), // Valid common SMTP ports

    imap_host: Yup.string()
      .required('IMAP host is required')
      .matches(/^imap\.[a-zA-Z0-9.-]+$/, 'Invalid IMAP host format'), // Example: imap.gmail.com

    imap_port: Yup.number().required('IMAP port is required').oneOf([993, 143], 'Invalid IMAP port'), // Valid common IMAP ports

    email_host_user: Yup.string().email('Invalid email format').required('Email host user is required'),

    email_use_tls: Yup.boolean()
      .required('TLS usage must be specified')
      .oneOf([true, false], 'TLS must be either true or false'),

    email_use_ssl: Yup.boolean()
      .required('SSL usage must be specified')
      .oneOf([true, false], 'SSL must be either true or false'),

    email_host_password: Yup.string().required('Email host password is required'),
  });

  const formik = useFormik({
    initialValues: {
      name: '',
      gst: '',
      email: '',
      phone: '',
      address: '',
      business_type: '',
      primary_contact_name: '',
      business_superuser: '', // Added new initial field
      logo: '',

      //email_config_payload_initial_values
      business: '',
      // server_email: "",
      // server_password: "",
      server_name: '',
      email_host: 'smtp.gmail.com',
      smtp_port: 587,
      imap_host: 'imap.gmail.com',
      imap_port: 993,
      email_host_user: '',
      email_use_tls: '',
      email_use_ssl: '',
      email_host_password: '',
    },
    validateOnMount: true,
    validationSchema,
    // onSubmit: (values) => {

    // },
  });

  useEffect(() => {
    // Trigger validation when formik values change (or on a specific field change)
    if (formik.dirty) {
      formik.validateForm();
    }
  }, [formik.values]);
  const orgIcon = () => (
    <svg width="16" height="15" viewBox="0 0 16 15" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        fill-rule="evenodd"
        clip-rule="evenodd"
        d="M2.26126 1.26126C2.78871 0.733816 3.50408 0.4375 4.25 0.4375H7.25C7.99592 0.4375 8.71129 0.733816 9.23874 1.26126C9.76618 1.78871 10.0625 2.50408 10.0625 3.25V13.1875H12.6875V11.2343C12.3518 11.1392 12.0427 10.9595 11.7916 10.7084C11.4048 10.3216 11.1875 9.79701 11.1875 9.25V7.75C11.1875 7.20299 11.4048 6.67839 11.7916 6.29159C12.1784 5.9048 12.703 5.6875 13.25 5.6875C13.797 5.6875 14.3216 5.9048 14.7084 6.29159C15.0952 6.67839 15.3125 7.20299 15.3125 7.75V9.25C15.3125 9.79701 15.0952 10.3216 14.7084 10.7084C14.4573 10.9595 14.1482 11.1392 13.8125 11.2343V13.1875H14.75C15.0607 13.1875 15.3125 13.4393 15.3125 13.75C15.3125 14.0607 15.0607 14.3125 14.75 14.3125H1.25C0.93934 14.3125 0.6875 14.0607 0.6875 13.75C0.6875 13.4393 0.93934 13.1875 1.25 13.1875H1.4375V3.25C1.4375 2.50408 1.73382 1.78871 2.26126 1.26126ZM2.5625 13.1875H5.1875V10.75C5.1875 10.4393 5.43934 10.1875 5.75 10.1875C6.06066 10.1875 6.3125 10.4393 6.3125 10.75V13.1875H8.9375V3.25C8.9375 2.80245 8.75971 2.37322 8.44324 2.05676C8.12678 1.74029 7.69755 1.5625 7.25 1.5625H4.25C3.80245 1.5625 3.37322 1.74029 3.05676 2.05676C2.74029 2.37323 2.5625 2.80245 2.5625 3.25V13.1875ZM4.4375 4.75C4.4375 4.43934 4.68934 4.1875 5 4.1875H6.5C6.81066 4.1875 7.0625 4.43934 7.0625 4.75C7.0625 5.06066 6.81066 5.3125 6.5 5.3125H5C4.68934 5.3125 4.4375 5.06066 4.4375 4.75ZM13.25 6.8125C13.0014 6.8125 12.7629 6.91127 12.5871 7.08709C12.4113 7.2629 12.3125 7.50136 12.3125 7.75V9.25C12.3125 9.49864 12.4113 9.7371 12.5871 9.91291C12.7629 10.0887 13.0014 10.1875 13.25 10.1875C13.4986 10.1875 13.7371 10.0887 13.9129 9.91291C14.0887 9.7371 14.1875 9.49864 14.1875 9.25V7.75C14.1875 7.50136 14.0887 7.2629 13.9129 7.08709C13.7371 6.91127 13.4986 6.8125 13.25 6.8125ZM4.4375 7.75C4.4375 7.43934 4.68934 7.1875 5 7.1875H6.5C6.81066 7.1875 7.0625 7.43934 7.0625 7.75C7.0625 8.06066 6.81066 8.3125 6.5 8.3125H5C4.68934 8.3125 4.4375 8.06066 4.4375 7.75Z"
        fill="#717680"
      />
    </svg>
  );

  const renderLogo = (logo) => {
    if (logo) {
      return <Image src={logo} />;
    }
    return orgIcon();
  };

  const renderList = () => {
    const itemsPerPage = 10;
    const totalPages = Math.ceil(paginationInfo?.count / itemsPerPage);
    const handlePaginationChange = (e, { activePage }) => {
      setActivePage(activePage);

      let endpoint = `${resturls.getBusinesses}?page=${activePage}`;

      // Add status filter only if it's not "all"
      if (status !== 'all' && status != '') {
        endpoint += `&is_active=${status}`;
      }

      setIsLoading(true);

      GlobalService.generalSelect(
        (respdata) => {
          if (respdata && respdata.results) {
            setBusinessList(respdata.results);
            setPaginationInfo(respdata);
            setIsLoading(false);
          } else {
            console.warn('No results found in response:', respdata);
            setBusinessList([]);
          }
        },
        endpoint,
        {},
        'GET'
      );
    };

    return (
      <>
        <div className={style.tableWrapper}>
          <Table basic="very">
            <Table.Header>
              <Table.Row>
                {/* <Table.HeaderCell><div className='customCheckBox '><Checkbox indeterminate className={`${style.checkbox}`}/></div></Table.HeaderCell> */}
                <Table.HeaderCell className={style.subjectheaderRow}>Org Name & Status</Table.HeaderCell>
                <Table.HeaderCell className={style.subjectheaderRow}>ID</Table.HeaderCell>
                <Table.HeaderCell className={style.subjectheaderRow}>GST Number</Table.HeaderCell>
                <Table.HeaderCell className={style.subjectheaderRow}>Account Manager</Table.HeaderCell>
                <Table.HeaderCell className={style.subjectheaderRow}>Primary Contact Info</Table.HeaderCell>
              </Table.Row>
            </Table.Header>
            <Table.Body>
              {businessList?.map((data) => (
                <Table.Row onClick={() => navigate(`/businessDetail/${data?.business_id}`)}>
                  {/* <Table.Cell><div className='customCheckBox'><Checkbox className={`${style.checkbox}`} /></div></Table.Cell> */}
                  <Table.Cell className={style.subjectRow}>
                    <div className={style.logo}>{renderLogo(data?.business_image)}</div>
                    <div className={style.businessName}>
                      <p>{data?.business_name}</p>
                      <span className={data?.is_active ? style.activeStatus : style.inActive}>
                        {data?.is_active ? 'Active' : 'Inactive'}
                      </span>
                    </div>
                  </Table.Cell>
                  <Table.Cell>#{data?.business_id}</Table.Cell>
                  <Table.Cell>{data?.gst}</Table.Cell>
                  <Table.Cell>{data?.accountant_manager || '-'}</Table.Cell>
                  <Table.Cell>
                    <div className={style.contactInfo}>
                      <p>{data?.primary_contact_name || '-'}</p>
                      <div className={style.iconWrapper}>
                        <Popup
                          inverted
                          className={style.popup}
                          trigger={<span>{phoneIcon()}</span>}
                          content={
                            <div className={style.popupContent}>
                              <p className={style.label}>Phone Number</p>
                              <p>{data?.business_phone}</p>
                            </div>
                          }
                          position="top right"
                          hoverable
                        />
                        <Popup
                          inverted
                          className={style.popup}
                          trigger={<span>{mailIcon()}</span>}
                          content={
                            <div className={style.popupContent}>
                              <p className={style.label}>Email</p>
                              <p>{data?.business_email}</p>
                            </div>
                          }
                          position="top right"
                          hoverable
                        />
                      </div>
                    </div>
                  </Table.Cell>
                </Table.Row>
              ))}
            </Table.Body>
          </Table>
        </div>
        <div className={style.paginationWrapper}>
          {paginationInfo?.count > 10 && businessList?.length > 0 && (
            <Pagination activePage={activePage} totalPages={totalPages} onPageChange={handlePaginationChange} />
          )}
        </div>
      </>
    );
  };
  const Superuseroptions =
    BuisnessSuperUserList?.length > 0 &&
    BuisnessSuperUserList?.map((user) => ({
      key: user.user_id,
      value: user.user_id,
      text: user.full_name,
    }));

  const renderCardList = () => {
    return (
      <div className={style.tableWrapper}>
        <div className={style.ticketList}>
          {businessList?.map((data) => (
            <Card className={`${style.ticketCard}`}>
              <Card.Content className={style.ticketContent}>
                <div className={style.rightContent}>
                  <h5>{data?.business_name}</h5>
                  <p>{data?.business_id}</p>
                  {data?.accountant_name && (
                    <p>
                      {data?.accountant_name || '-'}
                      {`  (Accountant)`}
                    </p>
                  )}
                </div>
                <div className={style.leftContent}>{renderLogo(data?.business_image)}</div>
              </Card.Content>
            </Card>
          ))}
        </div>
      </div>
    );
  };

  const OrganisationStatus = [
    {
      key: 'active',
      value: 'True',
      text: 'Active',
      className: style.verifiedStatus,
    }, // true for active
    {
      key: 'inactive',
      value: 'False',
      text: 'Inactive',
      className: style.deletedStatus,
    },
    {
      key: 'all',
      value: 'all',
      text: 'All',
      className: style.verifiedStatus,
    },
  ];

  const [status, setStatus] = useState(''); // Default status state

  // Function to handle dropdown change
  const handleStatusChange = (e, { value }) => {
    setStatus(value); // Update the status with boolean value

    OrganisationStatusUpdate(value); // Fetch data based on the new status
  };

  // Function to update organization status
  const OrganisationStatusUpdate = (isActiveStatus) => {
    setIsLoading(true);
    if (isActiveStatus === 'all') {
      GlobalService.generalSelect(
        (respdata) => {
          const { results } = respdata;
          setBuisnessCount(respdata.count);
          setBusinessList(results);
          setPaginationInfo(respdata);
          setIsLoading(false);
        },
        `${resturls.getBusinesses}`,
        {},
        'GET'
      );
      return;
    }

    GlobalService.generalSelect(
      (respdata) => {
        const { results } = respdata;
        setBusinessList([]); // Clear previous list to avoid mixing results
        setBuisnessCount(respdata.count);
        setBusinessList(results);
        setPaginationInfo(respdata);
        setIsLoading(false);
      },
      `${resturls.getBusinesses}?is_active=${isActiveStatus}`, // Use is_active in the API request
      {},
      'GET'
    );
  };

  const fetchBusinessTypes = useCallback(
    (url) => {
      if (isFetching) {
        return;
      }

      setIsFetching(true);

      GlobalService.generalSelect(
        (respData) => {
          const { results, next } = respData;

          if (!results || results.length === 0) {
            console.error('Invalid or empty response data', { respData });
            setIsFetching(false);
            return;
          }

          const newOptions = results.map((item) => ({
            key: item.id,
            value: item.id,
            text: item.name,
          }));

          setDropdownItems((prevItems) => {
            const existingKeys = new Set(prevItems.map((item) => item.key));
            const uniqueNewOptions = newOptions.filter((item) => !existingKeys.has(item.key));

            return [...prevItems, ...uniqueNewOptions];
          });
          setNextPageUrl(next);
          setIsFetching(false);

          console.log('Fetch Complete', {
            itemsLoaded: newOptions.length,
            nextPageAvailable: !!next,
          });
        },
        url,
        {},
        'GET'
      );
    },
    [isFetching]
  );

  useEffect(() => {
    if (dropdownItems.length === 0) {
      fetchBusinessTypes(resturls.fetchBusinessType);
    }
  }, [dropdownItems, fetchBusinessTypes, resturls.fetchBusinessType]);

  const loadMoreItems = () => {
    if (nextPageUrl && !isFetching) {
      const secureUrl = nextPageUrl.replace('http://', 'https://');
      fetchBusinessTypes(secureUrl);
    }
  };

  const dropdownOptions = [
    ...dropdownItems,
    ...(nextPageUrl && !isFetching
      ? [
          {
            key: 'load_more',
            text: 'Load More',
            value: 'load_more',
            disabled: isFetching,
            className: 'load-more-option',
          },
        ]
      : []),
  ];

  const sslTlsOptions = [
    { key: 'tls', text: 'TLS', value: 'tls' },
    { key: 'ssl', text: 'SSL', value: 'ssl' },
  ];

  const renderCreateForm = () => {
    if (step?.active === 'step1') {
      return (
        <div className={style.formContainer}>
          <Form.Field className={style.formField}>
            <label>Business Name *</label>
            <Form.Input
              id="name"
              name="name"
              placeholder="Enter business name"
              onChange={(e) => {
                formik.handleChange(e);
                formik.setFieldTouched('name', true); // Mark the field as touched
              }}
              onBlur={formik.handleBlur}
              value={formik.values.name}
              error={
                formik.touched.name && formik.errors.name ? { content: formik.errors.name, pointing: 'below' } : null
              }
            />
          </Form.Field>

          <Form.Field className={style.formField}>
            <label>GST Number *</label>
            <Form.Input
              id="gst"
              name="gst"
              placeholder="Enter GST"
              onChange={(e) => {
                formik.handleChange(e);
                formik.setFieldTouched('gst', true); // Mark the field as touched
              }}
              onBlur={formik.handleBlur}
              value={formik.values.gst}
              error={formik.touched.gst && formik.errors.gst ? { content: formik.errors.gst, pointing: 'below' } : null}
            />
          </Form.Field>

          <Form.Field className={style.formField}>
            <label>Address *</label>
            <Form.Input
              id="address"
              name="address"
              placeholder="Enter address"
              onChange={(e) => {
                formik.handleChange(e);
                formik.setFieldTouched('address', true); // Mark the field as touched
              }}
              onBlur={formik.handleBlur}
              value={formik.values.address}
              error={
                formik.touched.address && formik.errors.address
                  ? { content: formik.errors.address, pointing: 'below' }
                  : null
              }
            />
          </Form.Field>

          <Form.Field className={style.formField}>
            <label>Google Map Link (Optional)</label>
            <Form.Input
              id="google_map_link"
              name="google_map_link"
              placeholder="Enter Google Map Link"
              onChange={(e) => {
                formik.handleChange(e);
                formik.setFieldTouched('google_map_link', true); // Mark the field as touched
              }}
              onBlur={formik.handleBlur}
              value={formik.values.google_map_link}
              error={
                formik.touched.google_map_link && formik.errors.google_map_link
                  ? {
                      content: formik.errors.google_map_link,
                      pointing: 'below',
                    }
                  : null
              }
            />
          </Form.Field>

          <Form.Field className={style.formField}>
            <label>Business Type</label>
            <Dropdown
              ref={dropdownRef}
              id="business_type"
              name="business_type"
              placeholder="Select a business type"
              fluid
              search
              selection
              options={dropdownOptions}
              onChange={(e, { value }) => {
                formik.setFieldValue('business_type', value);
                formik.setFieldTouched('business_type', true); // Mark the field as touched
              }}
              value={formik.values.business_type}
              className="customDropdown4withLoadMore"
              onBlur={(e) => {
                e.preventDefault();
                return false;
              }}
              onClick={(e) => {
                if (e.target.classList.contains('load-more-option')) {
                  e.preventDefault();
                  e.stopPropagation();
                }
              }}
            />
          </Form.Field>
        </div>
      );
    }

    if (step?.active === 'step2') {
      return (
        <div className={style.formContainer}>
          <Form.Field className={style.formField}>
            <label>Primary Contact Name</label>
            <Form.Input
              id="primary_contact_name"
              name="primary_contact_name"
              placeholder="Enter Primary Contact Name"
              onChange={(e) => {
                formik.handleChange(e);
                formik.setFieldTouched('primary_contact_name', true); // Mark the field as touched
              }}
              onBlur={formik.handleBlur}
              value={formik.values.primary_contact_name}
              error={
                formik.touched.primary_contact_name && formik.errors.primary_contact_name
                  ? {
                      content: formik.errors.primary_contact_name,
                      pointing: 'below',
                    }
                  : null
              }
            />
          </Form.Field>

          <Form.Field className={style.formField}>
            <label>Phone Number</label>
            <Form.Input
              id="phone"
              name="phone"
              placeholder="Enter Phone Number"
              onChange={(e) => {
                formik.handleChange(e);
                formik.setFieldTouched('phone', true); // Mark the field as touched
              }}
              onBlur={formik.handleBlur}
              value={formik.values.phone}
              error={
                formik.touched.phone && formik.errors.phone ? { content: formik.errors.phone, pointing: 'below' } : null
              }
            />
          </Form.Field>

          <Form.Field className={style.formField}>
            <label>Email</label>
            <Form.Input
              id="email"
              name="email"
              placeholder="Enter email"
              onChange={(e) => {
                formik.handleChange(e);
                formik.setFieldTouched('email', true); // Mark the field as touched
              }}
              onBlur={formik.handleBlur}
              value={formik.values.email}
              error={
                formik.touched.email && formik.errors.email ? { content: formik.errors.email, pointing: 'below' } : null
              }
            />
          </Form.Field>

          <Form.Field className={style.formField}>
            <label>Business Superuser</label>
            <Dropdown
              placeholder="Select a Superuser"
              fluid
              search
              selection
              options={Superuseroptions}
              onChange={(e, { value }) => {
                formik.setFieldValue('business_superuser', value); // Set the value of the field
                formik.setFieldTouched('business_superuser', true); // Mark the field as touched
              }}
              value={formik.values.business_superuser}
              error={
                formik.touched.business_superuser && formik.errors.business_superuser
                  ? {
                      content: formik.errors.business_superuser,
                      pointing: 'below',
                    }
                  : null
              }
              icon={<DropdownIcon />}
              className="customDropdown4"
            />
          </Form.Field>
        </div>
      );
    }
    if (step?.active === 'step3') {
      return (
        <div className={style.formContainer}>
          <Form.Field className={style.formField}>
            <label>Server Name</label>
            <Form.Input
              id="server_name"
              name="server_name"
              placeholder="Enter Server Name:"
              onChange={formik.handleChange}
              onBlur={formik.handleBlur}
              value={formik.values.server_name}
              error={
                formik.touched.server_name && formik.errors.server_name
                  ? { content: formik.errors.server_name, pointing: 'below' }
                  : null
              }
            />
          </Form.Field>

          <Form.Field className={style.formField}>
            <label>SMTP HOST</label>
            <Form.Input
              id="email_host"
              name="email_host"
              placeholder="Enter server email"
              onChange={formik.handleChange}
              onBlur={formik.handleBlur}
              value={formik.values.email_host}
              error={
                formik.touched.email_host && formik.errors.email_host
                  ? { content: formik.errors.email_host, pointing: 'below' }
                  : null
              }
            />
          </Form.Field>

          <Form.Field className={style.formField}>
            <label>User Email </label>
            <Form.Input
              id="email_host_user"
              name="email_host_user"
              placeholder="Enter User Email "
              onChange={formik.handleChange}
              onBlur={formik.handleBlur}
              value={formik.values.email_host_user}
              error={
                formik.touched.email_host_user && formik.errors.email_host_user
                  ? {
                      content: formik.errors.email_host_user,
                      pointing: 'below',
                    }
                  : null
              }
            />
          </Form.Field>

          <Form.Field className={style.formField}>
            <label>User Password</label>
            <Form.Input
              id="email_host_password"
              name="email_host_password"
              type="password"
              placeholder="Enter server password"
              onChange={formik.handleChange}
              onBlur={formik.handleBlur}
              value={formik.values.email_host_password}
              error={
                formik.touched.email_host_password && formik.errors.email_host_password
                  ? {
                      content: formik.errors.email_host_password,
                      pointing: 'below',
                    }
                  : null
              }
            />
          </Form.Field>

          <Form.Field className={style.formField}>
            <label>SMTP PORT </label>
            <Form.Input
              id="smtp_port"
              name="smtp_port"
              placeholder="Enter SMTP prt"
              onChange={formik.handleChange}
              onBlur={formik.handleBlur}
              value={formik.values.smtp_port}
              error={
                formik.touched.smtp_port && formik.errors.smtp_port
                  ? { content: formik.errors.smtp_port, pointing: 'below' }
                  : null
              }
            />
          </Form.Field>

          <Form.Field className={style.formField}>
            <label>IMAP HOST </label>
            <Form.Input
              id="imap_host"
              name="imap_host"
              placeholder="Enter IMAP HOST: "
              onChange={formik.handleChange}
              onBlur={formik.handleBlur}
              value={formik.values.imap_host}
              error={
                formik.touched.imap_host && formik.errors.imap_host
                  ? { content: formik.errors.imap_host, pointing: 'below' }
                  : null
              }
            />
          </Form.Field>

          <Form.Field className={style.formField}>
            <label>IMAP PORT </label>
            <Form.Input
              id="imap_port"
              name="imap_port"
              placeholder="Enter IMAP PORT: "
              onChange={formik.handleChange}
              onBlur={formik.handleBlur}
              value={formik.values.imap_port}
              error={
                formik.touched.imap_port && formik.errors.imap_port
                  ? { content: formik.errors.imap_port, pointing: 'below' }
                  : null
              }
            />
          </Form.Field>

          <Form.Field className={style.formField}>
            <label>Email Encryption</label>
            <Dropdown
              id="email_encryption"
              name="email_encryption"
              placeholder="Select Encryption Type"
              fluid
              className="customDropdown4"
              selection
              options={sslTlsOptions}
              value={formik.values.email_encryption} // Bind to Formik value
              onChange={(e, { value }) => {
                formik.setFieldValue('email_encryption', value); // Update Formik's field
                // Update TLS and SSL fields based on encryption type
                formik.setFieldValue('email_use_tls', value === 'tls');
                formik.setFieldValue('email_use_ssl', value === 'ssl');
              }}
              onBlur={() => formik.setFieldTouched('email_encryption')} // Handle blur for Formik
              error={
                formik.touched.email_encryption && formik.errors.email_encryption
                  ? {
                      content: formik.errors.email_encryption,
                      pointing: 'below',
                    }
                  : null
              }
            />
          </Form.Field>
        </div>
      );
    }
  };

  const renderButtons = () => {
    const keys1 = ['name', 'gst', 'address', 'business_type'];
    const step1Validation = keys1.some((key) => Object.keys(formik.errors).includes(key));
    const keys2 = ['primary_contact_name', 'phone', 'email', 'business_superuser'];
    const step2Validation = keys2.some((key) => Object.keys(formik.errors).includes(key));
    const formatPhoneNumber = (phone) => {
      if (phone.startsWith('+91')) {
        return phone.slice(1);
      } else if (!phone.startsWith('91')) {
        return `91${phone}`;
      }
      return phone;
    };

    if (step?.active === 'step1') {
      return (
        <div className={style.buttonWrapper}>
          <Button type="button" className={style.cancel} onClick={() => setActiveModal(false)}>
            Cancel
          </Button>
          <Button
            type="button"
            className={style.nextBtn}
            onClick={() => setStep({ ...step, active: 'step2', step2: true })}
            disabled={step1Validation}
          >
            Next
          </Button>
        </div>
      );
    }

    if (step?.active === 'step2') {
      return (
        <div className={style.buttonWrapper}>
          <Button className={style.cancel} type="button" onClick={() => setActiveModal(false)}>
            Cancel
          </Button>
          <div className={style.subBtnWrapper}>
            <Button type="button" className={style.backBtn} onClick={() => setStep({ ...step, active: 'step1' })}>
              Back
            </Button>
            <Button
              type="button"
              className={style.nextBtn}
              onClick={() => {
                // Validate second step before moving
                formik.validateForm().then((errors) => {
                  const phone = formatPhoneNumber(formik.values.phone);
                  const basicAndContactValues = {
                    ...formik.values,
                    business_superuser: Number(formik.values.business_superuser),
                    phone: phone,
                  };

                  GlobalService.generalSelect(
                    (respdata) => {
                      // Store the generated business ID
                      const createdBusinessId = respdata.id;
                      setBusinessId(createdBusinessId);
                      formik.setFieldValue('business_id', createdBusinessId);

                      // Move to server info step
                      setStep({ ...step, active: 'step3', step3: true });
                    },
                    `${resturls.createBusiness}`,
                    basicAndContactValues,
                    'POST'
                  );
                });
              }}
              disabled={step2Validation}
            >
              Next
            </Button>
          </div>
        </div>
      );
    }
    return (
      <div className={style.buttonWrapper}>
        <Button className={style.cancel} type="button" onClick={() => setActiveModal(false)}>
          Cancel
        </Button>
        <div className={style.subBtnWrapper}>
          <Button
            type="button"
            className={style.backBtn}
            onClick={() => {
              setActiveModal(false);
              window.location.reload();
            }}
          >
            Skip
          </Button>
          <Button
            type="submit"
            className={style.nextBtn}
            onClick={() => {
              const serverInfoPayload = {
                password: formik.values.server_password,
                server_name: formik.values.server_name,
                email_host: formik.values.email_host,
                smtp_port: formik.values.smtp_port,
                imap_host: formik.values.imap_host,
                imap_port: formik.values.imap_port,
                email_host_user: formik.values.email_host_user,
                email_use_tls: formik.values.email_use_tls,
                email_use_ssl: formik.values.email_use_ssl,
                email_host_password: formik.values.email_host_password,
                business: businessId,
                is_active: true,
              };

              GlobalService.generalSelect(
                (respdata) => {
                  setActiveModal(false);
                  window.location.reload();
                },
                `${resturls.emailserver}`,
                serverInfoPayload,
                'POST'
              );
            }}
            disabled={!formik.isValid}
          >
            Save
          </Button>
        </div>
      </div>
    );
  };

  const renderCreateModal = () => {
    return (
      <Modal basic size="small" open={activeModal} onClose={() => setActiveModal(false)}>
        <div className={style.modalContent}>
          <h5>Add New Business</h5>
          <div className={style.progressWrapper}>
            <div className={style.progress}>
              <span
                className={`${step?.step1 ? style.enableDot : ''} ${
                  step?.active === 'step1' ? style.activeDot : ''
                } ${style.dot}`}
              />
              <span className={style.line} />
              <span
                className={`${step?.step2 ? style.enableDot : ''} ${
                  step?.active === 'step2' ? style.activeDot : ''
                } ${style.dot}`}
              />
              <span className={style.line} />
              <span
                className={`${step?.step3 ? style.enableDot : ''} ${
                  step?.active === 'step3' ? style.activeDot : ''
                } ${style.dot}`}
              />
            </div>
            <div className={style.labelWrapper}>
              <div className={style.label}>
                <span className={style.firstLable}>Basic Info</span>
                <span>Primary Contact</span>
                <span>Server Info</span>
              </div>
            </div>
          </div>
          <Form className={style.formWrapper} onSubmit={formik.handleSubmit}>
            {renderCreateForm()}
            {renderButtons()}
          </Form>
        </div>
      </Modal>
    );
  };

  return (
    <>
      <Header />
      <div className={style.bussinessListScreen}>
        <div className={style.navigationWrapper}>
          <NavigationBar disable />
        </div>
        <div className={style.rightContentWrapper}>
          <div className={style.headerPart}>
            <div>
              <h4 className={style.separate}>
                Organisations <span className={style.span_orgCOunt}>{BuisnesCount}</span>
              </h4>
              <p className={style.desc}>Manage organization details, settings, and configurations easily</p>
            </div>
            <div className={style.btnWrapper}>
              <div className={style.addBtn} onClick={() => setActiveModal(true)}>
                Add
              </div>
            </div>
          </div>
          <div className={style.searchWrapper}>
            <Input
              className={style.searchInput}
              icon="search"
              placeholder="Search"
              iconPosition="left"
              value={searchQuery}
              onChange={handleInputChange}
            />
            <Dropdown
              placeholder="All"
              className={`customDropdown3 ${style.statusDropdown}`}
              icon={<DropdownIcon />}
              options={OrganisationStatus}
              onChange={handleStatusChange}
            />
          </div>
          {isLoading ? (
            <div className={style.loaderContainer}>
              <Loader active inline="centered" size="medium" />
            </div>
          ) : isResponsive ? (
            renderCardList()
          ) : (
            renderList()
          )}
        </div>
      </div>
      {renderCreateModal()}
    </>
  );
};

export default BusinessList;

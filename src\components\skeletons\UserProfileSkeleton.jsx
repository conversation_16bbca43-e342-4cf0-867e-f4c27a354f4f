import React from 'react';
import style from './scss/userProfileSkeleton.module.scss';

function UserProfileSkeleton({ count = 1 }) {
  return Array.from({ length: count }).map((_, index) => (
    <div key={index}>
      <div className={style.profileWrapper}>
        <div className={style.skeletonAvatar} />
        <div className={style.skeletonText} />
      </div>
      <hr />
      <div className={style.details}>
        <p className={style.label}>User ID</p>
        <div className={style.skeletonTextSmall} />
        <p className={style.label}>Mobile Number</p>
        <div className={style.skeletonTextSmall} />
      </div>
      <div className={style.skeletonButton} />
      <div className={style.skeletonButton} />
    </div>
  ));
}

export default UserProfileSkeleton;

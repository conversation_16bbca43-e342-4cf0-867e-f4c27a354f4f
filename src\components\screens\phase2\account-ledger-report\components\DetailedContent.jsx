import React from 'react';
import { Popup } from 'semantic-ui-react';
import PaginationContent from '../../../../utils/reportUtils/PaginationContent';
import { formatAmount, formatDateLastUpdate } from '../../../../utils/dateUtils';
import { DownArrow, EmptyBox, SearchIcon, SortIcon, UpArrow } from '../../../../../assets/svgs';
import style from '../scss/detailedContent.module.scss';
import { useAuth } from '../../../../../contexts/AuthContext';

const DetailedContent = ({
  isLoading,
  overViewContent,
  handleSortDropdown,
  renderSortPopupContent,
  sortDropdown,
  setOpenDropdown,
  handleTransactionSearch,
  transactionSearch,
  transactionDetails,
  transactionActivePage,
  handleTransactionPagination,
}) => {
  const { isMobileScreen } = useAuth();

  if (!isLoading && Object.keys(overViewContent)?.length === 0) {
    return (
      <div className={style.emptyMsg}>
        <p>Select Account/Ledger to view more information here</p>
      </div>
    );
  }
  return (
    <div className={style.contentWrapper}>
      <div className={style.overViewContent}>
        {overViewContent?.length > 0 &&
          overViewContent?.map((info) => (
            <div className={`${info?.line ? style.overViewLine : ''} ${style.overViewItem}`}>
              {info?.line && <span className={style.sideBarLine} />}
              <div>
                <p>{info?.title}</p>
                <p className={style.timeStamp}>{info?.timeStamp}</p>
              </div>
              <p className={`${info?.className} ${style.amount}`}>{info?.amount}</p>
            </div>
          ))}
      </div>
      <div className={style.titlePart}>
        <h4>Detailed Report</h4>
        {!isMobileScreen && (
          <div className={style.filterWrapper}>
            <Popup
              className={style.popup}
              trigger={
                <div className={style.sortFilter} onClick={() => handleSortDropdown()}>
                  {' '}
                  <SortIcon />
                  <p>Sort</p>
                </div>
              }
              content={renderSortPopupContent()}
              position="bottom left"
              on="click"
              hoverable
              basic
              open={!isMobileScreen && sortDropdown}
              onClose={() => {
                setOpenDropdown(false);
              }}
            />
            <div className={style.customInput}>
              <SearchIcon />
              <input
                type="text"
                placeholder="Search Transaction by Journal Number, Type..."
                onChange={handleTransactionSearch}
                value={transactionSearch}
              />
            </div>
          </div>
        )}
      </div>
      <div className={style.detailedContentWrapper}>
        {transactionDetails?.data?.length > 0 ? (
          <>
            <div className={style.detailedCardHeader}>
              {isMobileScreen ? (
                <div className={`${style.invoiceItem} ${style.item}`}>
                  <p>Invoice</p>
                  <p>Type</p>
                  <p>Date</p>
                </div>
              ) : (
                <>
                  <p>Invoice</p>
                  <p>Type</p>
                  <p>Date</p>
                </>
              )}
              <p className={`${style.voucherType} ${style.item}`}>
                Voucher <p>type</p>
              </p>
              <p className={`${style.amountItem} ${style.item}`}>Amount</p>
            </div>
            {transactionDetails?.data &&
              transactionDetails?.data?.map((info) => (
                <div className={style.detailedContent}>
                  {isMobileScreen ? (
                    <div className={style.mobileDetailedContent}>
                      <p>{info?.journal_number}</p>
                      <p className={`${style.duedate} ${style.subContent}`}>{info?.type}</p>
                      <p className={`${style.duedate} ${style.subContent}`}>{formatDateLastUpdate(info?.date, true)}</p>
                    </div>
                  ) : (
                    <>
                      <p>{info?.journal_number}</p>
                      <p>{info?.type}</p>
                      <p className={`${style.duedate}`}>{formatDateLastUpdate(info?.date, true)}</p>
                    </>
                  )}
                  <p className={style.journal_type}>{info?.journal_type}</p>
                  <p
                    className={`${info?.type === 'debit' ? style.negativeAmount : style.positiveAmount} ${style.amountContent}`}
                  >
                    <p>{formatAmount(info?.amount)}</p>
                    <span>{info?.type === 'debit' ? <DownArrow /> : <UpArrow />}</span>
                  </p>
                </div>
              ))}
            {transactionDetails?.pagination?.total_pages > 1 && (
              <div className={style.paginationWrapper}>
                <PaginationContent
                  activePage={transactionActivePage}
                  totalPages={transactionDetails?.pagination?.total_pages}
                  pageChangeFunction={handleTransactionPagination}
                />
              </div>
            )}
          </>
        ) : (
          <div className={style.emptyMsgTab}>
            <EmptyBox />
            <p>No transaction found</p>
          </div>
        )}
      </div>
    </div>
  );
};

export default DetailedContent;

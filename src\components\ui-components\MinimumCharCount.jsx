import { AnimatePresence, motion } from 'motion/react';
import React from 'react';

function MinimumCharCount({ minChars, charCount }) {
  return (
    <AnimatePresence>
      {minChars > 0 && charCount > 0 && (
        <motion.div
          key="minCharacters"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          transition={{ duration: 0.4 }}
          className="text-gray-700 text-base font-medium"
        >
          <span className={charCount < minChars ? 'text-orange-500 font-bold' : 'text-green-500'}>
            Min Characters: {minChars} {charCount < minChars ? `(${minChars - charCount} more needed)` : '✓'}
          </span>
        </motion.div>
      )}
    </AnimatePresence>
  );
}

export default MinimumCharCount;

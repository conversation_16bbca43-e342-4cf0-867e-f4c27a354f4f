import React from 'react';
import Checkbox from '../../../../ui-components/fields/Checkbox';

function MarkAsValidated({ checked, disabled = false, onChange = null }) {
  return (
    <div className={`col-span-full p-3 rounded-full w-fit ${disabled ? 'bg-[#d5d7da]' : 'bg-[#E3F8D9]'}`}>
      <Checkbox
        disabled={disabled}
        label="Mark as Validated"
        onChange={(checked) => onChange && onChange(checked)}
        color="success"
        className="text-sm md:text-base"
        checked={checked}
      />
    </div>
  );
}

export default MarkAsValidated;

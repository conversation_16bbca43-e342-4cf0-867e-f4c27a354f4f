import React from 'react';
import { formatTimestamp } from '../../utils/dateUtils';
import { DownloadIcon, RefreshIcon } from '../../../assets/svgs';
import style from './scss/reportHeader.module.scss';
import { Loader } from 'semantic-ui-react';

const ReportHeader = ({ title, handleRefresh, setDownloadModal, isDisable, data, loading }) => {
  return (
    <div className={style.headerContent}>
      <div className={style.leftContent}>
        <h4>{title}</h4>
        <p>Last generated on {formatTimestamp(data?.synced_at)}</p>
      </div>
      <div className={style.rightContent}>
        <button className={style.refreshBtn} onClickCapture={() => handleRefresh()}>
          <RefreshIcon />
          <span>Refresh</span>
        </button>
        {!isDisable && (
          <button className={style.downloadBtn} onClick={() => setDownloadModal(true)}>
            {loading ? (
              <Loader active size="tiny" inline="centered" />
            ) : (
              <>
                <DownloadIcon /> Download Report
              </>
            )}
          </button>
        )}
      </div>
    </div>
  );
};

export default ReportHeader;

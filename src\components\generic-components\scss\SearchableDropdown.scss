@import '../../../assets/scss/main.scss';

.searchable-dropdown {
  width: 100%;
  position: relative;
  font-family: 'DM Sans', sans-serif;

  .label-row {
    display: flex;
    flex-direction: row;
    align-items: center;
    gap: 2px;
    margin-bottom: 4px;

    .label {
      font-size: 12px;
      font-weight: 400;
      line-height: 1.333em;
      color: #181d27;
    }

    .required {
      font-family: 'Lato', sans-serif;
      font-size: 12px;
      font-weight: 400;
      line-height: 1.333em;
      color: #e23131;
    }
  }

  .dropdown-field {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 14px 16px;
    background-color: #f5f5f5;
    border: 1px solid #e9eaeb;
    border-radius: 12px;
    cursor: pointer;
    transition: border-color 0.2s ease;
    min-height: 52px;

    &:hover {
      border-color: #d0d5dd;
    }

    &.disabled {
      opacity: 0.6;
      cursor: not-allowed;
    }

    .selected-value {
      font-size: 14px;
      font-weight: 400;
      line-height: 1.286em;
      color: #181d27;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      flex: 1;
    }
  }

  .search-input {
    flex: 1;
    border: none;
    background-color: transparent !important;
    font-size: 14px;
    font-weight: 400;
    line-height: 1.286em;
    color: #181d27;
    outline: none;
    padding: 0;
    width: 100%;
    height: 100%;
    appearance: none;
    -webkit-appearance: none;
    -moz-appearance: none;

    &::placeholder {
      color: #717680;
    }
  }

  .dropdown-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    color: #64748b;
    margin-left: 8px;
  }

  .dropdown-menu {
    position: absolute;
    top: calc(100% + 4px);
    left: 0;
    width: 100%;
    background-color: #ffffff;
    border: 1px solid #ebecf3;
    border-radius: 12px;
    box-shadow: 0px 0px 35px 0px rgba(0, 0, 0, 0.1);
    z-index: 10;
    overflow: hidden;

    .match-count {
      padding: 12px 16px;
      font-size: 12px;
      font-weight: 400;
      color: #717680;
      border-bottom: 1px solid #f5f5f5;
    }

    .options-list {
      max-height: 300px;
      overflow-y: auto;
      padding: 8px 0;
      display: flex;
      flex-direction: column;

      .option-item {
        font-size: 14px;
        font-weight: 400;
        line-height: 1.286em;
        color: #181d27;
        cursor: pointer;
        transition: background-color 0.2s ease;
        padding: 12px 16px;

        &:hover {
          background-color: #f5f5f5;
        }

        &.selected {
          font-weight: 500;
          color: #4e5ba6;
        }
      }

      .divider {
        height: 1px;
        background-color: #f5f5f5;
        margin: 8px 0;
      }

      .create-new {
        display: flex;
        align-items: center;
        gap: 8px;
        font-size: 14px;
        font-weight: 400;
        line-height: 1.286em;
        color: #4e5ba6;
        cursor: pointer;
        transition: background-color 0.2s ease;
        padding: 12px 16px;

        &:hover {
          background-color: rgba(78, 91, 166, 0.05);
        }

        svg {
          color: #4e5ba6;
        }
      }

      .no-options {
        padding: 12px 16px;
        font-size: 14px;
        color: #717680;
        text-align: center;
      }
    }
  }
}

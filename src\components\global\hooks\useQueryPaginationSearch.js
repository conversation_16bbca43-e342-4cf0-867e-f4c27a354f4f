import { useState, useEffect } from 'react';
import { useQuery } from '@tanstack/react-query';
import apiClient from '../../services/apiClient';
import useDebounce from './useDebounce';

const useQueryPaginationSearch = ({
  url,
  searchParam = 'search',
  searchValue = '',
  debounceDelay = 500,
  transformOptions = { key: 'id', label: 'name', value: 'id' },
  enabled = true,
  staleTime = 5 * 60 * 1000,
  initialPage = 1,
  ...queryOptions
}) => {
  const debouncedSearchValue = useDebounce(searchValue, debounceDelay);
  const [currentPage, setCurrentPage] = useState(initialPage);

  useEffect(() => {
    setCurrentPage(initialPage);
  }, [debouncedSearchValue, initialPage]);

  const { data, isLoading, isFetching, error, refetch } = useQuery({
    queryKey: ['query-pagination-search', url, debouncedSearchValue, currentPage],
    queryFn: async () => {
      const res = await apiClient.get(url, {
        params: {
          [searchParam]: debouncedSearchValue,
          page: currentPage,
        },
      });

      let items = res?.data || [];
      let pagination = res?.pagination || {};

      return {
        items: items.map((item) => ({
          key: item?.[transformOptions.key] ?? null,
          label: item?.[transformOptions.label] ?? '',
          value: item?.[transformOptions.value] ?? null,
          ...item,
        })),
        pagination,
      };
    },
    enabled: enabled && !!url,
    staleTime,
    ...queryOptions,
  });

  const { pagination } = data || {};
  const totalPages = pagination?.total_pages || 0;
  const pageSize = pagination?.per_page || 10;

  const goToPage = (page) => {
    if (page >= 1 && page <= totalPages) setCurrentPage(page);
  };

  return {
    data: data?.items || [],
    isLoading,
    isFetching,
    error,
    refetch,
    currentPage,
    totalPages,
    totalItems: pagination?.total_rows || 0,
    pageSize,
    goToPage,
    pagination,
  };
};

export default useQueryPaginationSearch;

import React, { useState, useEffect, useRef } from 'react';
import style from './scss/revenueReport.module.scss';
import { mediaBreakpoint } from '../../../global/MediaBreakPointes.js';
import GlobalService from '../../../services/GlobalServices.js';
import { resturls } from '../../../utils/apiurls.js';
import ls from 'local-storage';
import { useNavigate } from 'react-router-dom';
import { timelineDropdownOptions } from '../../../utils/constants.js';
import { formatDate, getGranularity, getDateRange, getPreviousDateRange } from '../../../utils/dateUtils.js';
import { debugLog } from '../../../utils/debuggerUtils.js';
import { BackIcon, DownloadIcon } from '../../../../assets/svgs/index.js';
import RenderOverallContent from './components/RenderOverallContent.jsx';
import LoadingWrapper from '../../../global/components/LoadingWrapper.jsx';

const RevenueReport = () => {
  const navigate = useNavigate();

  const [selectedTimeline, setTimeline] = useState(timelineDropdownOptions[0]);
  const [openDropdown, setOpenDropdown] = useState(false);
  const [downloadModal, setDownloadModal] = useState(false);
  const [activePage, setActivePage] = useState(1);
  const [isLoading, setIsLoading] = useState(false);
  const [detailedViewLoading, setDetiledViewLoading] = useState(false);
  const [revenueDetails, setRevenueDetails] = useState({});
  const [detailedReportData, setDetailedReportData] = useState({});
  const [trendLineDetails, setTrendLineDetails] = useState({});
  const [dateInfo, setDateInfo] = useState({ startDate: '', endDate: '' });
  const isResponsive = mediaBreakpoint?.mobile > window.innerWidth;
  const [activeInput, setActiveInput] = useState({ startDate: false, endDate: false });
  const [activeDetailsPage, setActiveDetailsPage] = useState(1);

  const storedData = ls.get('revenueDetails');
  const popupRef = useRef(null); // Reference for the popup

  const handleOutsideClick = (event) => {
    if (popupRef.current && !popupRef.current.contains(event.target) && !isResponsive) {
      if (selectedTimeline?.value === 'customDate') {
        if (dateInfo?.startDate && dateInfo?.endDate) {
          setOpenDropdown(false);
          return;
        }
        if (dateInfo?.startDate || dateInfo?.endDate) {
          return; // Prevent closing if at least one date is selected
        }
        setTimeline(timelineDropdownOptions[0]);
        const dateRanges = getDateRange(timelineDropdownOptions[0]?.value);
        handleFilter(dateRanges, false, timelineDropdownOptions[0]);
        setActiveInput({
          startDate: false,
          endDate: false,
        });
      }

      setOpenDropdown(false);
    }
  };

  useEffect(() => {
    document.addEventListener('mousedown', handleOutsideClick); // Add event listener
    return () => {
      document.removeEventListener('mousedown', handleOutsideClick); // Cleanup event listener
    };
  }, [selectedTimeline, dateInfo]);

  const detailedData = ls.get('revenueDetailedReport');
  const obtainRevenueReportDetails = () => {
    const storedInfo = ls.get('revenueTrendLineDetails');

    if (storedData) {
      const {
        data,
        expiry_at,
        date,
        timeLine = {
          text: 'Last 7 days',
          value: 'last7days',
        },
        page,
      } = storedData;
      const expiryDate = new Date(expiry_at);
      const currentDate = new Date();
      // Check if the stored data has expired
      if (expiryDate < currentDate) {
        // If expired, remove it from local storage and make a new API call
        ls.remove('revenueDetails');
        ls.remove('revenueTrendLineDetails');
        ls.remove('revenueDetailedReport');
        fetchRevenueReportData();
      } else {
        setRevenueDetails(data);
        setDetailedReportData(detailedData);
        setActiveDetailsPage(detailedData?.pagination?.page || 1);
        setTrendLineDetails(storedInfo?.data);
        setTimeline(timeLine);
        setActivePage(page || 1);
        if (date) {
          setDateInfo(date);
        }
      }
    } else {
      // If there's no data in local storage, fetch new data
      fetchRevenueReportData();
    }
  };

  const fetchTrendLinetData = async (dateRanges, page, timeLine) => {
    const previousDateRange = getPreviousDateRange(dateRanges?.startDate, dateRanges?.endDate, timeLine);
    const granularity = getGranularity(dateRanges?.startDate, dateRanges?.endDate);

    GlobalService.generalSelect(
      (respdata) => {
        if (respdata?.data) {
          const { expiry_at } = respdata;
          const dataToStore = {
            data: respdata,
            expiry_at,
          };
          ls.set('revenueTrendLineDetails', dataToStore);
          setTrendLineDetails(respdata);
        }
      },
      `${resturls.obtainRevenueReportDetails}?start_date=${
        previousDateRange?.startDate
      }&end_date=${previousDateRange?.endDate}&granularity=${granularity}&page_size=30&business_id=${
        ls.get('selectedBusiness')?.business_id
      }`,
      {},
      'GET'
    );
  };

  const fetchRevenueReportData = () => {
    const dateRanges =
      selectedTimeline?.value === 'customDate' ? storedData?.date : getDateRange(selectedTimeline?.value);
    fetchTrendLinetData(dateRanges);
    fetchDetailedReportData(dateRanges, 1);
    setIsLoading(true);
    const granularity = getGranularity(dateRanges?.startDate, dateRanges?.endDate);
    debugLog('Fetching data with params', {}, 'Revenue Report Page');
    GlobalService.generalSelect(
      (respdata) => {
        if (respdata?.data) {
          debugLog('Received API response', respdata, 'Revenue Report Page');
          const { expiry_at } = respdata;
          const dataToStore = {
            ...storedData,
            data: respdata,
            expiry_at,
            page: activePage,
          };

          ls.set('revenueDetails', dataToStore);
          debugLog('Stored data in localStorage', dataToStore, 'Revenue Report Page');
          setRevenueDetails(respdata);
          setIsLoading(false);
        }
      },
      `${resturls.obtainRevenueReportDetails}?start_date=${dateRanges?.startDate}&granularity=${granularity}&end_date=${dateRanges?.endDate}&page_size=30&business_id=${
        ls.get('selectedBusiness')?.business_id
      }`,
      {},
      'GET'
    );
  };

  const fetchDetailedReportData = (dateRanges, page) => {
    setDetiledViewLoading(true);
    GlobalService.generalSelect(
      (respdata) => {
        if (respdata?.data) {
          // const detailedViewInfo = respdata?.data?.sort((a, b) => new Date(b.label) - new Date(a.label)).map((item) => item);
          // const data = respdata?.pagination?.total_pages > 1 ? respdata?.data : detailedViewInfo;
          const obj = {
            pagination: respdata?.pagination,
            data: respdata?.data,
          };
          ls.set('revenueDetailedReport', obj);
          setDetailedReportData(obj);
          setActiveDetailsPage(respdata?.pagination?.page || 1);
          setDetiledViewLoading(false);
        }
      },
      `${resturls.obtainRevenueReportDetails}?start_date=${dateRanges?.startDate}&granularity=day&end_date=${dateRanges?.endDate}${page ? `&page=${page}` : ''}&page_size=31&business_id=${
        ls.get('selectedBusiness')?.business_id
      }`,
      {},
      'GET'
    );
  };

  useEffect(() => {
    // setIsLoading(true);
    obtainRevenueReportDetails();
  }, []);

  const handleFilter = (value, page, timeLine) => {
    const granularity = getGranularity(value?.startDate, value?.endDate);
    const queryParams = new URLSearchParams();
    setIsLoading(true);
    if (value?.startDate && value?.endDate) {
      queryParams.append('start_date', value?.startDate);
      queryParams.append('end_date', value?.endDate);
      queryParams.append('granularity', granularity);
      queryParams.append('page_size', 30);
      queryParams.append('business_id', ls.get('selectedBusiness')?.business_id);

      if (page) {
        queryParams.append('page', page);
      }
    } else {
      return;
    }
    fetchTrendLinetData({ startDate: value?.startDate, endDate: value?.endDate }, page, timeLine);
    fetchDetailedReportData({ startDate: value?.startDate, endDate: value?.endDate }, page, timeLine);
    GlobalService.generalSelect(
      (respdata) => {
        if (respdata?.data) {
          const { expiry_at } = respdata;
          let dataToStore = {
            timeLine,
            data: respdata,
            expiry_at,
            page,
          };
          if (timeLine?.value === 'customDate') {
            dataToStore = { ...dataToStore, date: value };
          }
          ls.set('revenueDetails', dataToStore);
          setRevenueDetails(respdata);
          setActiveDetailsPage(1);
          setIsLoading(false);
        }
      },
      `${resturls.obtainRevenueReportDetails}?${queryParams}`,
      {},
      'GET'
    );
  };

  const handlePagination = (direction) => {
    const totalPage = revenueDetails?.pagination?.total_pages;
    let page = activePage;

    if (direction === 'right') {
      if (page < totalPage) {
        page = page + 1;
      }
    } else if (direction === 'left') {
      if (page > 1) {
        page = page - 1;
      }
    }
    setActivePage(page);
    if (selectedTimeline?.value === 'customDate') {
      const startDate = formatDate(dateInfo.startDate);
      const endDate = formatDate(dateInfo.endDate);
      const obj = { startDate, endDate };
      handleFilter(obj, page, selectedTimeline);
    } else {
      const dateRanges = getDateRange(selectedTimeline?.value);
      handleFilter(dateRanges, page, selectedTimeline);
    }
  };

  const handleDetailsPagination = (page) => {
    setActiveDetailsPage(page);
    if (selectedTimeline?.value !== 'customDate') {
      const dateRanges = getDateRange(selectedTimeline?.value);
      fetchDetailedReportData(dateRanges, page, selectedTimeline);
    } else {
      const startDate = formatDate(dateInfo.startDate);
      const endDate = formatDate(dateInfo.endDate);
      const obj = { startDate, endDate };
      fetchDetailedReportData(obj, page, selectedTimeline);
    }
  };

  const handleSelectDropdown = (list) => {
    if (list?.value === selectedTimeline?.value) {
      // setOpenDropdown(false);
      return;
    }
    setTimeline(list);
    if (list?.value !== 'customDate' && !isResponsive) {
      setDateInfo({ startDate: '', endDate: '' });
      setOpenDropdown(false);
      const dateRanges = getDateRange(list?.value);
      handleFilter(dateRanges, false, list);
    }
    if (list?.value !== 'customDate') {
      setActiveInput({
        startDate: false,
        endDate: false,
      });
    }
  };

  const handleApplyFilter = () => {
    if (selectedTimeline?.value !== 'customDate') {
      setDateInfo({ startDate: '', endDate: '' });
      setOpenDropdown(false);
      const dateRanges = getDateRange(selectedTimeline?.value);
      handleFilter(dateRanges, false, selectedTimeline);
    } else {
      if (dateInfo.startDate && dateInfo.endDate) {
        const startDate = formatDate(dateInfo.startDate);
        const endDate = formatDate(dateInfo.endDate);
        const obj = { startDate, endDate };
        handleFilter(obj, false, selectedTimeline);
        setOpenDropdown(false);
      }
    }
  };

  const handleDateSelection = (key, value) => {
    const newDateInfo = { ...dateInfo, [key]: value };
    if (key === 'startDate') {
      if (!newDateInfo.endDate || newDateInfo.endDate < value) {
        newDateInfo.endDate = new Date().toISOString().split('T')[0];
      }
    }

    setDateInfo(newDateInfo);
    setActiveInput({
      startDate: !!newDateInfo.startDate,
      endDate: !!newDateInfo.endDate,
    });
  };

  const handleCloseDateFilter = () => {
    if (dateInfo?.startDate && dateInfo?.endDate) {
      setOpenDropdown(false);
      const startDate = formatDate(dateInfo.startDate);
      const endDate = formatDate(dateInfo.endDate);
      const obj = { startDate, endDate };
      handleFilter(obj, false, selectedTimeline);
    }
  };

  const handleDropdownList = () => {
    setOpenDropdown(true);
  };

  const handleClose = () => {
    setOpenDropdown(false);
    if (selectedTimeline?.value === 'customDate' && (!dateInfo?.startDate || !dateInfo?.endDate)) {
      setTimeline(timelineDropdownOptions[0]);
    }
  };

  const handleDownload = (transaction) => {
    const dateRanges =
      selectedTimeline?.value === 'customDate' ? storedData?.date : getDateRange(selectedTimeline?.value);
    GlobalService.generalSelect(
      (respdata, error) => {
        if (error) {
          console.error('Download failed:', error);
          alert('Failed to download the report. Please try again.');
          return;
        }

        if (respdata) {
          // Convert response to a Blob with 'text/csv' MIME type
          const blob = new Blob([respdata], { type: 'text/csv' });

          // Create an anchor element to trigger the download
          const link = document.createElement('a');
          link.href = URL.createObjectURL(blob);
          link.download = `Revenue_Report_${
            getDateRange(selectedTimeline?.value)?.startDate
          }-${getDateRange(selectedTimeline?.value)?.endDate}.csv`;
          document.body.appendChild(link);
          link.click();

          URL.revokeObjectURL(link.href);
          document.body.removeChild(link);
          setDownloadModal(false);
        }
      },
      `${resturls.obtainDownloadReportUrl}?start_date=${dateRanges?.startDate}&end_date=${dateRanges?.endDate}${
        transaction ? '&transaction=true' : '&transaction=false'
      }&business_id=${ls.get('selectedBusiness')?.business_id}`,
      {},
      'GET'
    );
  };

  const handleRefresh = () => {
    ls.remove('revenueDetails');
    ls.remove('revenueTrendLineDetails');
    ls.remove('revenueDetailedReport');
    fetchRevenueReportData();
  };

  const commonProps = {
    revenueDetails,
    handleRefresh,
    setDownloadModal,
    handleDropdownList,
    selectedTimeline,
    popupRef,
    handleSelectDropdown,
    setActiveInput,
    activeInput,
    dateInfo,
    handleDateSelection,
    handleCloseDateFilter,
    openDropdown,
    isLoading,
    handlePagination,
    activePage,
    detailedViewLoading,
    detailedReportData,
    handleDetailsPagination,
    activeDetailsPage,
    handleClose,
    handleApplyFilter,
    downloadModal,
    getDateRange,
    handleDownload,
    trendLineDetails,
    setDateInfo,
  };

  if (isResponsive) {
    return (
      <div className={style.mobileViewContainer}>
        <div className={style.backIcon}>
          <span onClickCapture={() => navigate('/reportsMenu')}>
            <BackIcon />
          </span>
        </div>
        <div className={style.rightContentWrapper}>
          <LoadingWrapper loading={isLoading} minHeight={true}>
            <RenderOverallContent {...commonProps} />
          </LoadingWrapper>
        </div>
        {revenueDetails?.data?.length > 0 && (
          <div className={style.downloadBtnWrapper}>
            <button className={style.downloadBtn} onClick={() => setDownloadModal(true)}>
              <DownloadIcon />
              Download Report
            </button>
          </div>
        )}
      </div>
    );
  }

  return (
    <>
      <LoadingWrapper loading={isLoading}>
        <RenderOverallContent {...commonProps} />
      </LoadingWrapper>
    </>
  );
};

export default RevenueReport;

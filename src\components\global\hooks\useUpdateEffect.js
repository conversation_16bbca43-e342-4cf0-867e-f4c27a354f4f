import { useEffect, useRef } from 'react';

/**
 * A custom hook similar to useEffect but skips the initial render.
 * Executes the callback only when dependencies change after the first mount.
 * Useful when you want to avoid side effects during component initialization.
 */
function useUpdateEffect(callback, dependencies) {
  const firstRenderRef = useRef(true);

  useEffect(() => {
    if (firstRenderRef.current) {
      firstRenderRef.current = false;
    } else {
      return callback();
    }
  }, dependencies);
}

export default useUpdateEffect;

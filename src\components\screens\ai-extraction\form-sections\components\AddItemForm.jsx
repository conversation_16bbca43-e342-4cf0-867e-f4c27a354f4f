import React, { use<PERSON><PERSON>back, useMemo } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  Checkbox,
  FormControlLabel,
  Button,
  Radio,
  InputAdornment,
} from '@mui/material';
import { NumericFormat } from 'react-number-format';
import { Formik, Field } from 'formik';
import * as yup from 'yup';
import { useAuth } from '../../../../../contexts/AuthContext';
import { resturls } from '../../../../utils/apiurls';
import { addZohoItems } from '../../../../services/zohoServices';
import { toast } from 'react-toastify';
import { getErrorMessage } from '../../../../utils/apiUtils';
import SearchAutocomplete from '../../../../custom-components/SearchAutocomplete';

// Yup validation schema
const validationSchema = yup.object().shape({
  name: yup.string().required('Name is required'),
  rate: yup.string().when(['can_be_sold'], {
    is: true,
    then: () => yup.string().required('Rate is required'),
  }),
  purchase_rate: yup.string().when(['can_be_purchased'], {
    is: true,
    then: () => yup.string().required('Purchase Rate is required'),
  }),
  hsn_or_sac: yup.string().required('HSN/SAC is required'),
  product_type: yup.string().required('Product type is required'),
  taxability_type: yup.string().required('Tax Preference is required'),
  can_be_sold: yup.boolean(),
  tax_exemption_code: yup.string().when(['taxability_type'], {
    is: (value) => value === 'none',
    then: () => yup.string().required('Tax Exemption Code is required'),
  }),
  account_id: yup.string().when(['can_be_sold'], {
    is: true,
    then: (schema) => schema.required('Account is required'),
  }),
  purchase_account_id: yup.string().when(['can_be_purchased'], {
    is: true,
    then: (schema) => schema.required('Purchase Account is required'),
  }),
  inventory_account_id: yup.string().when(['track_inventory'], {
    is: true,
    then: (schema) => schema.required('Inventory Account is required'),
  }),
  intra_state_tax_id: yup.string().when(['taxability_type'], {
    is: 'taxable',
    then: (schema) => schema.required('Intra State Tax Rate is required'),
    otherwise: () => yup.string().notRequired(),
  }),
  inter_state_tax_id: yup.string().when(['taxability_type'], {
    is: 'taxable',
    then: (schema) => schema.required('Inter State Tax Rate is required'),
    otherwise: () => yup.string().notRequired(),
  }),
  can_be_purchased: yup
    .boolean()
    .test('at-least-one-type', 'Select at least one: Sales Information or Purchase Information', function (value) {
      return value || this.parent.can_be_sold;
    }),
});

// Taxability type options
const taxabilityTypeOptions = [
  { key: 'taxable', text: 'Taxable', value: 'taxable' },
  { key: 'none', text: 'Non-Taxable', value: 'none' },
  { key: 'non_gst_supply', text: 'Non GST Supply', value: 'non_gst_supply' },
  { key: 'out_of_scope', text: 'Out Of Scope', value: 'out_of_scope' },
];

// Inventory valuation methods
const inventoryValuationMethods = [
  { key: 'fifo', text: 'FIFO (First In, First Out)', value: 'fifo' },
  { key: 'wac', text: 'WAC (Weighted Average Cost)', value: 'wac' },
];

function AddItemForm({ overrideInitialValues = null, open, onClose = null, onSave = null }) {
  const { globSelectedBusiness } = useAuth();
  const businessId = globSelectedBusiness?.business_id;

  // Handle form submission
  const handleFormSubmit = useCallback((values, { resetForm }) => {
    // Determine item_type based on selected sections
    const item_type = values.track_inventory
      ? 'inventory'
      : values.can_be_sold && values.can_be_purchased
        ? 'sales_and_purchases'
        : values.can_be_sold
          ? 'sales'
          : 'purchases';

    // Transform form values to match Zoho payload
    const isTaxable = values.taxability_type === 'taxable';
    const payload = {
      name: values.name,
      product_type: values.product_type,
      hsn_or_sac: values.hsn_or_sac,
      unit: values.unit,
      description: values.description,
      is_taxable: isTaxable,
      tax_exemption_code: !isTaxable ? 'GSTMARGINSCHEME' : values.tax_exemption_code || '',
      taxability_type: isTaxable ? 'none' : values.taxability_type,
      item_type,
      // Sales info - only include if sales is selected
      ...(values.can_be_sold && {
        rate: values.rate,
        account_id: values.account_id,
      }),
      // Purchase info - only include if purchase is selected
      ...(values.can_be_purchased && {
        purchase_rate: values.purchase_rate,
        purchase_account_id: values.purchase_account_id,
      }),
      // Inventory info - only include if track inventory is selected
      track_inventory: values.track_inventory,
      ...(values.track_inventory && {
        inventory_account_id: values.inventory_account_id,
        inventory_valuation_method: values.inventory_valuation_method,
      }),
      // Optional fields
      tags: [],
      custom_fields: [],
      // Tax preferences
      item_tax_preferences: isTaxable
        ? [
            {
              tax_id: values.intra_state_tax_id,
              tax_specification: 'intra',
            },
            {
              tax_id: values.inter_state_tax_id,
              tax_specification: 'inter',
            },
          ]
        : [],
      // as backend needs tax_id
      tax_id: '',
    };

    addZohoItems(businessId, payload)
      .then(() => {
        onSave?.(payload);
        resetForm();
        onClose?.();
        toast.success('Item added successfully');
      })
      .catch((err) => {
        const errorMessage = getErrorMessage(err);
        toast.error(errorMessage);
      });
  }, []);

  // Initial form values
  const initialValues = useMemo(() => {
    return {
      name: overrideInitialValues?.item_name ?? '',
      product_type: 'goods',
      unit: overrideInitialValues?.unit ?? '',
      hsn_or_sac: overrideInitialValues?.hsn_sac ?? '',
      taxability_type: 'taxable',
      tax_exemption_code: '',
      description: '',
      // Sales section
      can_be_sold: true,
      rate: '',
      account_id: '',
      // Purchase section
      can_be_purchased: true,
      purchase_rate: overrideInitialValues?.rate ?? '',
      purchase_account_id: '',
      // Inventory section
      track_inventory: false,
      inventory_account_id: '',
      inventory_valuation_method: 'fifo',
      // Tax rates
      intra_state_tax_id: '',
      inter_state_tax_id: '',
    };
  }, [overrideInitialValues]);

  return (
    <Dialog open={open} onClose={() => onClose?.()} maxWidth="md" fullWidth>
      <DialogTitle>Add New Item</DialogTitle>
      <DialogContent className="!pt-3">
        <Formik
          initialValues={initialValues}
          validationSchema={validationSchema}
          onSubmit={handleFormSubmit}
          enableReinitialize
          validateOnMount={true}
        >
          {({ values, errors, touched, handleSubmit, setFieldValue, setFieldTouched }) => (
            <>
              <form onSubmit={handleSubmit} id="add-item-form" className="relative">
                {/* Type Selection */}
                <div className="flex items-center gap-2 mb-4">
                  <label className="block mr-5">Type: </label>
                  <FormControlLabel
                    control={
                      <Radio
                        checked={values.product_type === 'goods'}
                        onChange={() => setFieldValue('product_type', 'goods')}
                        className="!text-accent2"
                      />
                    }
                    label="Goods"
                  />
                  <FormControlLabel
                    control={
                      <Radio
                        checked={values.product_type === 'services'}
                        onChange={() => setFieldValue('product_type', 'services')}
                        className="!text-accent2"
                      />
                    }
                    label="Service"
                  />
                </div>

                {/* Basic Information */}
                <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-4">
                  <Field name="name">
                    {({ field }) => (
                      <TextField
                        {...field}
                        label="Name"
                        placeholder="Enter item name"
                        fullWidth
                        required
                        error={!!(errors.name && touched.name)}
                        size="small"
                      />
                    )}
                  </Field>

                  <Field name="unit">
                    {({ field }) => (
                      <TextField {...field} label="Unit" placeholder="e.g., pcs, kg, m" fullWidth size="small" />
                    )}
                  </Field>

                  <Field name="hsn_or_sac">
                    {({ field }) => (
                      <TextField
                        {...field}
                        label="HSN/SAC"
                        placeholder="Enter HSN/SAC code"
                        fullWidth
                        required
                        error={!!(errors.hsn_or_sac && touched.hsn_or_sac)}
                        size="small"
                      />
                    )}
                  </Field>

                  <Field name="taxability_type">
                    {({ field }) => (
                      <FormControl fullWidth size="small">
                        <InputLabel>Tax Preference</InputLabel>
                        <Select
                          {...field}
                          label="Tax Preference"
                          required
                          onChange={(e) => {
                            const newValue = e.target.value;
                            setFieldValue('taxability_type', newValue);

                            // Clear tax-related fields when switching to non-taxable
                            if (newValue !== 'taxable') {
                              setFieldValue('intra_state_tax_id', '');
                              setFieldValue('inter_state_tax_id', '');
                              setFieldTouched('intra_state_tax_id', false);
                              setFieldTouched('inter_state_tax_id', false);
                            }

                            // Set default tax exemption code for non-taxable
                            if (newValue === 'none') {
                              setFieldValue('tax_exemption_code', 'GSTMARGINSCHEME');
                            } else {
                              setFieldValue('tax_exemption_code', '');
                              setFieldTouched('tax_exemption_code', false);
                            }
                          }}
                        >
                          {taxabilityTypeOptions.map((option) => (
                            <MenuItem key={option.key} value={option.value}>
                              {option.text}
                            </MenuItem>
                          ))}
                        </Select>
                      </FormControl>
                    )}
                  </Field>
                </div>

                <div className="grid grid-cols-1 gap-4 mb-4">
                  {values.taxability_type === 'none' && (
                    <Field name="tax_exemption_code">
                      {({ field }) => (
                        <TextField
                          {...field}
                          label="Tax Exemption Code"
                          placeholder="Enter tax exemption code"
                          fullWidth
                          required
                          error={!!(errors.tax_exemption_code && touched.tax_exemption_code)}
                          size="small"
                        />
                      )}
                    </Field>
                  )}
                </div>

                {/* Default Tax Rates */}
                {values.taxability_type === 'taxable' && (
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                    <Field name="intra_state_tax_id">
                      {({ field }) => (
                        <SearchAutocomplete
                          {...field}
                          fullWidth
                          required
                          url={`${resturls.getGstLedgers}?business_id=${businessId}&gst_type=tax_group`}
                          error={!!(errors.intra_state_tax_id && touched.intra_state_tax_id)}
                          size="small"
                          onSelect={(value) => setFieldValue('intra_state_tax_id', value?.value ?? '')}
                          label="Intra State Tax Rate"
                          searchParam="search"
                          transformOptions={{
                            key: 'master_id',
                            label: 'ledger',
                            value: 'master_id',
                          }}
                        />
                      )}
                    </Field>

                    <Field name="inter_state_tax_id">
                      {({ field }) => (
                        <SearchAutocomplete
                          {...field}
                          fullWidth
                          required
                          url={`${resturls.getGstLedgers}?business_id=${businessId}&gst_type=tax`}
                          error={!!(errors.inter_state_tax_id && touched.inter_state_tax_id)}
                          size="small"
                          onSelect={(value) => setFieldValue('inter_state_tax_id', value?.value ?? '')}
                          label="Inter State Tax Rate"
                          searchParam="search"
                          transformOptions={{
                            key: 'master_id',
                            label: 'ledger',
                            value: 'master_id',
                          }}
                        />
                      )}
                    </Field>
                  </div>
                )}

                {/* Sales Information */}
                <>
                  <FormControlLabel
                    control={
                      <Checkbox
                        checked={values.can_be_sold}
                        onChange={(e) => {
                          const newValue = e.target.checked;
                          setFieldValue('can_be_sold', newValue);

                          // If unchecking sales and inventory is tracked, uncheck inventory tracking
                          if (!newValue && values.track_inventory) {
                            setFieldValue('track_inventory', false);
                            setFieldValue('inventory_account_id', '');
                            setFieldValue('inventory_valuation_method', 'fifo');
                            setFieldTouched('inventory_account_id', false);
                          }
                        }}
                        className="!text-accent2"
                      />
                    }
                    label="Sales Information"
                  />

                  {values.can_be_sold && (
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
                      <NumericFormat
                        customInput={TextField}
                        thousandSeparator={true}
                        decimalScale={2}
                        fixedDecimalScale
                        placeholder="0.00"
                        value={values.rate}
                        onValueChange={(values) => setFieldValue('rate', values.value)}
                        onBlur={() => setFieldTouched('rate', true)}
                        label="Selling Price"
                        fullWidth
                        required
                        size="small"
                        error={!!(errors.rate && touched.rate)}
                        slotProps={{
                          input: {
                            startAdornment: <InputAdornment position="start">INR</InputAdornment>,
                          },
                        }}
                      />

                      <Field name="account_id">
                        {({ field }) => (
                          <SearchAutocomplete
                            {...field}
                            fullWidth
                            required
                            url={`${resturls.getPurchaseLedger}/search?business_id=${businessId}`}
                            error={!!(errors.account_id && touched.account_id)}
                            size="small"
                            onSelect={(value) => setFieldValue('account_id', value?.value ?? '')}
                            label="Account"
                            searchParam="ledger_name"
                            transformOptions={{
                              key: 'master_id',
                              label: 'ledger_name',
                              value: 'master_id',
                            }}
                          />
                        )}
                      </Field>

                      <Field name="description">
                        {({ field }) => (
                          <TextField
                            {...field}
                            label="Description"
                            placeholder="Sales description"
                            fullWidth
                            multiline
                            rows={2}
                            size="small"
                            className="md:col-span-2"
                          />
                        )}
                      </Field>
                    </div>
                  )}
                </>

                {/* Purchase Information */}
                <>
                  <FormControlLabel
                    control={
                      <Checkbox
                        checked={values.can_be_purchased}
                        onChange={(e) => {
                          const newValue = e.target.checked;
                          setFieldValue('can_be_purchased', newValue);

                          // If unchecking purchase and inventory is tracked, uncheck inventory tracking
                          if (!newValue && values.track_inventory) {
                            setFieldValue('track_inventory', false);
                            setFieldValue('inventory_account_id', '');
                            setFieldValue('inventory_valuation_method', 'fifo');
                            setFieldTouched('inventory_account_id', false);
                          }
                        }}
                        className="!text-accent2"
                      />
                    }
                    label="Purchase Information"
                  />

                  {values.can_be_purchased && (
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
                      <NumericFormat
                        customInput={TextField}
                        thousandSeparator={true}
                        decimalScale={2}
                        fixedDecimalScale
                        placeholder="0.00"
                        value={values.purchase_rate}
                        onValueChange={(values) => setFieldValue('purchase_rate', values.value)}
                        label="Cost Price"
                        fullWidth
                        required
                        size="small"
                        error={!!(errors.purchase_rate && touched.purchase_rate)}
                        slotProps={{
                          input: {
                            startAdornment: <InputAdornment position="start">INR</InputAdornment>,
                          },
                        }}
                      />

                      <Field name="purchase_account_id">
                        {({ field }) => (
                          <SearchAutocomplete
                            {...field}
                            fullWidth
                            required
                            url={`${resturls.getPurchaseLedger}/search?business_id=${businessId}`}
                            error={!!(errors.purchase_account_id && touched.purchase_account_id)}
                            size="small"
                            onSelect={(value) => setFieldValue('purchase_account_id', value?.value ?? '')}
                            label="Account"
                            searchParam="ledger_name"
                            transformOptions={{
                              key: 'master_id',
                              label: 'ledger_name',
                              value: 'master_id',
                            }}
                          />
                        )}
                      </Field>

                      <Field name="description">
                        {({ field }) => (
                          <TextField
                            {...field}
                            label="Description"
                            placeholder="Purchase description"
                            fullWidth
                            multiline
                            rows={2}
                            size="small"
                            className="md:col-span-2"
                          />
                        )}
                      </Field>
                    </div>
                  )}
                  {!values.can_be_sold && !values.can_be_purchased && (
                    <div className="text-red-500 text-sm mt-1">{errors.can_be_purchased}</div>
                  )}
                </>

                {/* Inventory Tracking */}
                {values.product_type === 'goods' && values.can_be_sold && values.can_be_purchased && (
                  <>
                    <FormControlLabel
                      control={
                        <Checkbox
                          checked={values.track_inventory}
                          onChange={(e) => setFieldValue('track_inventory', e.target.checked)}
                          className="!text-accent2"
                        />
                      }
                      label="Track Inventory"
                    />

                    {values.track_inventory && (
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4 mb-4">
                        <Field name="inventory_account_id">
                          {({ field }) => (
                            <SearchAutocomplete
                              {...field}
                              fullWidth
                              required
                              url={`${resturls.getPurchaseLedger}/search?business_id=${businessId}&ledger_type=stock`}
                              error={!!(errors.inventory_account_id && touched.inventory_account_id)}
                              size="small"
                              onSelect={(value) => setFieldValue('inventory_account_id', value?.value ?? '')}
                              label="Inventory Account"
                              searchParam="ledger_name"
                              transformOptions={{
                                key: 'master_id',
                                label: 'ledger_name',
                                value: 'master_id',
                              }}
                            />
                          )}
                        </Field>

                        <Field name="inventory_valuation_method">
                          {({ field }) => (
                            <FormControl fullWidth size="small">
                              <InputLabel>Inventory Valuation Method</InputLabel>
                              <Select {...field} label="Inventory Valuation Method">
                                {inventoryValuationMethods.map((option) => (
                                  <MenuItem key={option.key} value={option.value}>
                                    {option.text}
                                  </MenuItem>
                                ))}
                              </Select>
                            </FormControl>
                          )}
                        </Field>
                      </div>
                    )}
                  </>
                )}
              </form>

              <DialogActions>
                <Button onClick={() => onClose?.()} color="inherit">
                  Cancel
                </Button>
                <Button
                  onClick={() => handleSubmit()}
                  variant="contained"
                  className="!bg-accent2 !text-white hover:!bg-accent2-hover"
                  type="submit"
                >
                  Add Item
                </Button>
              </DialogActions>
            </>
          )}
        </Formik>
      </DialogContent>
    </Dialog>
  );
}

export default React.memo(AddItemForm);

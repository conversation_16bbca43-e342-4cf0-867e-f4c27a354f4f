import React, { useState, useRef } from 'react';
import { Button, Modal, Box, Typography, Alert } from '@mui/material';
import { Upload } from 'lucide-react';
import useDragAndDrop from '../global/hooks/useDragAndDrop';

function UploadBtn({ onFilesSelected, multiple = false, acceptedFileTypes = '.csv', className = '' }) {
  const [open, setOpen] = useState(false);
  const [files, setFiles] = useState([]);
  const [message, setMessage] = useState(null);
  const fileInputRef = useRef(null);

  const handleClose = () => {
    setOpen(false);
    setFiles([]);
    setMessage(null);
  };

  const handleDropFiles = (droppedFiles) => {
    const validFiles = validateFiles(droppedFiles);
    if (validFiles.length > 0) {
      setFiles(validFiles);
    }
  };

  const { isDragActive, eventHandlers } = useDragAndDrop(handleDropFiles);

  const validateFiles = (fileList) => {
    const filesArray = Array.from(fileList);

    if (!multiple && filesArray.length > 1) {
      setMessage({ type: 'error', text: 'Only one file can be uploaded' });
      return [];
    }

    const validFiles = filesArray.filter((file) => {
      if (acceptedFileTypes !== '*') {
        const fileType = file.type;
        const fileExtension = file.name.split('.').pop().toLowerCase();
        const acceptedTypes = acceptedFileTypes.split(',').map((type) => type.trim());

        const isValidType = acceptedTypes.some((type) => {
          if (type.startsWith('.')) {
            return `.${fileExtension}` === type;
          }
          return fileType.includes(type);
        });

        if (!isValidType) {
          setMessage({ type: 'error', text: `File type not supported: ${file.name}` });
          return false;
        }
      }

      return true;
    });

    if (validFiles.length > 0) {
      setMessage({ type: 'success', text: `${validFiles.length} file(s) ready to upload` });
    }

    return validFiles;
  };

  const handleFileChange = (e) => {
    if (e.target.files && e.target.files.length > 0) {
      const validFiles = validateFiles(e.target.files);
      if (validFiles.length > 0) {
        setFiles(validFiles);
      }
    }
  };

  const handleSubmit = () => {
    if (files.length > 0) {
      onFilesSelected && onFilesSelected(multiple ? files : files[0]);
      handleClose();
    }
  };

  return (
    <>
      <Button variant="contained" startIcon={<Upload size={16} />} onClick={() => setOpen(true)} className={className}>
        Upload
      </Button>

      <Modal open={open} onClose={handleClose} {...eventHandlers} aria-labelledby="upload-modal-title">
        <Box
          className={`absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 bg-white rounded-lg shadow-xl p-6 w-full max-w-md transition-colors ${
            isDragActive ? 'bg-blue-50 border-2 border-blue-500' : ''
          }`}
        >
          <Typography id="upload-modal-title" variant="h6" component="h2" className="mb-4">
            Upload Files
          </Typography>

          <div
            className={`border-2 border-dashed rounded-lg p-8 mb-4 text-center cursor-pointer transition-colors ${
              isDragActive ? 'border-blue-500 bg-blue-100' : 'border-gray-300 hover:border-blue-400'
            }`}
            onClick={() => fileInputRef.current.click()}
          >
            <Upload className="mx-auto h-12 w-12 text-gray-400 mb-2" />
            <Typography variant="body1" className="mb-1">
              {isDragActive ? 'Drop files here' : 'Drag and drop files here'}
            </Typography>
            <Typography variant="body2" color="textSecondary">
              or <span className="text-blue-500 font-medium">browse files</span>
            </Typography>
            <input
              ref={fileInputRef}
              type="file"
              multiple={multiple}
              accept={acceptedFileTypes}
              onChange={handleFileChange}
              className="hidden"
            />
          </div>

          {message && (
            <Alert severity={message.type} className="mb-4">
              {message.text}
            </Alert>
          )}

          {files.length > 0 && (
            <Box className="mb-4 max-h-32 overflow-y-auto">
              <Typography variant="subtitle2" className="mb-1">
                Selected Files:
              </Typography>
              <ul className="list-disc pl-5">
                {files.map((file, index) => (
                  <li key={index} className="text-sm">
                    {file.name} ({(file.size / 1024).toFixed(1)} KB)
                  </li>
                ))}
              </ul>
            </Box>
          )}

          <Box className="flex justify-end gap-2">
            <Button onClick={handleClose} color="inherit">
              Cancel
            </Button>
            <Button onClick={handleSubmit} variant="contained" disabled={files.length === 0}>
              Upload
            </Button>
          </Box>
        </Box>
      </Modal>
    </>
  );
}

export default UploadBtn;

@import '../../../assets//scss/main.scss';

.previewContainer {
  display: flex;
  gap: 10px;
  margin: 2em 0;
  flex-wrap: wrap;
  align-items: center;
  max-height: 23em;
  padding: 0 1.5em;
  overflow-y: auto !important;
  img {
    height: 5em !important;
    border-radius: 5px;
  }
  .fileLabel {
    height: 2.5em;
    background-color: $white;
    box-shadow: 0 0 5px 1px rgb(231, 231, 231);
  }
  .previewWrapper {
    position: relative;
    cursor: pointer;
    .loader {
      position: absolute;
      left: 29px;
      top: 24px;
    }
  }
}

.commonLoader {
  margin-top: 2em !important;
}

.mainContainer {
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  padding: 2em 0;
  @include for_media(mobileScreen) {
    padding: 0em 0em 7em 0em;
  }
}

.formWrapper {
  display: flex;
  padding: 3em;
  width: 85%;
  box-shadow: 0 0 10px 1px rgb(209, 209, 209);
  border-radius: 10px;
  margin: 5em;
  @include for_media(bigTabletScreen) {
    width: 75%;
  }
  @include for_media(mobileScreen) {
    width: 95%;
    padding: 0 1em;
    box-shadow: none;
    margin: 0em;
  }
  .form {
    width: 100%;
    .formContent {
      width: 100%;
      flex-wrap: wrap;
      display: flex;
      gap: 2em;
      @include for_media(mobileScreen) {
        display: unset !important;
      }
    }
    @include for_media(mobileScreen) {
      display: unset;
      gap: 1em;
    }
    .formField {
      // margin: 2.5em 0;
      width: 47%;
      .disabledField {
        color: rgb(168, 168, 168);
        border: 1px solid rgb(224, 224, 224);
        padding: 0.5em 0.6em;
        font-size: 1.1em;
      }
      @include for_media(mobileScreen) {
        margin: 2em 0;
        width: 100%;
      }
      @include for_media(tabletScreen) {
        width: auto;
      }
      @include for_media(bigTabletScreen) {
        width: 100%;
      }
      label {
        margin: 1em 0 !important;
      }
      input,
      textarea {
        background-color: #f5f5f5 !important;
        color: $black !important;
        border: 1px solid #e9eaeb;
        border-radius: 10px;
      }
      input {
        height: 3.5em;
      }
      textarea {
        height: 7em;
      }
      label {
        font-size: 1.2em !important;
        @include for_media(mobileScreen) {
          font-size: 1em !important;
          font-weight: 400;
        }
      }
    }
    .fileField {
      background-color: $white;
      box-shadow: 0 0 2px 2px #ebecf3;
      padding: 1em;
      hr {
        width: 85%;
        // border-color: #E9EAEB;
      }
    }
    .fileInput {
      display: flex;
      align-items: center;
      cursor: pointer;
      justify-content: center;
      border-radius: 7px;
      gap: 2em;
      border-radius: 10px;

      @include for_media(mobileScreen) {
        //    margin: 1em 0;
      }
      span {
        // margin-left: 10px;
        color: black !important;
        font-size: 1.2em;
        @include for_media(mobileScreen) {
          font-size: 1em;
        }
      }
      div {
        font-size: 0.9em !important;
        margin: 0;
        //   padding: 1em;
        //   width: 50%;
        //   min-height: 14em;
        border-radius: 5px;
        text-align: center;
        //   background-color: #D7D7D7;

        label {
          display: flex;
          flex-direction: column;
          justify-content: center;
          gap: 1em;
          svg {
            width: 30px;
            height: 30px;
          }
          .iconContainer {
            background-color: #e9eaeb;
            border-radius: 100%;
            padding: 0.7em;
          }
        }
      }
      .activeCameraContainer {
        width: 100%;
        padding: 0;
      }
      .videoContent {
        display: none;
      }
      .activeCamera {
        display: block;
        width: 100%;
        height: 100%;
      }
      .disableLabel {
        display: none;
      }
      .captureBtn {
        width: 100%;
        padding: 1em;
        text-align: center;
        cursor: pointer;
        display: flex;
        justify-content: center;
        align-items: center;
        gap: 1em;
        font-size: 1.4em;
      }
    }
    .customFileInput {
      display: flex;
      align-items: center;
    }
    button {
      // width: 100%;
      height: 3.5em;
      background-color: $primaryColor;
      @include for_media(mobileScreen) {
        background-color: $newVarient;
        font-size: 1.1em;
        height: 3em;
        border-radius: 10px;
      }
    }

    .priorityList {
      display: flex;
      gap: 1em;
      span {
        padding: 0.5em 1em;
        border-radius: 35px;
        cursor: pointer;
        border: 2px solid $accentBorder1;
        background-color: $accentBgColor1;
      }
      .activeItem {
        background-color: $accentColor2;
        border: none;
        color: $white;
      }
    }
    .fileInstuction {
      font-size: 1.1em !important;
    }
  }

  .disabledField {
    input {
      font-size: 1.1em !important;
    }
  }
}

.buttonWrapper {
  margin: 3em 0 0 0;
  display: flex;
  justify-content: flex-end;
  @include for_media(mobileScreen) {
    margin: 2em 0 0 0;
    .submitBtn {
      width: 100% !important;
      font-size: 1.5em;
      border-radius: 35px !important;
      color: #363636 !important;
    }
  }
  .submitBtn {
    width: 30%;
    background-color: $accentColor2 !important;
    color: $primaryBgColor !important;
    border-radius: 12px;
    font-size: 1.2em;
  }
  .submitBtn:hover {
    background-color: $accentHover2 !important;
  }
}

.modalForm {
  margin: 0;
  width: 100%;
}

.backIcon {
  display: flex;
  justify-content: flex-start;
  position: relative;
  height: 0em;
  width: 100%;
  padding: 0 2em;
  @include for_media(mobileScreen) {
    padding: 0.5em 1.5em;
    bottom: 1em;
  }
  svg {
    width: 50px;
    height: 50px;
    @include for_media(mobileScreen) {
      width: 35px;
      height: 35px;
    }
  }
}

.closeIconWrapper {
  display: none;
}
.fileUploadContainer {
  flex-direction: column;
  .closeIconWrapper {
    display: block;
    margin: 0;
    height: 15px;
    display: flex;
    justify-content: flex-end;
    width: 100%;
  }
}

.fileInput,
.fileUploadContainer,
.disableLabel {
  -webkit-tap-highlight-color: transparent;
}

.fileInput:focus,
.fileUploadContainer:focus,
.disableLabel:focus {
  outline: none;
  box-shadow: none;
}

.pdfLabe {
  padding: 1em 0.5em;
}

.downloadIcon {
  cursor: pointer;
}

.completedPopup {
  // height: 0;
  // visibility: hidden;
  padding: 2em;
  display: flex;
  flex-direction: column;
  // position: relative;
  // z-index: 1000;
  background-color: #ffffff;
  width: 100%;
  justify-content: center;
  align-items: center;
  gap: 1.5em;
  box-shadow: 0 0 1px 1px #d7d7d7;
  border-radius: 10px;
  @include for_media(mobileScreen) {
    transform: translateY(100%);
    transition: all 1s ease;
    padding: 2em 2em 4em 2em;
    position: fixed;
    bottom: 0;
    border-radius: 30px 30px 0 0;
  }

  p {
    margin: 0;
  }
  h4 {
    font-size: 1.5em !important;
  }
  .cmptIconWrapper {
    height: 100px;
    width: 100px;
    // background-color: #EBEBEB;
    border-radius: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
  }
  .cmptContent {
    width: 100%;
    padding: 1em;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background-color: #f5f5f5;
    box-shadow: 0 0 1px 2px #e9eaeb;
    border-radius: 10px;
    p,
    span {
      margin: 0;
      font-size: 1.1em !important;
    }
    span {
      cursor: pointer;
    }
  }
  .cmptBtn {
    width: 100%;
    background-color: $yellowColor !important;
    font-size: 1.3em !important;
    font-family: $primaryFont !important;
    color: #7e6607;
    border-radius: 35px;
  }
}

.completedModal {
  // height: 50dvh;
  transform: translateY(0%);
  z-index: 1000;
  // visibility: visible;
}

.pdfPreview {
  display: flex;
  align-items: center;
  width: 100%;
  border: 1px solid #e6e6e6;
  gap: 2em;
  padding-right: 1em;
  min-width: 19em;
  padding: 0.5em;
  border-radius: 10px;
  img {
    width: 25px !important;
    height: 25px !important;
  }
  p {
    font-size: 1em !important;
    margin: 0;
    max-width: 75%;
    overflow: hidden;
    text-overflow: ellipsis;
    text-align: center;
    width: 75%;
  }
  .pdfImgWrapper {
    padding: 0.5em;
    background-color: #fbe6e4;
    border-radius: 7px;
    width: 3em;
  }
}

.pdfPreviewWrapper {
  width: 100% !important;
}

.detailsFeild {
  padding: 1em 1em 2em 1em;
  background-color: $white;
  width: 100%;
  border-radius: 10px;
  box-shadow: 0 0 2px 2px #ebecf3;
  margin: 1em 0;
}

.detailLabel {
  font-weight: 900 !important;
}

.closeIconSvg {
  right: 2px;
  position: absolute;
  z-index: 10;
  height: 18px;
  width: 18px;
  top: 2px;
}

.extraPdf {
  .closeIconSvg {
    right: 10px;
    position: absolute;
    z-index: 10;
    height: 22px;
    width: 22px;
    top: 15px;
    rect {
      fill: gray !important;
    }
  }
}

.ticketCreation {
  width: 100%;
  min-height: 89vh;
  padding: 2.5em 2em;
  display: flex;
  gap: 2em;
  .navigationWrapper {
    width: 20%;
    height: 83vh;
  }
  .rightContent {
    width: 80%;
  }
}

////////////////

.formConatiner {
  .formContent {
    display: flex;
    margin: 2em 0;
    gap: 2em;
  }
  .detailLabel {
    font-size: 1.3em !important;
  }
  .fileInstuction {
    font-size: 1.1em !important;
    color: #717680 !important;
  }
  .leftForm {
    width: 50%;
    height: 100%;
  }
  .fileField {
    padding: 2em;
    border-radius: 15px;
    background-color: $white;
    height: 100%;
  }
  .fileInputContainer {
    padding: 1em;
    border: 2px dashed rgb(224, 224, 224);
    border-radius: 10px;
    height: 15em;
    display: flex;
    justify-content: center;
    background-color: #fafafa;
    align-items: center;
    cursor: pointer;
  }
  .customFileInput {
    display: flex;
    flex-direction: column;
    gap: 1em;
    align-items: center;
    .fileIcon {
      width: 50px;
      height: 50px;
    }
    .uploadBtn {
      display: flex;
      justify-content: center;
      align-items: center;
      gap: 0.5em;
      height: 2.5em;
      padding: 0 1em;
      background-color: $accentBgColor1;
      border-radius: 35px;
      color: $primaryColor;
      border: 1px solid $accentBorder1;
      font-size: 1.3em;
      svg {
        width: 20px;
        height: 20px;
      }
    }
  }
  .rightForm {
    width: 50%;
    .detailsFeild {
      min-height: 55vh;
      padding: 2em;

      input,
      textarea {
        background-color: #f5f5f5 !important;
        color: $black !important;
        border: 1px solid #e9eaeb;
        border-radius: 10px;
      }
      input {
        height: 3.5em;
      }
      textarea {
        height: 7em;
      }
      label {
        font-size: 1.2em !important;
        margin: 1em 0 !important;
      }
      .priorityList {
        display: flex;
        gap: 1em;
        span {
          padding: 0.5em 1em;
          border-radius: 35px;
          cursor: pointer;
          border: 1px solid $accentBorder1;
          background-color: $accentBgColor1;
        }
        .activeItem {
          background-color: $accentColor2;
          border: none;
          color: $white;
        }
      }
    }
  }
  .buttonWrapper {
    button {
      width: 10em;
      height: 2.6em;
      border-radius: 35px;
      border: 1px solid $accentBorder1;
      background-color: $accentColor2 !important;
      color: $primaryBgColor !important;
    }
    button:hover {
      background-color: $accentHover2 !important;
    }
  }
  .combainWraper {
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;
    gap: 1em;
    .formField {
      width: 48%;
    }
    .assignToField {
      p {
        font-size: 1em !important;
      }
    }
  }
}

.docIcon {
  .pdfImgWrapper {
    background-color: #dbeefd !important;
  }
}

.excelIcon {
  .pdfImgWrapper {
    background-color: #dbece3 !important;
  }
}

.commentScreenForm {
  margin: 1em 0;
  background-color: $white;
  .detailsFeild {
    padding: 1em 1em 2em 1em;
    background-color: $white;
    width: 100%;
    border-radius: 10px;
    box-shadow: none;
    margin: 1em 0;
  }
  .rightForm {
    height: 65vh;
    overflow-y: scroll;
    width: 100%;
    position: relative;
    box-shadow: 0 0 2px 2px #ebecf3;
    border-radius: 10px;
    @include for_media(mobileScreen) {
      height: 55vh;
    }
    .formField {
      // margin: 2.5em 0;
      width: 100%;
      display: flex;
      flex-direction: column;
      .disabledField {
        color: rgb(168, 168, 168);
        border: 1px solid rgb(224, 224, 224);
        padding: 0.5em 0.6em;
        font-size: 1.1em;
      }
      label {
        margin: 1em 0 !important;
      }
      input,
      textarea {
        background-color: #f5f5f5 !important;
        color: $black !important;
        border: 1px solid #e9eaeb;
        border-radius: 10px;
      }
      input {
        height: 3.5em;
      }
      textarea {
        height: 7em;
        padding: 1em;
      }
      label {
        font-size: 1.2em !important;
      }
    }
  }
  .rightForm::-webkit-scrollbar {
    display: none;
  }

  .btnsWrapper {
    width: 100%;
    display: flex;
    justify-content: flex-end;
    align-items: center;
    position: sticky;
    padding: 1em;
    background-color: $white;
    bottom: 0;
    border-top: 1px solid #ebecf3;
    button {
      width: 10em;
      height: 3.3em;
      border-radius: 35px;
      color: #7e6607 !important;
      background-color: #e4b600 !important;
    }
    button:hover {
      color: #000000 !important;
      background-color: #e4b600 !important;
    }
  }
}

.dragging {
  background-color: #e6e6e6 !important;
  border: 1px solid rgb(204, 204, 204);
}

import React, { useState, useEffect } from 'react';
import { Formik, Form, Field, ErrorMessage } from 'formik';
import * as Yup from 'yup';
import style from './scss/login.module.scss';
import forgetPassword from '../assets/Images/forgotPassword.png';
import { Button, Image, Message, Progress, Icon } from 'semantic-ui-react';
import { Link, useNavigate, useParams } from 'react-router-dom'; // Import useNavigate for redirecting
// import logo from '../assets/Images/logo.png'
import GlobalService from './services/GlobalServices';
import { resturls } from './utils/apiurls';
import Header from './global/Header';
import { OtpInput } from 'reactjs-otp-input';
import ls from 'local-storage'; // Importing local-storage library

const ForgetPassword = () => {
  const navigate = useNavigate();
  const [step, setStep] = useState(1); // State to track current step
  const [message, setMessage] = useState(false); // State for error messages
  const [info, setInfo] = useState(false); // State for user info
  const [emailSent, setEmailSent] = useState(false); // State to track if the email has been sent
  const [passwordStrength, setPasswordStrength] = useState(0); // Track password strength
  const [showPassword, setShowPassword] = useState({ new: false, confirm: false });
  const [code, setCode] = useState('');
  const [isResendDisabled, setIsResendDisabled] = useState(false); // State to disable resend
  const [timer, setTimer] = useState(60);
  const [resendCount, setCount] = useState(0);
  const [initialEmail, setInitialEmail] = useState('');
  const { reset } = useParams();
  const userId = ls.get('access_token')?.userId;

  const emailValidationSchema = Yup.object({
    email: Yup.string()
      .matches(/^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/, 'Invalid email address')
      .required('Email is required'),
  });

  useEffect(() => {
    const savedEmail = ls.get('rememberedEmail');
    if (savedEmail) {
      setInitialEmail(savedEmail);
    }
  }, []);

  const codeValidationSchema = Yup.object({
    code: Yup.string().required('Code is required'),
  });

  const passwordValidationSchema = Yup.object({
    newPassword: Yup.string()
      .required('New Password is required')
      .test(
        'password-strength',
        'Your password must be at least 8 characters, and include a letter, a number, an uppercase letter, and a special character (e.g., !@#$%)',
        (value) => {
          if (!value) return false;
          const hasMinLength = value.length >= 8;
          const hasLetterOrNumber = /[A-Za-z0-9]/.test(value);
          const hasSpecialCharacter = /[^A-Za-z0-9]/.test(value);
          const hasUppercase = /[A-Z]/.test(value);

          return hasMinLength && hasLetterOrNumber && hasSpecialCharacter && hasUppercase;
        }
      ),

    confirmPassword: Yup.string()
      .oneOf([Yup.ref('newPassword'), null], 'Passwords must match')
      .required('Confirm Password is required'),
  });

  const calculateStrength = (password) => {
    let strength = 0;
    if (password.length >= 8) strength++;
    if (/[A-Z]/.test(password)) strength++;
    if (/[a-z]/.test(password)) strength++;
    if (/\d/.test(password)) strength++;
    if (/[^A-Za-z0-9]/.test(password)) strength++;

    return strength;
  };

  const handleFieldChange = (e, setFieldValue, otp) => {
    if (otp) {
      setCode(e);
      setFieldValue('code', e);
    } else {
      const { name, value } = e.target;
      setFieldValue(name, value);
      if (name === 'newPassword') {
        setPasswordStrength(calculateStrength(value));
      }
    }
    setEmailSent(false);
    setMessage(false);
  };

  const handleEmailSubmit = (values, { resetForm }) => {
    const obj = {
      email: values?.email,
    };
    setEmailSent(true);
    setTimer(60);
    setIsResendDisabled(true);
    GlobalService.generalSelect(
      (respdata) => {
        const { data, msg } = respdata;
        if (msg === 'success') {
          setInfo({ ...data, email: values.email });
          setMessage(false);
          setStep(2);
          resetForm();
        } else {
          setMessage(msg);
        }
      },
      resturls.forgotPassword,
      obj,
      'POST'
    );
  };

  const handleResend = () => {
    const obj = {
      email: info?.email,
    };
    setEmailSent(true);
    setTimer(60);
    setIsResendDisabled(true);
    setCount(resendCount + 1);
    GlobalService.generalSelect(
      (respdata) => {
        const { data, msg } = respdata;
        if (msg === 'success') {
          setInfo({ ...data, email: info.email });
          setMessage(false);
        } else {
          setMessage(msg);
        }
      },
      resturls.forgotPassword,
      obj,
      'POST'
    );
  };

  useEffect(() => {
    if (isResendDisabled && timer > 0) {
      const interval = setInterval(() => {
        setTimer((prev) => prev - 1);
      }, 1000);

      return () => clearInterval(interval);
    }

    if (timer === 0) {
      setIsResendDisabled(false);
    }
  }, [isResendDisabled, timer]);

  const handleCodeSubmit = (values, { resetForm }) => {
    const obj = {
      otp: values?.code,
      user_id: info?.user_id || userId,
    };
    setEmailSent(true);
    GlobalService.generalSelect(
      (respdata) => {
        const { msg } = respdata;
        if (msg === 'success') {
          setMessage(false);
          setStep(3);
          resetForm();
        } else {
          setMessage(msg);
        }
      },
      resturls.verifyOtp,
      obj,
      'POST'
    );
  };

  const handlePasswordSubmit = (values, { resetForm }) => {
    const obj = {
      new_password: values?.newPassword,
      user_id: info?.user_id || userId,
    };
    setEmailSent(true);
    GlobalService.generalSelect(
      (respdata) => {
        const { msg } = respdata;
        if (msg === 'success') {
          setMessage(false);
          setStep(4);
          resetForm();
        } else {
          setMessage(msg);
        }
      },
      resturls.resetPassword,
      obj,
      'POST'
    );
  };

  const getProgressBarColor = () => {
    if (passwordStrength === 5) return 'green';
    if (passwordStrength >= 3) return 'orange';
    return 'red';
  };

  const getStrengthMessage = () => {
    if (passwordStrength === 5) return 'Strong';
    if (passwordStrength >= 3) return 'Medium';
    return 'Weak';
  };

  const renderForm = () => {
    switch (step) {
      case 1:
        return (
          <Formik
            initialValues={{ email: initialEmail || '' }}
            validationSchema={emailValidationSchema}
            onSubmit={handleEmailSubmit}
            key={initialEmail}
          >
            {({ isSubmitting, isValid, dirty, setFieldValue }) => (
              <Form className={style.formContainer}>
                <div className={style.fieldWrapper}>
                  <Field
                    type="email"
                    name="email"
                    placeholder="Email"
                    className={style.formField}
                    onChange={(e) => handleFieldChange(e, setFieldValue)}
                  />
                  <ErrorMessage name="email" component="div" className={style.inputError} />
                </div>
                {message && (
                  <Message className={style.messageContent} negative>
                    {message}
                  </Message>
                )}
                <Button className={style.loginButton} type="submit" disabled={(!isValid && dirty) || emailSent}>
                  Send Code
                </Button>
              </Form>
            )}
          </Formik>
        );

      case 2:
        return (
          <Formik
            initialValues={{ code: '' }}
            validationSchema={codeValidationSchema}
            onSubmit={handleCodeSubmit}
            key={step}
          >
            {({ isSubmitting, isValid, dirty, setFieldValue }) => (
              <Form className={style.formContainer}>
                <div className={`${style.otpContainer} ${style.fieldWrapper}`}>
                  <OtpInput
                    value={code}
                    onChange={(value) => handleFieldChange(value, setFieldValue, true)}
                    numInputs={6}
                    separator={<span className={style.seperator}> </span>}
                    isInputNum
                    inputStyle={style.formField}
                  />
                  {resendCount <= 2 && (
                    <p className={style.resendLink}>
                      {' '}
                      If you didn't receive the code!{' '}
                      {isResendDisabled ? (
                        <>
                          <span>Resend OTP in {timer}s</span>
                        </>
                      ) : (
                        <span onClickCapture={handleResend}>Resend</span>
                      )}
                    </p>
                  )}
                  <ErrorMessage name="code" component="div" className={style.inputError} />
                </div>
                {message && (
                  <Message className={style.messageContent} negative>
                    {message}
                  </Message>
                )}
                <Button
                  className={style.loginButton}
                  type="submit"
                  disabled={!(isValid && dirty) || code?.length < 6 || emailSent}
                >
                  Verify Code
                </Button>
              </Form>
            )}
          </Formik>
        );

      case 3:
        return (
          <Formik
            initialValues={{ newPassword: '', confirmPassword: '' }}
            validationSchema={passwordValidationSchema}
            onSubmit={handlePasswordSubmit}
            key={step}
          >
            {({ isSubmitting, isValid, dirty, setFieldValue }) => (
              <Form className={style.formContainer}>
                <div className={style.fieldWrapper}>
                  <div className={style.formFieldWrapper}>
                    <Field
                      type={showPassword?.new ? 'text' : 'password'}
                      name="newPassword"
                      placeholder="New Password"
                      className={style.formField}
                      onChange={(e) => handleFieldChange(e, setFieldValue)}
                    />
                    <Icon
                      name={!showPassword?.new ? 'eye slash' : 'eye'}
                      // link
                      onClick={() => setShowPassword({ ...showPassword, new: !showPassword?.new })}
                      className={style.eyeIcon}
                    />
                  </div>

                  {passwordStrength > 0 && (
                    <Progress
                      percent={(passwordStrength / 5) * 100}
                      color={getProgressBarColor()}
                      size="tiny"
                      className={style.progressBar}
                    />
                  )}
                  {passwordStrength > 0 && (
                    <p className={style.pswdMsg} style={{ color: getProgressBarColor() }}>
                      {getStrengthMessage()}
                    </p>
                  )}
                  <ErrorMessage name="newPassword" component="div" className={style.inputError} />
                </div>
                <div className={style.fieldWrapper}>
                  <div className={style.formFieldWrapper}>
                    <Field
                      type={showPassword?.confirm ? 'text' : 'password'}
                      name="confirmPassword"
                      placeholder="Confirm Password"
                      className={style.formField}
                      onChange={(e) => handleFieldChange(e, setFieldValue)}
                    />
                    <Icon
                      name={!showPassword?.confirm ? 'eye slash' : 'eye'}
                      onClick={() => setShowPassword({ ...showPassword, confirm: !showPassword?.confirm })}
                      className={style.eyeIcon}
                    />
                  </div>
                  <ErrorMessage name="confirmPassword" component="div" className={style.inputError} />
                </div>
                {message && (
                  <Message className={style.messageContent} negative>
                    {message}
                  </Message>
                )}
                <Button className={style.loginButton} type="submit" disabled={!(isValid && dirty) || emailSent}>
                  Reset Password
                </Button>
              </Form>
            )}
          </Formik>
        );

      case 4:
        return (
          <Message positive>
            <Message.Header>
              <p>Password Reset Successful</p>
            </Message.Header>
            <p>
              Your password has been reset successfully. Go back to the{' '}
              <Link to="/login" className={style.loginText}>
                Login
              </Link>{' '}
              page.
            </p>
          </Message>
        );

      default:
        return null;
    }
  };

  // const handleBack = (value) => {
  //   setStep(value);
  //   setMessage(false);
  //   setEmailSent(false);
  // }

  // const renderBackBtn = () => {
  //   if(step === 2){
  //     return <p className={style.backToLogin} onClickCapture={() => handleBack(1)}><Icon size='large' name="angle left" />Back</p>
  //   }
  //   if(step === 3){
  //     return <p className={style.backToLogin} onClickCapture={() => handleBack(2)}><Icon size='large' name="angle left" />Back</p>
  //   }
  //   if(step === 4){
  //     return <></>;
  //   }
  //  return <p className={style.backToLogin} ><Link to="/login"><Icon size='large' name="angle left" />Back</Link></p>

  // }

  const handleBack = () => {
    navigate('/login');
  };

  const handleChangeBack = (value) => {
    setStep(value);
    setMessage(false);
    setEmailSent(false);
  };

  const renderBackBtn = () => {
    if (step === 2) {
      return (
        <p className={style.backToLogin} onClickCapture={() => handleChangeBack(1)}>
          <Icon size="large" name="angle left" />
          Back
        </p>
      );
    }
    if (step === 3) {
      return (
        <p className={style.backToLogin} onClickCapture={() => handleChangeBack(1)}>
          <Icon size="large" name="angle left" />
          Back
        </p>
      );
    }
    if (step === 4) return null;

    return (
      <p className={style.backToLogin} onClickCapture={handleBack}>
        <Icon size="large" name="angle left" />
        Back
      </p>
    );
  };

  const renderTitle = () => {
    switch (step) {
      case 1:
        return reset ? (
          <div className={style.titleMsg}>
            <h4>Create a new Password</h4>
            <p>
              Enter your email address below, and we’ll send you a verification code to proceed with setting up your
              account.
            </p>
          </div>
        ) : (
          <div className={style.titleMsg}>
            <h4>Forgot Password</h4>
            <p>Enter your email address below, and we’ll send you a verification code to reset your password</p>
          </div>
        );
      case 2:
        return (
          <div className={style.titleMsg}>
            <h4>Verify Code</h4>
            <p>
              A verification code has been sent to <span>{info?.email}</span>. Enter it below to continue resetting your
              password.
            </p>
          </div>
        );
      case 3:
        return (
          <div className={style.titleMsg}>
            <h4>Reset Password</h4>
            <p>Enter your new password below to complete the reset process.</p>
          </div>
        );
      case 4:
        return <></>;
      default:
        return <h4>Forgot Password</h4>;
    }
  };

  return (
    <div className={style.mainContainer}>
      <Header withOutUser={true} />
      <div className={style.contentWrapper}>
        <div className={`${style.leftContainer} ${style.forotScreenImg}`}>
          <Image src={forgetPassword} size="massive" />
        </div>
        <div className={`${step === 4 && style.alignCenter} ${style.rightContainer}`}>
          {renderBackBtn()}
          {renderTitle()}
          {renderForm()}
        </div>
      </div>
    </div>
  );
};

export default ForgetPassword;

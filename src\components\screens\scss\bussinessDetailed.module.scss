@import '../../../assets//scss/main.scss';

.dropdown {
  height: 2.5em;
  padding: 0em 1em;
  display: flex !important;
  align-items: center;
  border-radius: 35px;
  justify-content: space-evenly;
}

.statusDropdown {
  max-width: 14em;
}

.openStatus {
  background-color: #eaecf5 !important;
  color: #4e5ba6 !important;
  div {
    color: #4e5ba6 !important;
  }
  path {
    fill: #4e5ba6;
  }
}

.pendingStatus {
  background-color: #fdf7dd !important;
  color: #7e6607;
  div {
    color: #7e6607;
  }
  svg {
    path {
      fill: #7e6607;
    }
  }
}

.closedStatus {
  background-color: #f2d7d5 !important;
  color: #c0392b;
  div {
    color: #c0392b;
  }
  svg {
    path {
      fill: #c0392b;
    }
  }
}

.verifiedStatus {
  background-color: #e3f8d9 !important;
  color: #2e7a31;
  div {
    color: #2e7a31;
  }
  svg {
    path {
      fill: #2e7a31;
    }
  }
}

.deletedStatus {
  background-color: #fee4e2 !important;
  color: #b42318;
  div {
    color: #b42318;
  }
  svg {
    path {
      fill: #b42318;
    }
  }
}

.businessDetails {
  padding: 2em 10%;
  .headerContainer {
    display: flex;
    justify-content: space-between;
    .leftContent {
      display: flex;
      gap: 2em;
      .logo {
        border: 2px solid $borderColor;
        border-radius: 10px;
        width: 45px;
        height: 45px;
        display: flex;
        justify-content: center;
        align-items: center;
        background-color: #f5f5f5;
        svg {
          width: 25px;
          height: 25px;
        }
        img {
          width: 100%;
          height: 100%;
          border-radius: 10px;
        }
        svg,
        path {
          fill: #0a0d12;
        }
      }
    }
    .rightContent {
      display: flex;
      gap: 2em;
      p {
        display: flex;
        margin: 0;
        gap: 0.5em;
        color: #717680;
        cursor: pointer;
      }
    }
  }
}

.backIconWrapper {
  padding: 2em 0;
}

.contentWrapper {
  padding: 2em 0;
  margin: 2em 0;
}
.menuList {
  display: flex;
  gap: 2em;
  .menuItem {
    font-size: 1.3em !important;
    color: #535862;
    display: flex;
    gap: 0.5em;
    align-items: center;
    cursor: pointer;
    span {
      height: 25px;
      width: 35px;
      border-radius: 20px;
      background-color: #e9eaeb;
      color: #717680;
      font-size: 0.8em !important;
      align-items: center;
      display: flex;
      justify-content: center;
    }
  }
  .activeMenu {
    color: #4e5ba6;
    position: relative;
  }
  .activeMenu::after {
    content: '';
    height: 3px;
    width: 100%;
    background-color: #4e5ba6;
    position: absolute;
    bottom: -10px;
  }
}

.detailView {
  padding: 2em;
  border-radius: 15px;
  background-color: $white;
  width: 100%;
  min-height: 35vh;
  margin: 1em 0;
  .detailPart {
    padding: 1em 0;
    .heading {
      font-size: 1.4em !important;
      font-weight: 900;
      color: $black;
      margin-bottom: 0.5em;
    }
    label {
      font-size: 1.3em !important;
      color: #181d27;
      display: block;
    }
    p {
      color: #717680;
      margin-top: 0.5em;
      display: flex;
      align-items: center;
      gap: 0.5em;
      word-break: break-word;
      svg {
        width: 20px;
        height: 20px;
        cursor: pointer;
      }
    }
    .detail {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 2em;
    }
  }
}

.timelineTable {
  min-width: 120% !important;
  td {
    font-size: 1.2em !important;
  }
}

.tableWrapper {
  padding: 1em 0;
  max-height: 50vh;
  overflow-y: auto;
  height: 40%;
  overflow-x: auto;
  -ms-overflow-style: none; /* For IE and Edge */
  scrollbar-width: none; /* For Firefox */
  table {
    min-width: 100%;
    // border-spacing: 0 15px !important;
    thead {
      background-color: #f5f5f5 !important;
      height: 5em !important;
    }
    th {
      color: #717680 !important;
      // text-align: center !important;
      padding: 1em !important;
      font-size: 1.2rem !important;
    }
    td {
      padding: 1em !important;
      // text-align: center !important;
      color: #717680;
    }
    .subjectheaderRow {
      text-align: start !important;
      padding: 1em !important;
    }
    .subjectRow {
      text-align: start !important;
      display: flex;
      align-items: center;
      gap: 1em;
      padding: 1em !important;
      font-weight: 900;
      font-size: 1rem !important;
      .logo {
        border: 2px solid $borderColor;
        border-radius: 10px;
        width: 45px;
        height: 45px;
        display: flex;
        justify-content: center;
        align-items: center;
        background-color: #f5f5f5;
        svg {
          width: 25px;
          height: 25px;
        }
        img {
          width: 100%;
          height: 100%;
          border-radius: 10px;
        }
        svg,
        path {
          fill: #0a0d12;
        }
      }
    }
    tbody {
      tr {
        background-color: $white;
        border-radius: 20px !important;
      }
    }
  }
}

.tableWrapper::-webkit-scrollbar {
  display: none; /* For Chrome, Safari, and Opera */
}

.checkbox {
  label::before {
    height: 25px !important;
    width: 25px !important;
  }
  label::after {
    top: 4px !important;
    width: 25px !important;
    height: 19px !important;
    color: $white !important;
  }
}

.contactInfo {
  display: flex;
  align-items: center;
  gap: 2em;
  p {
    margin: 0;
  }
  .iconWrapper {
    display: flex;
    gap: 1em;
    span {
      width: 40px;
      height: 40px;
      background-color: #eaecf5;
      border-radius: 100%;
      // padding: 1em;
      display: flex;
      justify-content: center;
      align-items: center;
      svg {
        width: 27px !important;
        height: 27px !important;
      }
      cursor: pointer;
    }
  }
}

.dotContainer {
  display: flex;
  justify-content: flex-end;
  width: 100%;
}

.searchInput {
  width: 35% !important;
  height: 3.5em !important;
  // border: 1px solid #E9EAEB;
  border-radius: 10px;
  box-shadow: 0 0 1px 1px #e9eaeb;
  input {
    background-color: #fdfdfd !important;
    color: #717680 !important;
    // border: 1px solid #E9EAEB;
    border-radius: 10px !important;
  }
}

.addBtn {
  display: flex;
  gap: 1em;
  align-items: center;
  width: 10em;
  height: 2.5em;
  border-radius: 35px;
  border: 1px solid #d5d7da;
  justify-content: center;
  color: $white;
  background-color: #4e5ba6;
  font-size: 1.4em;
  cursor: pointer;
  svg {
    width: 25px;
    height: 25px;
  }
}
.memebersList {
  padding: 1em 0;
  background-color: $white;
  border-radius: 20px;
  box-shadow: 0 0 1px 2px $borderColor;
  margin: 1em 0;
}

.inputWrapper {
  display: flex;
  justify-content: flex-end;
  padding: 1em 1.5em;
}

.downloadBtn {
  display: flex;
  gap: 1em;
  align-items: center;
  width: 10em;
  height: 2.5em;
  border-radius: 35px;
  border: 1px solid #d5d7da;
  justify-content: center;
  color: #293056;
  font-size: 1.4em;
  svg {
    width: 25px;
    height: 25px;
  }
}
.downloadBtn2 {
  display: flex;
  gap: 1em;
  align-items: center;
  width: 10em;
  height: 2.5em;
  border-radius: 35px;
  border: 1px solid #d5d7da;
  justify-content: center;
  color: #293056;
  background-color: #eaecf5;
  font-size: 1.4em;
  svg {
    width: 25px;
    height: 25px;
  }
}

.fileWrapper {
  display: flex;
  gap: 1em;
  align-items: center;
  img {
    width: 75px;
    height: 75px;
  }
  p {
    color: $black;
    font-weight: 400 !important;
  }
}

.id {
  color: $black;
  font-size: 1.2em !important;
}

.category {
  height: 2em;
  display: flex;
  align-items: center;
  // padding: 0 2em;
  background-color: #e9eaeb;
  color: #535862;
  justify-content: center;
  margin: 0;
  width: auto !important;
  border-radius: 10px;
}

.uploadInfo {
  margin: 0;
  color: $black;
}
.uploadRole {
  font-size: 1.1em;
}

.heading {
  display: flex;
  margin-bottom: 1em !important;
  border-bottom: 2px solid #edededd0;
  padding-bottom: 0.2em;
  span {
    display: inline-block;
    color: #4e5ba6;
    text-decoration: underline;
    cursor: pointer;
  }
}

.modalContent {
  min-height: 62vh;
  background-color: $white;
  border-radius: 25px !important;
  h5 {
    font-size: 1.3em !important;
    padding: 1em 1.6em;
    color: black;
  }

  .formContainer {
    margin: 0 0 2em 0;
    margin: 0 2em 2em 2em;
    .formField {
      margin: 2em 0;
    }
    input,
    textarea {
      background-color: #f5f5f5 !important;
      color: $black !important;
      box-shadow: 0 0 1px 1px #e9eaeb;
      border-radius: 13px !important;
      border: none !important;
    }
    input {
      height: 3.5em;
    }
    textarea {
      height: 7em;
    }
    label {
      font-size: 0.9em !important;
      margin: 0.5em 0 !important;
      font-weight: 400 !important;
      font-weight: 900 !important;
    }
  }
}

.formWrapper {
  min-height: 54vh;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.buttonWrapper {
  display: flex;
  width: 100%;
  justify-content: space-between;
  padding: 2em;
  gap: 2em;
  border-top: 1px solid #717680;
  .nextBtn {
    background-color: #4e5ba6 !important;
    color: $white !important;
    padding: 0.8em 2em !important;
    border-radius: 35px !important;
    font-size: 1.2em !important;
  }
  .cancel {
    background-color: #eaecf5 !important;
    border: 1px solid #9ea5d1;
    border-radius: 35px !important;
    font-size: 1.2em !important;
    padding: 0.8em 2em !important;
  }
  .backBtn {
    background-color: #eaecf5 !important;
    border: 1px solid #9ea5d1;
    border-radius: 35px !important;
    font-size: 1.2em !important;
    padding: 0.8em 2em !important;
  }
}

.memberModal {
  min-height: auto !important;
  .formWrapper {
    min-height: auto !important;
  }
}

.selctedUsers {
  display: flex;
  gap: 1em;
  flex-wrap: wrap;

  p {
    margin: 0;
    display: flex;
    align-items: center;
    gap: 1em;
    padding: 0 2em;
    height: 2em;
    background-color: #011638;
    color: $white;
    border-radius: 25px;
  }
}

.modalContentdelete {
  min-height: 62vh;
  background-color: $white;
  border-radius: 25px !important;
  h5 {
    font-size: 1.3em !important;
    padding: 1em 1.6em;
    color: black;
    margin: 0;
  }

  .formContainer {
    margin: 0 0 2em 0;
    margin: 0 2em 2em 2em;
    .formField {
      margin: 2em 0;
    }
    input,
    textarea {
      background-color: #f5f5f5 !important;
      color: $black !important;
      box-shadow: 0 0 1px 1px #e9eaeb;
      border-radius: 13px !important;
      border: none !important;
    }
    input {
      height: 3.5em;
    }
    textarea {
      height: 7em;
    }
    label {
      font-size: 1.1em !important;
      margin: 0.5em 0 !important;
      font-weight: 400 !important;
    }
  }
}

.redBtn {
  background-color: #f04438 !important;
  // background-color: #F04438 !important;
  border-radius: 20px !important;
  color: #ffff !important;
}

.autoHeight {
  min-height: auto !important;
  .formWrapper {
    min-height: auto !important;
  }
}
.imgWrapper {
  padding: 1em 2em;
  display: flex;
  justify-content: center;
}
.descContent {
  padding: 0 2em;
  text-align: center;
  margin: 1em 0;
  h5 {
    margin: 0.5em !important;
    padding: 0 !important ;
  }
  p {
    color: #717680;
  }
}

img.ui.preview_files {
  height: 50px !important;
  width: 50px !important;
  border-radius: 15px !important;
}

.progressWrapper {
  padding: 2em;
  .progress {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 0.8em;
  }
  .line {
    width: 65%;
    height: 2px;
    background-color: #d5d7da;
    position: relative;
    top: 5px;
    z-index: 0;
  }
  .dot {
    position: relative;
  }
  .dot::before {
    width: 10px;
    height: 10px;
    background-color: #d5d7da;
    border-radius: 100%;
    content: '';
    position: absolute;
  }
  .enableDot::before {
    background-color: #4e5ba6;
    // position: relative;
    z-index: 20;
  }
  .enableDot::after {
    content: '';
    position: absolute;
    height: 30px;
    width: 30px;
    border-radius: 100%;
    border: 2px solid #eaecf5;
    left: -10px;
    top: -10px;
  }
  .activeDot::after {
    background-color: #eaecf5;
    z-index: 10;
  }
}

.labelWrapper {
  display: flex;
  justify-content: center;
  position: relative;
  padding: 1em;
  top: 1em;
  .firstLable {
    padding-left: 2em;
  }
  .label {
    display: flex;
    justify-content: space-between;
    width: 88%;
    span {
      color: black;
    }
  }
}

.emptyTable {
  padding: 2em 1em;
  text-align: center;
  width: 100%;
  font-size: 1.3em !important;
  color: #717680;
  span {
    color: $black;
  }
}

.eyeBtn {
  display: flex;
  gap: 1em;
  align-items: center;
  cursor: pointer;
  svg {
    width: 25px;
    height: 25px;
  }
  span {
    font-size: 1.3em !important;
    font-weight: 900;
    color: #717680;
  }
}

.paginationWrapper {
  padding: 1em;
}

.ticketInfoHeader {
  display: flex;
  gap: 1em;
  .icon {
    svg {
      width: 45px;
      height: 45px;
    }
  }
}

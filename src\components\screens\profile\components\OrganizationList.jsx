import { Image, Pagination } from 'semantic-ui-react';
import { OrganizationIcon, RigthArrowIcon } from '../../../../assets/svgs';
import style from './scss/OrganizationList.module.scss';
import OrganizationListSkeleton from '../../../skeletons/OrganizationListSkeleton';

const OrganizationList = ({
  organisationList,
  totalItemsCount,
  currentPage,
  totalPages,
  setPage,
  loading,
  handleSwitchingScreen,
}) => {
  return (
    <div className={style.orgListWrapper}>
      <p>Organisations {` (${totalItemsCount})`}</p>

      {!loading ? (
        <>
          {organisationList.map((data) => (
            <div className={style.orgItem} key={data.id}>
              <div className={style.leftContent}>
                <div className={style.logo}>{data?.image ? <Image src={data?.image} /> : <OrganizationIcon />}</div>
                <div className={style.content}>
                  <p className={style.name}>{data?.name}</p>
                </div>
              </div>
              <p className={style.viewDetail} onClickCapture={() => handleSwitchingScreen(data)}>
                View details <RigthArrowIcon />
              </p>
            </div>
          ))}
          {totalPages > 1 && (
            <div className={style.paginationWrapper}>
              <Pagination
                activePage={currentPage}
                totalPages={totalPages}
                onPageChange={(e, { activePage }) => setPage(activePage)}
              />
            </div>
          )}
        </>
      ) : (
        <OrganizationListSkeleton count={5} />
      )}
    </div>
  );
};

export default OrganizationList;

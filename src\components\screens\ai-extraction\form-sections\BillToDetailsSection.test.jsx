import React from 'react';
import { render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import BillToDetailsSection from './BillToDetailsSection';
import { sectionValidation } from '../../../services/aiServices';
import { removeObjectsFromJson } from '../../../utils/jsonUtils';

jest.mock('../../../services/aiServices', () => ({
  sectionValidation: jest.fn(),
}));

jest.mock('react-router-dom', () => ({
  ...jest.requireActual('react-router-dom'),
  useParams: () => ({
    fileId: 'mock-file-id',
    businessId: 'mock-business-id',
  }),
}));

const mockFormData = {
  bill_to_details: {
    buyer_name: 'Parameshwara Jewellers',
    buyer_gst_no: '29**********1ZN',
    buyer_pan_no: '**********',
    buyer_address: '123 Test Street',
    buyer_state_code: '29',
    buyer_state_name: 'Karnataka',
    purchase_ledger_name: 'Purchase A/c',
    platform: 'TallyPrime',
    invoice_no: 'DNJ/521/23-24',
    invoice_date: '02/08/2023',
    buyer_country: 'India',
    cost_center_name: 'Main',
    tally_invoice_date: '20230802',
    cost_center_category: null,
  },
};

let latestFormData = null;

const FormWrapper = ({ initialFormData, ...restProps }) => {
  const [formData, setFormData] = React.useState(initialFormData);
  latestFormData = formData;
  const formAction = React.useCallback((action, section, field, value) => {
    let updatedSection;

    setFormData((prevFormData) => {
      const prevSection = prevFormData[section] || {};
      switch (action) {
        case 'FIELD_CHANGE':
          updatedSection = { ...prevSection, [field]: value };
          break;

        case 'UPDATE_SECTION':
          updatedSection = { ...prevSection, ...value };
          break;

        case 'HARD_UPDATE_SECTION':
          updatedSection = value;
          break;

        default:
          return prevFormData;
      }
      return {
        ...prevFormData,
        [section]: updatedSection,
      };
    });

    return updatedSection;
  }, []);

  return (
    <BillToDetailsSection
      formData={formData}
      setFormData={setFormData}
      isReadOnly={false}
      invoiceType="purchase"
      formAction={formAction}
      {...restProps}
    />
  );
};

describe('BillToDetailsSection', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders all fields with correct initial values', () => {
    render(<FormWrapper initialFormData={mockFormData} />);

    expect(screen.getByLabelText("Buyer's Name")).toHaveValue('Parameshwara Jewellers');
    expect(screen.getByLabelText("Buyer's GST No.")).toHaveValue('29**********1ZN');
    expect(screen.getByLabelText("Buyer's PAN No.")).toHaveValue('**********');
    expect(screen.getByLabelText("Buyer's Address")).toHaveValue('123 Test Street');
    expect(screen.getByLabelText("Buyer's State Code")).toHaveValue('29');
    expect(screen.getByLabelText("Buyer's State Name")).toHaveValue('Karnataka');
    expect(screen.getByLabelText('Cost Center')).toHaveValue('Main');
    expect(screen.getByLabelText('Invoice No.')).toHaveValue('DNJ/521/23-24');
    expect(screen.getByLabelText('Invoice Date (dd/mm/yyyy)')).toHaveValue('02/08/2023');
  });

  it('shows initial suggestions when available', () => {
    const formDataWithSuggestions = {
      bill_to_details: {
        ...mockFormData.bill_to_details,
        recommended_fields: [
          {
            buyer_name: 'New Company',
            buyer_gst_no: 'NEWGST123',
          },
        ],
      },
    };

    render(<FormWrapper initialFormData={formDataWithSuggestions} />);
    expect(screen.getByTestId('copy-icon-buyer_name')).toBeInTheDocument();
    expect(screen.getByTestId('paste-icon-buyer_name')).toBeInTheDocument();
    expect(screen.getByTestId('copy-icon-buyer_gst_no')).toBeInTheDocument();
    expect(screen.getByTestId('paste-icon-buyer_gst_no')).toBeInTheDocument();
  });

  it('shows initial error messages & indicators correctly', async () => {
    const formDataWithErrors = {
      bill_to_details: {
        ...mockFormData.bill_to_details,
        error: {
          buyer_gst_no: {
            short_message: 'Invalid GST number',
            long_message: 'Please enter a valid GST number',
          },
          buyer_name: {
            short_message: 'Invalid name',
            long_message: 'Please enter a valid company name',
          },
        },
      },
    };

    render(<FormWrapper initialFormData={formDataWithErrors} />);

    const errorIcons = screen.getAllByTestId('errorIcon');
    expect(errorIcons).toHaveLength(2);

    const gstTooltip = await screen.findByTestId('tooltip-buyer_gst_no');
    await userEvent.hover(gstTooltip);
    const tooltip = await screen.findByText('Invalid GST number');
    expect(tooltip).toBeInTheDocument();

    const nameTooltip = screen.getByTestId('tooltip-buyer_name');
    await userEvent.hover(nameTooltip);
    const nameTooltipContent = await screen.findByText('Invalid name');
    expect(nameTooltipContent).toBeInTheDocument();
  });

  it('shows initial warning messages & indicators correctly', async () => {
    const formDataWithWarnings = {
      bill_to_details: {
        ...mockFormData.bill_to_details,
        warning: {
          buyer_gst_no: {
            short_message: 'GST number might be incorrect',
            long_message: 'Please verify the GST number',
          },
          buyer_address: {
            short_message: 'Address format might be incorrect',
            long_message: 'Please verify the address format',
          },
        },
      },
    };

    render(<FormWrapper initialFormData={formDataWithWarnings} />);

    const warningIcons = screen.getAllByTestId('warningIcon');
    expect(warningIcons).toHaveLength(2);

    const gstTooltip = await screen.findByTestId('tooltip-buyer_gst_no');
    await userEvent.hover(gstTooltip);
    const gstTooltipContent = await screen.findByText('GST number might be incorrect');
    expect(gstTooltipContent).toBeInTheDocument();

    const addressTooltip = await screen.findByTestId('tooltip-buyer_address');
    await userEvent.hover(addressTooltip);
    const addressTooltipContent = await screen.findByText('Address format might be incorrect');
    expect(addressTooltipContent).toBeInTheDocument();
  });

  it('shows initial exact match indicators when present', async () => {
    const formDataWithSuccess = {
      bill_to_details: {
        ...mockFormData.bill_to_details,
        exact_match: {
          buyer_gst_no: true,
          buyer_name: true,
          buyer_pan_no: true,
          buyer_address: true,
          buyer_state_code: true,
          buyer_state_name: true,
        },
      },
    };

    render(<FormWrapper initialFormData={formDataWithSuccess} />);

    const successIcons = screen.getAllByTestId('successIcon');
    expect(successIcons).toHaveLength(6);
  });

  it('handles suggestion paste functionality correctly', async () => {
    const formDataWithSuggestions = {
      bill_to_details: {
        ...mockFormData.bill_to_details,
        recommended_fields: [
          {
            buyer_name: 'Updated Company Name',
            buyer_gst_no: 'Updated GST Number',
          },
        ],
      },
    };

    render(<FormWrapper initialFormData={formDataWithSuggestions} />);

    const namePasteIcon = screen.getByTestId('paste-icon-buyer_name');
    const gstPasteIcon = screen.getByTestId('paste-icon-buyer_gst_no');

    await userEvent.click(namePasteIcon);
    await waitFor(() => {
      expect(screen.getByLabelText("Buyer's Name")).toHaveValue('Updated Company Name');
    });

    await userEvent.click(gstPasteIcon);
    await waitFor(() => {
      expect(screen.getByLabelText("Buyer's GST No.")).toHaveValue('Updated GST Number');
    });
  });

  it('validates field on input change and blur', async () => {
    const newValue = 'New Company Name';
    render(<FormWrapper initialFormData={mockFormData} />);

    const mockResponse = {
      bill_to_details: {
        ...mockFormData.bill_to_details,
        buyer_name: newValue,
        error: {
          buyer_name: {
            short_message: 'Invalid company name',
            long_message: 'Please enter a valid company name',
          },
        },
      },
    };

    sectionValidation.mockResolvedValue(mockResponse);

    const nameInput = screen.getByLabelText("Buyer's Name");
    await userEvent.clear(nameInput);
    await userEvent.type(nameInput, newValue);
    await userEvent.tab();

    expect(nameInput).toHaveValue(newValue);

    const expectedPayload = {
      bill_to_details: {
        ...removeObjectsFromJson(mockFormData.bill_to_details, [
          'error',
          'warning',
          'exact_match',
          'recommended_fields',
        ]),
        buyer_name: newValue,
      },
    };

    expect(sectionValidation).toHaveBeenCalledWith(
      'mock-file-id',
      'mock-business-id',
      'bill_to_details',
      'purchase',
      expectedPayload
    );

    await waitFor(() => {
      expect(screen.getByTestId('errorIcon')).toBeInTheDocument();
    });
  });
});

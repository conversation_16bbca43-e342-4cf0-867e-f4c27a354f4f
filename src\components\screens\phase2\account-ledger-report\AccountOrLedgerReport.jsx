import React, { useState, useEffect, useRef } from 'react';
import style from './scss/accountOrLedgerReport.module.scss';
import { Button } from 'semantic-ui-react';
import { mediaBreakpoint } from '../../../global/MediaBreakPointes';
import GlobalService from '../../../services/GlobalServices';
import { resturls } from '../../../utils/apiurls';
import ls from 'local-storage';
import { useNavigate } from 'react-router-dom';
import { timelineDropdownOptions } from '../../../utils/constants';
import RenderDownloadModal from '../../../utils/reportUtils/RenderDownloadModal';
import { getDateRange, formatDate, formatDateLastUpdate, formatAmount } from '../../../utils/dateUtils';
import { BackIcon, DownloadIcon } from '../../../../assets/svgs';
import RenderOverallContent from './components/RenderOverallContent';
import { LoadingWrapper } from '../../../global/components';
import ReportSortFilter from '../../../global/components/ReportSortFilter';

const AccountOrLedgerReport = () => {
  const navigate = useNavigate();

  const [selectedTimeline, setTimeline] = useState(timelineDropdownOptions[0]);
  const [selectedAccountType, setAccountType] = useState([]);
  const [openDropdown, setOpenDropdown] = useState(false);
  const [accountTypeDropdown, setAccountTypeDropdown] = useState(false);
  const [downloadModal, setDownloadModal] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [dateInfo, setDateInfo] = useState({ startDate: '', endDate: '' });
  const isResponsive = mediaBreakpoint?.mobile > window.innerWidth;
  const [activeInput, setActiveInput] = useState({
    startDate: false,
    endDate: false,
  });
  const [searchTerm, setSearchTerm] = useState('');
  const [transactionSearch, setTransactionSearch] = useState('');
  const [sortDropdown, setSortDropdown] = useState(false);
  const [accountTypeOptions, setAccountTypeOptions] = useState([]);
  const [overViewContent, setOverViewContent] = useState([]);
  const [transactionDetails, setTransactionDetails] = useState({});
  const [activeFilterPage, setActiveFilterPage] = useState(1);
  const [selectedSort, setSelectedSort] = useState(null);
  const [originalTransactionDetails, setOriginalTransactionDetails] = useState(transactionDetails);
  const [transactionActivePage, setTransactionActivePage] = useState(1);
  const [appliedAccountList, setAppliedAccountList] = useState([]);
  const business_id = ls.get('selectedBusiness')?.business_id;

  const popupRef = useRef(null);
  const accountPopupRef = useRef(null);
  const sortPopupRef = useRef(null);

  const handleOutsideClick = (event) => {
    if (popupRef.current && !popupRef.current.contains(event.target) && !isResponsive) {
      if (selectedTimeline?.value === 'customDate') {
        if (dateInfo?.startDate && dateInfo?.endDate) {
          setOpenDropdown(false);
          return;
        }
        if (dateInfo?.startDate || dateInfo?.endDate) {
          return; // Prevent closing if at least one date is selected
        }
        setTimeline(timelineDropdownOptions[0]);
        const dateRanges = getDateRange(timelineDropdownOptions[0]?.value);
        handleFilter(dateRanges, false, timelineDropdownOptions[0]);
        setActiveInput({
          startDate: false,
          endDate: false,
        });
      }

      setOpenDropdown(false);
    }
  };

  useEffect(() => {
    document.addEventListener('mousedown', handleOutsideClick); // Add event listener
    return () => {
      document.removeEventListener('mousedown', handleOutsideClick); // Cleanup event listener
    };
  }, [selectedTimeline, dateInfo]);

  const handleOutsideClickAccount = (event) => {
    if (accountPopupRef.current && !accountPopupRef.current.contains(event.target) && !isResponsive) {
      setAccountTypeDropdown(false);
      setAccountType(appliedAccountList);
    }
  };

  useEffect(() => {
    document.addEventListener('mousedown', handleOutsideClickAccount); // Add event listener
    return () => {
      document.removeEventListener('mousedown', handleOutsideClickAccount); // Cleanup event listener
    };
  }, [selectedAccountType]);

  const handleOutsideClickSort = (event) => {
    if (sortPopupRef.current && !sortPopupRef.current.contains(event.target) && !isResponsive) {
      setSortDropdown(false);
    }
  };

  useEffect(() => {
    document.addEventListener('mousedown', handleOutsideClickSort); // Add event listener
    return () => {
      document.removeEventListener('mousedown', handleOutsideClickSort); // Cleanup event listener
    };
  }, [selectedSort]);

  const ledgerReportList = ls.get('ledgerReportList');
  const overviewInfo = ls.get('overviewInfo');
  const transactionInfo = ls.get('transactionInfo');
  const ledgerTimeline = ls.get('ledgerTimeline');

  const checkExpiryOfData = () => {
    const activeAccountList = ls.get('activeAccountList');
    if (ledgerReportList) {
      const { data, expiry_at, page } = ledgerReportList;
      const expiryDate = new Date(expiry_at);
      const currentDate = new Date();
      // Check if the stored data has expired
      if (expiryDate < currentDate) {
        // If expired, remove it from local storage and make a new API call
        ls.remove('ledgerReportList');
        obtainLedgerReportList();
      } else {
        setAccountTypeOptions(data);
        setActiveFilterPage(page);
        setAccountType(activeAccountList || []);
        setAppliedAccountList(activeAccountList || []);
        if (overviewInfo) setOverViewContent(overviewInfo || []);
        if (transactionInfo) {
          setTransactionDetails(transactionInfo);
          setOriginalTransactionDetails(transactionInfo);
          setTransactionActivePage(transactionInfo?.pagination?.page);
        }

        if (ledgerTimeline) {
          const timeLine = ledgerTimeline?.timeline || timelineDropdownOptions[0];
          setTimeline(timeLine);
          if (ledgerTimeline?.timeline?.value === 'customDate') {
            setDateInfo(ledgerTimeline?.date);
          }
        }

        // if(activeAccountList?.length > 0){

        // }
      }
    } else {
      // If there's no data in local storage, fetch new data
      obtainLedgerReportList();
    }
  };

  const obtainLedgerReportList = (page, search) => {
    const searchItem = search !== undefined ? search : searchTerm;
    // Construct query parameters dynamically
    const params = new URLSearchParams();
    if (page) params.append('page', page);
    if (searchItem) params.append('ledger_name', searchItem);
    if (business_id) params.append('business_id', business_id);

    const apiUrl = `${resturls.obtainLedgerList}?${params.toString()}`;

    GlobalService.generalSelect(
      (respdata) => {
        if (respdata?.data) {
          const { expiry_at, data } = respdata;

          const formattedData = {
            ...respdata,
            data: [
              ...(data?.length > 1
                ? [
                    {
                      text: `Select All (${data.length})`,
                      value: `all${page || 1}`,
                    },
                  ]
                : []),
              ...data?.map((item) => ({
                text: item.ledger_name,
                value: item.ledger_id,
              })),
            ],
          };

          const dataToStore = {
            data: formattedData,
            expiry_at,
            page: page || activeFilterPage,
          };

          setAccountTypeOptions(formattedData);

          if (!search) {
            ls.set('ledgerReportList', dataToStore);
          }
        }
      },
      apiUrl,
      {},
      'GET'
    );
  };

  const fetchLedgerBalanceData = (queryParams, date) => {
    if (!selectedAccountType?.length) {
      setOverViewContent([]);
      return;
    } // Prevent unnecessary calls

    let dateRanges = selectedTimeline?.value === 'customDate' ? dateInfo : getDateRange(selectedTimeline?.value);
    const idList = selectedAccountType
      .filter((info) => String(info.value).startsWith('all') === false) // Ensure value is a string
      .map((info) => info.value)
      .join(',');

    const queryParam =
      queryParams ||
      new URLSearchParams({
        ledger_id: idList,
        start_date: dateRanges?.startDate,
        end_date: dateRanges?.endDate,
        business_id,
      }).toString();

    setIsLoading(true);

    const finalDates = date || dateRanges;

    GlobalService.generalSelect(
      (respdata) => {
        if (respdata) {
          const closingBalance = `${formatAmount(respdata.closing_balance)} (${respdata?.closing_balance_type})`;
          const openingBalance = `${formatAmount(respdata.opening_balance)} (${respdata?.opening_balance_type})`;
          const obj = [
            {
              title: 'Opening Balance',
              timeStamp: `As on ${formatDateLastUpdate(finalDates?.startDate, true)}`,
              amount: openingBalance,
              className: style.highLightContent,
            },
            {
              title: 'Total Debit',
              amount: formatAmount(respdata.total_debit),
              className: style.negativeValue,
            },
            {
              title: 'Total Credit',
              amount: formatAmount(respdata.total_credit),
              className: style.positionValue,
            },
            {
              title: 'Closing Balance',
              timeStamp: `As on ${formatDateLastUpdate(finalDates?.endDate, true)}`,
              amount: closingBalance,
              className: `${style.highLightContent} ${
                respdata.closing_balance >= respdata.opening_balance ? style.positionValue : style.negativeValue
              }`,
              line: true,
            },
          ];
          setOverViewContent(obj || []);
          ls.set('overviewInfo', obj);
          setAppliedAccountList(selectedAccountType);
          ls.set('activeAccountList', selectedAccountType);
          setIsLoading(false);
        }
      },
      `${resturls.obtainLedgerBalanceInfo}?${queryParam}`,
      {},
      'GET'
    );
  };

  const obtainLedgerDetailedData = (queryParams) => {
    if (!selectedAccountType?.length) {
      // setTransactionDetails([]);
      return;
    }

    let dateRanges = selectedTimeline?.value === 'customDate' ? dateInfo : getDateRange(selectedTimeline?.value);
    const idList = selectedAccountType
      .filter((info) => String(info.value).startsWith('all') === false) // Ensure value is a string
      .map((info) => info.value)
      .join(',');

    const queryParam =
      queryParams ||
      new URLSearchParams({
        ledger_id: idList,
        start_date: dateRanges?.startDate,
        end_date: dateRanges?.endDate,
        business_id,
      }).toString();

    GlobalService.generalSelect(
      (respdata) => {
        if (respdata?.data) {
          setTransactionDetails(respdata);
          setOriginalTransactionDetails(respdata);
          setTransactionActivePage(respdata?.pagination?.page);
          ls.set('transactionInfo', respdata);
        }
      },
      `${resturls.obtainLedgerDetailedData}?${queryParam}`,
      {},
      'GET'
    );
  };

  useEffect(() => {
    checkExpiryOfData();
  }, []);

  const handleFilter = (value, page, timeLine) => {
    if (!selectedAccountType?.length || !value?.startDate || !value?.endDate) return;

    const idList = selectedAccountType
      .filter((info) => String(info.value).startsWith('all') === false) // Ensure value is a string
      .map((info) => info.value)
      .join(',');

    const queryParams = new URLSearchParams({
      start_date: value.startDate,
      end_date: value.endDate,
      ledger_id: idList,
      business_id: ls.get('selectedBusiness')?.business_id,
    });

    if (page) queryParams.append('page', page);

    setIsLoading(true);
    fetchLedgerBalanceData(queryParams.toString(), value);
    obtainLedgerDetailedData(queryParams.toString());
  };

  const handleSelectDropdown = (list) => {
    if (list?.value === selectedTimeline?.value) return;

    setTimeline(list);
    ls.set('ledgerTimeline', { timeline: list, date: dateInfo });

    if (list?.value !== 'customDate') {
      setDateInfo({ startDate: '', endDate: '' });
      setActiveInput({ startDate: false, endDate: false });

      if (!isResponsive) {
        setOpenDropdown(false);
        handleFilter(getDateRange(list.value), false, list); // Trigger on selection for desktop
      }
    }
  };

  // Mobile: Handle Apply button click to trigger filter
  const handleApplyFilter = () => {
    if (selectedTimeline?.value !== 'customDate') {
      setDateInfo({ startDate: '', endDate: '' });
      setOpenDropdown(false);
      const dateRanges = getDateRange(selectedTimeline?.value);
      handleFilter(dateRanges, false, selectedTimeline); // Trigger filter after apply button click
    } else {
      if (dateInfo.startDate && dateInfo.endDate) {
        const startDate = formatDate(dateInfo.startDate);
        const endDate = formatDate(dateInfo.endDate);
        const obj = { startDate, endDate };
        handleFilter(obj, false, selectedTimeline); // Trigger filter after apply button click
        setOpenDropdown(false);
      }
    }
  };

  const handleDateSelection = (key, value) => {
    const newDateInfo = { ...dateInfo, [key]: value };

    if (key === 'startDate' && !newDateInfo.endDate) {
      newDateInfo.endDate = new Date().toISOString().split('T')[0];
    }

    setDateInfo(newDateInfo);
    setActiveInput({
      startDate: !!newDateInfo.startDate,
      endDate: !!newDateInfo.endDate,
    });
  };

  const handleCloseDateFilter = () => {
    if (dateInfo?.startDate && dateInfo?.endDate) {
      setOpenDropdown(false);
      const startDate = formatDate(dateInfo.startDate);
      const endDate = formatDate(dateInfo.endDate);
      const obj = { startDate, endDate };
      ls.set('ledgerTimeline', { timeline: selectedTimeline, date: dateInfo });
      handleFilter(obj, false, selectedTimeline);
    }
  };

  const handleAccountDropdown = (selectedItem) => {
    setAccountType((prevSelected = []) => {
      let updatedSelection;
      const selectAllValue = `all${activeFilterPage}`; // Dynamic "Select All" value
      const currentPageItems = accountTypeOptions?.data?.slice(1, 11) || []; // Get only current page items (excluding "Select All")

      if (selectedItem.value === selectAllValue) {
        // Check if all items are already selected
        const isAllSelected = currentPageItems.every((item) =>
          prevSelected.some((selected) => selected.value === item.value)
        );

        // If all are selected, remove them. Otherwise, select all.
        updatedSelection = isAllSelected
          ? prevSelected.filter((item) => !currentPageItems.some((data) => data.value === item.value)) // Remove only current page items
          : [
              ...prevSelected.filter((item) => !currentPageItems.some((data) => data.value === item.value)),
              ...currentPageItems,
            ];

        // Always remove "Select All" if deselecting
        if (isAllSelected) {
          updatedSelection = updatedSelection.filter((item) => item.value !== selectAllValue);
        } else {
          updatedSelection.push({
            text: `Select All (${currentPageItems.length})`,
            value: selectAllValue,
          });
        }
      } else {
        const isSelected = prevSelected.some((item) => item.value === selectedItem.value);

        if (isSelected) {
          // Remove the selected item
          updatedSelection = prevSelected.filter((item) => item.value !== selectedItem.value);

          // Ensure "Select All" is unchecked if any item is removed
          updatedSelection = updatedSelection.filter((item) => item.value !== selectAllValue);
        } else {
          // Add the selected item
          updatedSelection = [...prevSelected, selectedItem];

          // Check if all items (excluding "Select All") on the current page are selected
          const allSelectedNow = currentPageItems.every((item) =>
            updatedSelection.some((sel) => sel.value === item.value)
          );

          // If everything is selected, ensure "Select All" is included
          if (allSelectedNow) {
            updatedSelection.push({
              text: `Select All (${currentPageItems.length})`,
              value: selectAllValue,
            });
          }
        }
      }
      return updatedSelection;
    });
  };

  const handleAccountDropdownApply = () => {
    setAccountTypeDropdown(false);
    fetchLedgerBalanceData();
    obtainLedgerDetailedData();
  };

  const handleClearAccountFilter = () => {
    ls.remove('ledgerReportList');
    ls.remove('overviewInfo');
    ls.remove('transactionInfo');
    ls.remove('ledgerTimeline');
    ls.remove('activeAccountList');
    setOverViewContent([]);
    setTransactionDetails({});
    setAccountType([]);
    setAppliedAccountList([]);
    setAccountTypeDropdown(false);
    setSearchTerm('');
    obtainLedgerReportList(1, false);
  };

  const handleAccountFilterPagination = (page) => {
    setActiveFilterPage(page);
    obtainLedgerReportList(page);
  };

  const handleSelectSortDropdown = (selectedOption) => {
    if (selectedOption?.value === selectedSort?.value) {
      setOpenDropdown(false);
      return;
    }
    setSelectedSort(selectedOption);
  };

  const handleApplySort = () => {
    let sortedData = [...transactionDetails?.data];

    switch (selectedSort?.value) {
      case 'earliestToLatest':
        sortedData.sort((a, b) => new Date(a.date) - new Date(b.date));
        break;
      case 'latestToEarliest':
        sortedData.sort((a, b) => new Date(b.date) - new Date(a.date));
        break;
      case 'lowToHigh':
        sortedData.sort((a, b) => a.amount - b.amount);
        break;
      case 'highToLow':
        sortedData.sort((a, b) => b.amount - a.amount);
        break;
      default:
        break;
    }
    setSortDropdown(false);
    setTransactionDetails({ ...transactionDetails, data: sortedData });
  };

  const handleClear = () => {
    setTransactionDetails(originalTransactionDetails);
    setSelectedSort(false);
    setSortDropdown(false);
  };

  const renderSortPopupContent = () => {
    return (
      <ReportSortFilter
        handleSelectDropdown={handleSelectSortDropdown}
        selectedTimeline={selectedSort}
        handleApplySort={handleApplySort}
        handleClear={handleClear}
      />
    );
  };

  const debounceTimeout = useRef(null);

  const handleSearch = (e) => {
    const { value } = e.target;
    setSearchTerm(value);
    setActiveFilterPage(1);

    // Clear previous timeout before setting a new one
    if (debounceTimeout.current) {
      clearTimeout(debounceTimeout.current);
    }

    debounceTimeout.current = setTimeout(() => {
      obtainLedgerReportList(false, value);
    }, 500); // Adjust debounce delay as needed
  };

  useEffect(() => {
    return () => {
      clearTimeout(debounceTimeout.current);
    };
  }, []);

  const handleDropdownList = () => {
    setOpenDropdown(true);
  };

  // Function to check if the date matches today's date

  const handleRefresh = () => {
    fetchLedgerBalanceData();
    obtainLedgerDetailedData();
    obtainLedgerReportList(activeFilterPage);
  };

  const handleSortDropdown = () => {
    setSortDropdown(true);
  };

  const handleTransactionSearch = (e) => {
    const { value } = e.target;
    const lowerValue = value.toLowerCase();

    if (!value) {
      setTransactionDetails(originalTransactionDetails); // Restore original data
      setTransactionSearch(value);
      return;
    }

    const filteredList = originalTransactionDetails?.data?.filter(
      (info) =>
        info?.journal_number?.toLowerCase().includes(lowerValue) ||
        info?.journal_type?.toLowerCase().includes(lowerValue) ||
        info?.type?.toLowerCase().includes(lowerValue) ||
        info?.amount?.toString().includes(lowerValue)
    );

    setTransactionSearch(value);
    setTransactionDetails({
      ...originalTransactionDetails,
      data: filteredList,
    });
  };

  const handleTransactionPagination = (page) => {
    const idList = selectedAccountType
      .filter((info) => String(info.value).startsWith('all') === false) // Ensure value is a string
      .map((info) => info.value)
      .join(',');

    setTransactionActivePage(page);
    if (selectedTimeline?.value !== 'customDate') {
      const dateRanges = getDateRange(selectedTimeline?.value);
      const queryParams = new URLSearchParams({
        start_date: dateRanges.startDate,
        end_date: dateRanges.endDate,
        ledger_id: idList,
        business_id: ls.get('selectedBusiness')?.business_id,
      });

      if (page) queryParams.append('page', page);
      obtainLedgerDetailedData(queryParams);
    } else {
      const startDate = formatDate(dateInfo.startDate);
      const endDate = formatDate(dateInfo.endDate);
      const queryParams = new URLSearchParams({
        start_date: startDate,
        end_date: endDate,
        ledger_id: idList,
        business_id: ls.get('selectedBusiness')?.business_id,
      });
      obtainLedgerDetailedData(queryParams);
    }
  };

  const handleClose = () => {
    setOpenDropdown(false);
    if (selectedTimeline?.value === 'customDate' && (!dateInfo?.startDate || !dateInfo?.endDate)) {
      setTimeline(timelineDropdownOptions[0]);
    }
  };

  const handleDownload = (transaction) => {
    const dateRanges = selectedTimeline?.value === 'customDate' ? dateInfo : getDateRange(selectedTimeline?.value);
    if (!selectedAccountType?.length) {
      return;
    } // Prevent unnecessary calls
    const idList = selectedAccountType
      .filter((info) => String(info.value).startsWith('all') === false) // Ensure value is a string
      .map((info) => info.value)
      .join(',');

    GlobalService.generalSelect(
      (respdata, error) => {
        // Updated to handle errors
        if (error) {
          console.error('Download failed:', error);
          alert('Failed to download the report. Please try again.');
          return; // Stop execution if error occurs
        }

        if (respdata) {
          // Convert response to a Blob with 'text/csv' MIME type
          const blob = new Blob([respdata], { type: 'text/csv' });

          // Create an anchor element to trigger the download
          const link = document.createElement('a');
          link.href = URL.createObjectURL(blob);
          link.download = `Revenue_Report_${
            getDateRange(selectedTimeline?.value)?.startDate
          }-${getDateRange(selectedTimeline?.value)?.endDate}.csv`;
          document.body.appendChild(link);
          link.click();

          URL.revokeObjectURL(link.href);
          document.body.removeChild(link);

          setDownloadModal(false);
        }
      },
      `${resturls.obtainLedgerDownloadUrl}?start_date=${dateRanges?.startDate}&end_date=${dateRanges?.endDate}${
        transaction ? '&transaction=true' : ''
      }&business_id=${ls.get('selectedBusiness')?.business_id}&ledger_id=${idList}`,
      {},
      'GET'
    );
  };

  const commonProps = {
    accountTypeOptions,
    handleRefresh,
    setDownloadModal,
    transactionDetails,
    accountPopupRef,
    handleSearch,
    searchTerm,
    accountTypeDropdown,
    selectedAccountType,
    handleAccountDropdown,
    handleAccountDropdownApply,
    activeFilterPage,
    handleClearAccountFilter,
    setAccountTypeDropdown,
    handleSelectDropdown,
    setActiveInput,
    handleDateSelection,
    dateInfo,
    activeInput,
    popupRef,
    selectedTimeline,
    handleCloseDateFilter,
    appliedAccountList,
    handleDropdownList,
    openDropdown,
    isLoading,
    overViewContent,
    handleSortDropdown,
    renderSortPopupContent,
    sortDropdown,
    setOpenDropdown,
    handleTransactionSearch,
    transactionSearch,
    transactionActivePage,
    handleTransactionPagination,
    RenderDownloadModal,
    downloadModal,
    handleDownload,
    handleClose,
    handleApplyFilter,
    handleAccountFilterPagination,
    setDateInfo,
  };

  if (isResponsive) {
    return (
      <div className={style.mobileViewContainer}>
        <div className={style.backIcon}>
          <span onClickCapture={() => navigate('/reportsMenu')}>{<BackIcon />}</span>
        </div>
        <div className={style.rightContentWrapper}>{<RenderOverallContent {...commonProps} />}</div>
        {transactionDetails?.data?.length > 0 && (
          <div className={style.downloadBtnWrapper}>
            <button className={style.downloadBtn} onClick={() => setDownloadModal(true)}>
              {<DownloadIcon />}Download Report
            </button>
          </div>
        )}
      </div>
    );
  }

  return (
    <>
      <LoadingWrapper loading={isLoading}>
        <RenderOverallContent {...commonProps} />
      </LoadingWrapper>
    </>
  );
};

export default AccountOrLedgerReport;

@import '../../../assets//scss/main.scss';

.defaultHeader {
  background-color: $primaryBgColor !important;
  position: relative !important;
}
.title {
  font-size: 1.7em !important;
  -webkit-text-stroke: 0.8px;
}
.mainLogoWrappper {
  display: flex;
  gap: 1em;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}
.mainContent {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.4em 2em;
  box-shadow: 0px 0px 3px 0px $grayColor1;
  background-color: $primaryBgColor;
  color: white;
  position: sticky;
  top: 0;
  z-index: 1000;
  @include for_media(mobileScreen) {
    // background-color: $newVarient !important;
    padding: 1.4em 1em;
    margin-bottom: 1em;
    background-color: #f6f8fa;
    z-index: 998 !important;
  }
  .logoWrappper {
    display: flex;
    gap: 1em;
    align-items: center;
    cursor: pointer;
    h4 {
      color: $white;
      margin: 0;
      font-size: 1.6em !important;
    }
    .iconWrapper {
      padding: 0.5em;
      border-radius: 100%;
      // background-color: white;
      border: 1px solid white;
      .logo {
        width: 150px !important;
        height: auto !important;
      }
    }
    .textImg {
      width: 150px !important;
    }
    // .logo{
    //     width: 50px !important;
    // }
  }
  .buttonWrapper,
  button {
    background-color: $white !important;
    border-radius: 10px;
    color: $primaryColor !important;
    text-transform: capitalize !important;
    @include for_media(mobileScreen) {
      background-color: transparent !important;
    }
  }
  button {
    height: 3.2em !important;
    i {
      display: none;
    }
  }
  .dropdown {
    background-color: $white !important;
    height: 3.2em !important;
    color: $primaryColor !important;
    @include for_media(mobileScreen) {
      background-color: transparent !important;
      i {
        color: $black;
      }
    }
    i {
      font-size: 1.3em !important;
    }
  }
  .buttonWrapper {
    p {
      font-size: 1em !important;
      @include for_media(mobileScreen) {
        color: $black;
      }
    }
    i {
      font-size: 1.8em;
      position: relative;
      top: 0.2em;
      left: 0.3em;
    }
    button {
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 0.5em;
    }
  }
  .userNameField {
    display: none !important;
    @include for_media(mobileScreen) {
      display: block !important;
    }
  }
}
.selectedBussiness {
  :nth-child(1) {
    display: flex !important;
    align-items: center;
    font-size: 1.2rem !important;
    @include for_media(mobileScreen) {
      font-size: 1.1rem !important;
      font-weight: 400;
    }
  }
  .dropdownText {
    font-weight: 600;
    @include for_media(mobileScreen) {
      font-size: 1.1rem !important;
      max-width: 170px;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      font-size: 1em !important;
    }
  }
  p {
    font-size: 2rem !important;
    font-weight: 700;
    font-family: $primaryFont !important;
    @include for_media(mobileScreen) {
      font-size: 1em !important;
    }
  }
  .dropdownIcon {
    width: 40px;
    height: 10px;
  }
}

.bussinesHeader {
  background-color: #f6f8fa !important;
  color: $black !important;
  h4 {
    color: $black !important;
  }
}

.bellIcon {
  // padding: 1em;
  border-radius: 15px;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  svg {
    height: 45px !important;
    width: 45px !important;
  }
}

.rightWrapper {
  display: flex;
  align-items: center;
  gap: 1em;
  .avatarProfile {
    border-radius: 10px;
    height: 3em !important;
    width: 3em !important;
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: $accentColor1 !important;
    // border: 1px solid $white;
    cursor: pointer;
    span {
      font-size: 1.2em;
      color: $white;
      @include for_media(mobileScreen) {
        color: #ffffff !important;
      }
    }
    @include for_media(mobileScreen) {
      color: #ffffff !important;
      height: 3em !important;
      width: 3em !important;
    }
  }
}
.popupContent {
  h6 {
    margin: 0.5em 0 !important;
    padding: 0.5em 1em;
  }
  p {
    cursor: pointer;
    padding: 0.5em 1em;
  }
  p:hover {
    background-color: #cfcfcf;
  }
}

.bussinessListConatiner {
  position: absolute;
  left: 0;
  height: 100vh;
  background-color: $white;
  width: 80%;
  transform: translateX(-105%);
  top: 0;
  border-radius: 0 35px 35px 0;
  padding: 2em 1em;
  transition: all 1s;
  .menuItem {
    display: flex;
    gap: 1em;
    align-items: center;
    margin: 1.5em 0;
    padding: 1em;
    background-color: $white;
    border-radius: 20px;
    p {
      font-size: 1.4em;
    }
  }
  .logoWrapper {
    padding: 0.5em;
    box-shadow: 0 0 1px 2px $borderColor;
    border-radius: 10px;
    background-color: $white;
    img,
    svg {
      width: 30px;
      height: 30px;
    }
  }
  .activeMenu {
    background-color: #fefbf0;
    box-shadow: 0 0 0px 1px $yellowColor;
    p {
      -webkit-text-stroke: 0.1px;
    }
  }
}

.activeListContainer {
  z-index: 1000;
  transform: translateX(0);
}
.avatarWrapper {
  border: 3px solid $accentBorder1;
  border-radius: 15px;
}

.adminHeader {
  background-color: #f6f8fa !important;
  .logoWrappper {
    .iconWrapper {
      border-radius: 13px !important;
      box-shadow: 0 0 1px 1px $grayColor1;
    }
    p {
      color: #181d27;
      -webkit-text-stroke: 0.5px;
    }
  }
  .rightWrapper {
    gap: 1em;
  }
}

.logo {
  width: 150px !important;
  height: auto !important;
  @include for_media(mobileScreen) {
    width: 25px !important;
  }
}

.notificationModal {
  width: 100% !important;
  background-color: transparent !important;
  .notificationModalContent {
    width: 45% !important;
    background-color: $white;
    overflow: hidden;
    height: 85vh;
    border-radius: 10px !important;
  }
}

.desktopHeader {
  padding: 1em;
  background-color: $white;
  display: flex;
  align-items: center;
  position: static;
  top: 0;
  p {
    width: 100%;
    margin: 0;
  }
  span {
    height: 30px;
    width: 30px;
    background-color: #f5f5f5;
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 5px;
    cursor: pointer;
    svg {
      width: 25px;
      height: 25px;
      path {
        fill: black;
      }
    }
  }
}

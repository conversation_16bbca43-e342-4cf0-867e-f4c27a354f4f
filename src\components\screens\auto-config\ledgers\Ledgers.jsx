import React from 'react';
import AutoConfigDropdown from '../AutoConfigDropdown';
import { Form, Formik } from 'formik';
import { Button, Typography } from '@mui/material';

function Ledgers() {
  const handleSubmit = (values) => {
    console.log('values ::: ', values);
  };

  const initialValues = {
    purchase: '',
    freight: '',
    insurance: '',
    tcs: '',
    round_off: '',
    discount: '',
    other_charges: '',
  };

  return (
    <Formik initialValues={initialValues} onSubmit={handleSubmit}>
      {({ values, setFieldValue }) => (
        <Form className="flex flex-col gap-4 px-5">
          <Typography variant="h6" className="mb-4">Default Ledger</Typography>
          
          <AutoConfigDropdown
            label="Purchase"
            onSelect={(value) => setFieldValue('purchase', value?.uuid_id ?? '')}
            value={values.purchase}
            size="small"
          />

          <AutoConfigDropdown
            label="Freight"
            onSelect={(value) => setFieldValue('freight', value?.uuid_id ?? '')}
            value={values.freight}
          />

          <AutoConfigDropdown
            label="Insurance"
            onSelect={(value) => setFieldValue('insurance', value?.uuid_id ?? '')}
            value={values.insurance}
          />

          <AutoConfigDropdown
            label="TCS"
            onSelect={(value) => setFieldValue('tcs', value?.uuid_id ?? '')}
            value={values.tcs}
          />

          <AutoConfigDropdown
            label="Round off"
            onSelect={(value) => setFieldValue('round_off', value?.uuid_id ?? '')}
            value={values.round_off}
          />

          <AutoConfigDropdown
            label="Discount"
            onSelect={(value) => setFieldValue('discount', value?.uuid_id ?? '')}
            value={values.discount}
          />

          <AutoConfigDropdown
            label="Other Charges"
            onSelect={(value) => setFieldValue('other_charges', value?.uuid_id ?? '')}
            value={values.other_charges}
          />

          <Button variant="contained" color="primary" type="submit">Save</Button>
        </Form>
      )}
    </Formik>
  );
}

export default Ledgers;

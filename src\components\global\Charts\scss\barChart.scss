@import '../../../../assets/scss/main.scss';

.chartMainContentWarpper {
  overflow-x: auto;
  width: 100%;
  height: 450px;
  overflow-y: hidden;
}

.singleAddition {
  justify-content: flex-end !important;
}
.trendlineActive {
  justify-content: flex-start !important;
}

.additionDetails {
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: sticky;
  left: 0;
  padding: 1em 0;
  @include for_media(mobileScreen) {
    padding: 1em 0;
    justify-content: flex-start;
  }
  .trendLineEnable {
    display: flex;
    align-items: center;
    gap: 1em;
    padding: 2em 1em;
    label {
      font-size: 1.1em !important;
    }
    input {
      height: 20px;
      width: 20px;
      cursor: pointer;
    }
  }
  .paginationWrapper {
    display: flex;
    gap: 2em;
    align-items: center;
    .leftArrow {
      svg {
        transform: rotate(180deg);
      }
    }
    .arrowWrapper {
      height: 28px;
      width: 28px;
      cursor: pointer;
      display: flex;
      justify-content: center;
      align-items: center;
      background-color: #4e5ba6;
      border-radius: 100%;
      svg {
        width: 22px;
        height: 22px;
        path {
          fill: white;
        }
      }
    }
    .disableBtn {
      background-color: #dfdfdf;
      cursor: unset;
    }
  }
}

.loader {
  display: flex;
  justify-content: center;
  width: 100%;
  height: 100%;
  position: relative;
}

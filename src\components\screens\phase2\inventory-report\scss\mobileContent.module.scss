@import '../../../../../assets/scss/main.scss';

.mobileHeaderContent {
  justify-content: space-between;
  display: inline-flex;
  background: #eaecf5;
  border-radius: 1em;
  padding: 1em;
  align-items: last baseline;
  width: 100%;
  .detailedWrapper {
    width: 100%;
  }
  .overAllInfo {
    display: flex;
    justify-content: space-between;
    align-items: center;
    grid-template-columns: 2fr 1fr;
    background: #eaecf5;
    border-radius: 1em;
    padding: 0em;
    // align-items: last baseline;
    font-weight: bolder;
    width: 100%;

    .leftInfo {
      display: flex;
      gap: 0.2em;
      align-items: center;
      font-size: 1.3em !important;
      margin: 0;
      @include for_media(mobileScreen) {
        font-size: 1.1em !important;
      }
      svg {
        width: 30px;
        height: 30px;
      }
    }

    .rightInfo {
      font-size: 1.3em !important;
      margin: 0;
      @include for_media(mobileScreen) {
        font-size: 1.1em !important;
      }
    }
  }
}

.filterWrapper {
  display: flex;
  margin: 1em 0;
  gap: 1em;

  .customInput {
    display: flex;
    align-items: center;
    gap: 1em;
    width: 80%;
    height: 3.7em;
    background-color: #f5f5f5;
    padding: 1em;
    border-radius: 13px;
    border: 1px solid #d5d7da;

    svg {
      width: 25px;
      height: 25px;
    }

    input {
      width: 85%;
      border: none !important;
      height: 2.5em;
      color: black !important;
      background-color: unset !important;
    }

    input:focus {
      outline: none !important;
      border: none !important;
    }

    input::placeholder {
      font-size: 1.2em !important;
    }

    @include for_media(mobileScreen) {
      width: 100%;
    }
  }
}

.reportHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 1.5em;
}

.sortFilter {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20%;
  min-width: 8em;
  background-color: #f5f5f5;
  border-radius: 13px;
  border: 1px solid #d5d7da;
  gap: 1em;
  cursor: pointer;

  svg {
    width: 22px;
    height: 22px;
  }

  p {
    font-size: 1.2em !important;
  }

  @include for_media(mobileScreen) {
    height: 3.2em;
  }
}

.listContainer {
  width: 100%;
  max-width: 600px;
  /* Adjust width based on design */
  margin: auto;
}

.listHeader {
  display: grid;
  grid-template-columns: 2fr 1fr 2fr;
  justify-content: space-between;
  padding: 12px 16px;
  font-weight: 580;
  color: #535862;

  @include for_media(mobileScreen) {
    display: flex;
    justify-content: space-between;

    span {
      width: 30%;
      text-align: center;
    }
  }
}

.downloadBtnWrapper {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  padding: 1em;
  background-color: $white;
  z-index: 100;
}

.downloadBtn {
  background-color: #f6d659;
  color: #7e6607;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 3.1em;
  gap: 1em;
  border-radius: 35px !important;
  width: 100%;
  cursor: pointer;
  -webkit-text-stroke: 0.25px;
  font-size: 1.2em !important;

  svg {
    path {
      fill: #7e6607;
    }
  }
}

.headerItem {
  // flex: 1;
  font-size: smaller;
}

.valueColumnHeader {
  text-align: right !important;
}

.valueColumnItem {
  display: flex;
  justify-content: flex-end;
  width: 35%;
}

.itemList {
  list-style: none;
  list-style-type: none;
  padding: 0;
  margin-top: 8px;

  @include for_media(mobileScreen) {
    padding-bottom: 7em;
  }
}

.item {
  // display: grid;
  // grid-template-columns: 2fr 1fr 2fr;
  display: flex;
  gap: 1em;
  justify-content: space-between;
  align-items: center;
  background: white;
  padding: 12px 16px;
  border-radius: 15px;
  margin-bottom: 10px;
  font-size: 14px;
  color: #333;
  span {
    font-size: 0.95em !important;
  }
}

.unitName {
  width: 40% !important;
  text-align: start !important;
}

.itemDetails {
  display: flex;
  gap: 15px;
}

.alignRight {
  margin-right: 0.1em;
}

.filterWrapper {
  display: flex;
  margin: 1em 0;
  gap: 1em;
}

.emptyMsg {
  display: flex;
  justify-content: center;
  flex-direction: column;
  align-items: center;
  gap: 2em;
  height: 45vh;
  width: 100%;

  @include for_media(mobileScreen) {
    text-align: center;
  }
}

import React, { useEffect, useMemo, useState } from 'react';
import style from './scss/filtersUser.module.scss';
import { DropdownIcon, ListingIcon } from '../../../../assets/svgs';
import { Dropdown } from 'semantic-ui-react';
import ticketServices from '../../../services/ticketServices';
import useServiceFetch from '../../../global/hooks/useServiceFetch';
import useUpdateEffect from '../../../global/hooks/useUpdateEffect';

const statusOptions = [
  { key: 'all', value: '', text: 'All' },
  { key: 'open', value: 'Open', text: 'Open', className: style.openStatus },
  {
    key: 'pending',
    value: 'Pending',
    text: 'Pending',
    className: style.pendingStatus,
  },
  {
    key: 'closed',
    value: 'Closed',
    text: 'Closed',
    className: style.closedStatus,
  },
];

const defaultCategory = {
  key: 'All category',
  value: '',
  text: 'All category',
  className: style.closedStatus,
};

function FiltersUser({ setExtraParams }) {
  const [statusFilter, setStatusFilter] = useState('');
  const { data, loading } = useServiceFetch(ticketServices.getCategories, true, "Can't fetch category");

  const categoryOptions = useMemo(() => {
    const fetchedCategories = data?.results.reduce((acc, item) => {
      acc.push({
        key: item?.name,
        value: item?.name,
        text: item?.name,
      });
      return acc;
    }, []);
    return [defaultCategory, ...(fetchedCategories || [])];
  }, [data]);

  useUpdateEffect(() => setExtraParams((prev) => ({ ...prev, status: statusFilter })), [statusFilter]);

  return (
    <div className={style.filtersWrapper}>
      <div className={style.logFilterWrapper}>
        <div className="customDropdown2">
          <ListingIcon className={style.listingIcon} />
          <Dropdown
            placeholder="All category"
            selection
            options={categoryOptions}
            icon={<DropdownIcon />}
            loading={loading}
            onChange={(e, { value }) => setExtraParams((prev) => ({ ...prev, category: value }))}
          />
        </div>
      </div>
      <div className={style.statusListContainer}>
        {statusOptions?.map((item) => (
          <span
            className={statusFilter === item?.value && style.activeStatus}
            onClick={() => setStatusFilter(item?.value)}
          >
            {item?.text}
          </span>
        ))}
      </div>
    </div>
  );
}

export default FiltersUser;

import { Download } from 'lucide-react';
import { toast } from 'react-toastify';
import { Button } from '@mui/material';
import axios from 'axios';
import ls from 'local-storage';
import { restbaseurl } from '../utils/constants';
import { getErrorMessage, extractFilenameFromHeaders } from '../utils/apiUtils';

const DownloadBtn = ({ downloadUrl, onError, className = '', disabled = false }) => {
  const handleDownload = async () => {
    const headers = {
      Authorization: `Bearer ${ls.get('access_token')?.data}`,
    };
    axios
      .get(`${restbaseurl}/${downloadUrl}`, {
        headers,
        responseType: 'blob',
      })
      .then((res) => {
        const blob = new Blob([res?.data], {
          type: 'text/csv',
        });
        const filename = extractFilenameFromHeaders(res.headers) || 'download.csv';
        const downloadLink = document.createElement('a');
        downloadLink.href = URL.createObjectURL(blob);
        downloadLink.download = filename;
        downloadLink.click();
        URL.revokeObjectURL(downloadLink.href);
        downloadLink.remove();
      })
      .catch((err) => {
        toast.error(getErrorMessage(err));
        onError?.(err);
      });
  };

  return (
    <Button
      id="export-data-btn"
      onClick={handleDownload}
      disabled={disabled}
      variant="contained"
      startIcon={<Download size={16} />}
      className={className}
    >
      Download
    </Button>
  );
};

export default DownloadBtn;

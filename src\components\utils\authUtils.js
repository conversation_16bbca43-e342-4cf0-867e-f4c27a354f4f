import { toast } from 'react-toastify';

/**
 * @description: This function is used to open a popup window to authenticate the user
 * @param {string} authUrl - The URL to authenticate the user
 * @param {string} redirectUrl - The URL to redirect the user to after authentication
 * @returns {Promise<string>} - The URL that the user is redirected to after authentication
 */
export const authPopup = (authUrl, redirectUrl) => {
  return new Promise((resolve, reject) => {
    const width = 600;
    const height = 700;
    const left = window.screenX + (window.innerWidth - width) / 2;
    const top = window.screenY + (window.innerHeight - height) / 2;

    const popup = window.open(authUrl, 'ZohoAuth', `width=${width},height=${height},top=${top},left=${left}`);

    if (!popup) {
      toast.error('Please allow popups for this site');
      return reject(new Error('Popup blocked'));
    }

    const pollTimer = setInterval(() => {
      if (popup.closed) {
        clearInterval(pollTimer);
        return reject(new Error('Popup closed before authentication'));
      }

      try {
        const popupUrl = popup.location.href;
        if (popupUrl.includes(redirectUrl)) {
          const url = new URL(popupUrl);

          clearInterval(pollTimer);
          popup.close();

          if (url) {
            return resolve(popupUrl);
          } else {
            return reject(new Error('No auth code found'));
          }
        }
      } catch (err) {
        // Ignore cross-origin error
      }
    }, 500);
  });
};

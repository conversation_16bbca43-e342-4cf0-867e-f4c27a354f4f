import { Download } from 'lucide-react';
import { Tooltip } from 'react-tooltip';
import { toast } from 'react-toastify';
import { extractFilenameFromHeaders, getErrorMessage } from '../../../utils/apiUtils';
import axios from 'axios';
import ls from 'local-storage';
import { restbaseurl } from '../../../utils/constants';

const DownloadBtn = ({
  downloadUrl,
  onError,
  className = '',
  disabled = false,
  disableMessage = 'Export is disabled',
}) => {
  const handleDownload = async () => {
    const headers = {
      Authorization: `Bearer ${ls.get('access_token')?.data}`,
    };
    axios
      .get(`${restbaseurl}/${downloadUrl}`, {
        headers,
        responseType: 'blob',
      })
      .then((res) => {
        const blob = new Blob([res?.data], {
          type: 'text/csv',
        });
        const filename = extractFilenameFromHeaders(res.headers) || 'download.csv';
        const downloadLink = document.createElement('a');
        downloadLink.href = URL.createObjectURL(blob);
        downloadLink.download = filename;
        downloadLink.click();
        URL.revokeObjectURL(downloadLink.href);
        downloadLink.remove();
      })
      .catch((err) => {
        toast.error(getErrorMessage(err));
        onError?.(err);
      });
  };
  return (
    <>
      <div
        id="export-data-btn"
        onClick={handleDownload}
        className={`
          flex items-center justify-center py-[0.9em] px-[0.8em] rounded-[1em]
          transition-colors duration-200 whitespace-nowrap select-none
          ${
            disabled
              ? 'bg-gray-300 border-2 border-gray-400 text-gray-500 cursor-not-allowed opacity-70'
              : 'common-btn-schema'
          }
          ${className}
        `}
        aria-disabled={disabled}
      >
        <Download
          className={`
            ${disabled ? 'text-gray-500' : ''}
            mr-2 
          `}
          size={16}
        />
        <span className="text-[1.2em] text-inherit">Download</span>
      </div>
      {disabled && disableMessage && (
        <Tooltip
          anchorSelect="#export-data-btn"
          place="bottom"
          style={{
            backgroundColor: '#011638',
            borderRadius: '15px',
            zIndex: 1000,
            fontSize: '14px',
            fontWeight: 500,
            padding: '8px 12px',
          }}
        >
          {disableMessage}
        </Tooltip>
      )}
    </>
  );
};

export default DownloadBtn;

import { useState, useCallback, useEffect } from 'react';
import apiClient from '../../services/apiClient';

const useLoadMorePage = (fetchUrl, queryParams = {}) => {
  const [data, setData] = useState([]);
  const [nextPageUrl, setNextPageUrl] = useState(null);
  const [error, setError] = useState(null);
  const [isLoading, setIsLoading] = useState(!!fetchUrl);
  const [isLoadMore, setIsLoadMore] = useState(false);

  const fetchData = useCallback((url, isLoadMore = false) => {
    if (!url) return;
    const loadingState = isLoadMore ? setIsLoadMore : setIsLoading;
    loadingState(true);
    apiClient
      .get(url)
      .then((resData) => {
        setData((prev) => [...prev, ...resData.results]);
        setNextPageUrl(resData.next ?? null);
      })
      .catch((err) => {
        setError(err.message || 'Failed to load data');
      })
      .finally(() => {
        loadingState(false);
      });
  }, []);

  useEffect(() => {
    if (!fetchUrl) return;
    setData([]);
    fetchData(fetchUrl);
  }, []);

  return {
    data,
    nextPageUrl,
    isLoading,
    isLoadMore,
    error,
    fetchMore: () => fetchData(nextPageUrl, true),
  };
};

export default useLoadMorePage;

import React from 'react';
import AutoConfigDropdown from '../AutoConfigDropdown';
import { INDIAN_STATES } from '../../../utils/constants';
import { Button } from '@mui/material';
import Checkbox from '../../../ui-components/fields/Checkbox';
import { Form, Formik } from 'formik';

function General() {
  const handleSubmit = (values) => {
    console.log('values ::: ', values);
  };

  const initialValues = {
    business_name: 'abc-123',
    gst_status: '',
    gst_no: '',
    address: '',
    state: '',
    default_invoice_type: '',
    manage_inventory: false,
  };

  return (
    <Formik initialValues={initialValues} onSubmit={handleSubmit}>
      {({ values, setFieldValue }) => (
        <Form className="flex flex-col gap-4 px-5">
          <AutoConfigDropdown
            label="Business Name"
            onSelect={(value) => setFieldValue('business_name', value?.uuid_id ?? '')}
            value={values.business_name}
            size="small"
          />

          <AutoConfigDropdown
            label="GST Status"
            onSelect={(value) => setFieldValue('gst_status', value?.uuid_id ?? '')}
            value={values.gst_status}
          />

          <AutoConfigDropdown
            label="GST NO."
            onSelect={(value) => setFieldValue('gst_no', value?.uuid_id ?? '')}
            value={values.gst_no}
          />

          <AutoConfigDropdown
            label="Address"
            onSelect={(value) => setFieldValue('address', value?.uuid_id ?? '')}
            value={values.address}
          />

          <AutoConfigDropdown
            label="State"
            options={INDIAN_STATES}
            optionLabel="name"
            onSelect={(value) => setFieldValue('state', value?.code ?? '')}
            value={values.state}
            required
          />

          <AutoConfigDropdown
            label="Default Invoice Type"
            onSelect={(value) => setFieldValue('default_invoice_type', value?.uuid_id ?? '')}
            value={values.default_invoice_type}
          />

          <Checkbox
            label="Manage Inventory"
            checked={values.manage_inventory}
            onChange={(checked) => setFieldValue('manage_inventory', checked)}
            className="m-2"
          />

          <Button variant="contained" color="primary" type="submit">
            Save
          </Button>
        </Form>
      )}
    </Formik>
  );
}

export default General;

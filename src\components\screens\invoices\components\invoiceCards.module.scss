.cardsContainer {
  display: flex;
  flex-direction: column;
  gap: 1em;
  width: 100%;
  padding: 0 1em;
}

.card {
  background-color: white;
  border-radius: 0.75em;
  box-shadow:
    0 0.125em 0.375em rgba(16, 24, 40, 0.1),
    0 0.0625em 0.125em rgba(16, 24, 40, 0.06);
  display: flex;
  flex-direction: column;
  overflow: hidden;
  transition:
    transform 0.2s ease,
    box-shadow 0.2s ease;

  &:active {
    transform: scale(0.98);
    box-shadow: 0 0.0625em 0.1875em rgba(16, 24, 40, 0.1);
  }
}

.cardHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1em 1em 0.75em;
  border-bottom: 0.0625em solid #f2f4f7;

  .fileInfo {
    display: flex;
    align-items: center;
    gap: 0.5em;
    max-width: 70%;
  }

  .fileName {
    font-size: 0.9375em;
    font-weight: 600;
    color: #101828;
    margin: 0;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .fileIcon {
    flex-shrink: 0;
    color: #4e5ba6;
    min-width: 1.25em;
  }
}

.cardBody {
  padding: 0.75em 1em;
  display: flex;
  flex-direction: column;
  gap: 0.75em;
  flex: 1;
}

.infoRow {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 0.875em;
}

.infoLabel {
  display: flex;
  align-items: center;
  gap: 0.2em;
  color: #667085;
  font-weight: 500;
  min-width: 6.25em;

  svg {
    color: #4e5ba6;
  }
}

.infoValue {
  color: #101828;
  font-weight: 500;
  text-align: right;
  max-width: 60%;
  font-size: 1.2em;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;

  p {
    margin: 0;
    font-size: 1em;
  }
}

.ticketLink {
  color: #4e5ba6;
  text-decoration: none;
  font-weight: 500;

  &:hover {
    text-decoration: underline;
  }
}

.cardFooter {
  padding: 0.75em 1em 1em;
  display: flex;
  justify-content: flex-end;
  border-top: 0.0625em solid #f2f4f7;
}

.viewButton {
  background-color: #4e5ba6;
  color: white;
  border: none;
  border-radius: 0.5em;
  padding: 0.5em 1em;
  font-size: 0.875em;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s ease;

  &:hover {
    background-color: #3a4580;
  }

  &:active {
    background-color: #293056;
  }
}

.status {
  display: inline-block;
  padding: 0.25em 0.625em;
  border-radius: 1em;
  font-size: 0.75em;
  font-weight: 500;
  text-align: center;
  min-width: 5em;
}

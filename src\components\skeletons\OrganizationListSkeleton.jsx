import React from 'react';
import style from './scss/organizationListSkeleton.module.scss';

function OrganizationListSkeleton({ count }) {
  return (
    <div className={style.skeletonContainer}>
      {Array.from({ length: count }).map((_, index) => (
        <div className={style.orgItem} key={index}>
          <div className={style.leftContent}>
            <div className={style.logo}>
              <div className={style.skeletonBox} />
            </div>
            <div className={style.content}>
              <div className={style.skeletonText} />
            </div>
          </div>
          <div className={style.skeletonButton} />
        </div>
      ))}
    </div>
  );
}

export default OrganizationListSkeleton;

@import '../../../../assets/scss/main.scss';

.popupContainer {
  padding: 0 0.5em !important;
  // width: 20em !important;
  border-radius: 15px !important;

  .dropDownItem {
    margin: 1em 0;
    cursor: pointer;

    .option {
      display: flex;
      align-items: center;
      gap: 1em;

      p {
        width: 85%;
      }
    }
  }

  .customRadio {
    height: 22px;
    width: 22px;
    margin: 0;
    border-radius: 100%;
    border: 1px solid #4e5ba6;
    display: flex;
    justify-content: center;
    align-items: center;

    svg {
      height: 100%;
      width: 100%;
      display: none;
    }
  }

  .customCheckBox {
    height: 25px;
    width: 25px;
    margin: 0;

    svg {
      height: 100%;
      width: 100%;
    }
  }

  .selectedItem {
    .customRadio {
      border: none;

      svg {
        display: block;
      }
    }

    .customCheckBox {
      svg {
        rect {
          fill: #4e5ba6;
        }
      }
    }
  }
}

.applyBtn {
  display: flex;
  justify-content: center;

  button {
    width: 100%;
    background-color: #f6d659;
    color: #7e6607;
    display: flex;
    align-items: center;
    justify-content: center;
    height: 3.1em;
    gap: 1em;
    border-radius: 35px !important;
    cursor: pointer;
    -webkit-text-stroke: 0.25px;
    font-size: 1.2em !important;

    svg {
      path {
        fill: #7e6607;
      }
    }
  }
}

.logListPopup {
  display: flex;
  flex-direction: column;
  background-color: #ffffff;
  width: 100%;
  gap: 1em;
  box-shadow: 0 0 1px 1px #d7d7d7;
  transform: translateY(150%);
  transition: all 1s ease;
  padding: 1em 0 2em 0;
  position: fixed;
  bottom: 0;
  left: 0;
  border-radius: 30px 30px 0 0;

  p {
    margin: 0;
  }

  h4 {
    font-size: 1.5em !important;
  }
}

.openDropdown {
  // height: 50dvh;
  transform: translateY(0%);
  z-index: 1000;
  // visibility: visible;
}

.logItem {
  display: flex;
  align-items: center;
  gap: 2em;
}

.listCloseIcon {
  position: absolute;
  background-color: white;
  padding: 1.3em;
  border-radius: 35px;
  right: -23px;
  top: -97px;

  svg path {
    fill: $black !important;
  }

  @include for_media(mobileScreen) {
    right: 10px;
    top: -77px;
  }
}

@include for_media(mobileScreen) {
  .modalContent {
    border-radius: 20px !important;
  }

  .popupContainer {
    padding: 1em !important;
  }

  .applyBtn {
    padding: 1em 1em 0 1em !important;
  }

  .modalContent {
    h4 {
      padding: 0.5em 1em;
    }
  }
}

.dateContainer {
  display: flex;
  align-items: center;
  gap: 0.5em;
  padding: 1em 0;

  p {
    margin: 0;
    height: 0;
  }

  .dateInput {
    // width: 50%;
    background-color: #f5f5f5 !important;
    border-radius: 12px !important;

    input {
      border-radius: 12px !important;
      background-color: #f5f5f5 !important;
      color: $black !important;
      height: 3.7em;

      svg {
        display: none !important;
      }
    }
  }
}

.dateWrapper {
  @include for_media(mobileScreen) {
    width: 45%;
  }

  label {
    padding: 0.5em 0;
    display: inline-block;
  }

  input {
    cursor: pointer;
  }
}

.emptyInput {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1em;
  background-color: #f5f5f5;
  border-radius: 12px !important;
  border: 1px solid #e9eaeb;

  // width: 50%;
  p {
    font-size: 1.1em !important;
    white-space: nowrap;
  }

  span {
    display: flex;
    align-items: center;
  }

  min-width: 10em;

  @include for_media(mobileScreen) {
    min-width: unset !important;
  }
}

.dateApplyBtn {
  display: flex;
  justify-content: flex-end;

  button {
    background-color: #f6d659;
    border-radius: 35px !important;
    color: #7e6607;
    cursor: pointer;
    // width: 46%;
  }

  .disableBtn {
    background-color: #f6d759a4;
    color: #a8890e;
    cursor: unset;
  }
}

.btnWrapper {
  display: flex;
  justify-content: space-between;

  div {
    width: 45%;

    button {
      width: 100%;
    }
  }

  .clearBtn {
    button {
      border: 1px solid #f9e699 !important;
      background-color: #fdf7dd !important;
      color: #7e6607 !important;
      border-radius: 35px;
      cursor: pointer;
      // width: 46%;
    }
  }
}

import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { Modal } from 'semantic-ui-react';
import { useInfiniteQuery } from '@tanstack/react-query';
import style from './modalDropdown.module.scss';
import { ChevronRight, Plus, RefreshCcw, Search } from 'lucide-react';
import { SuccessIcon, WarningIcon } from '../../../../../assets/svgs';
import Option from '../../../../ui-components/fields/Option';
import { Button } from '@mui/material';
import { fullRefreshZoho } from '../../../../services/zohoServices';
import { useAuth } from '../../../../../contexts/AuthContext';
import { getErrorMessage } from '../../../../utils/apiUtils';
import { toast } from 'react-toastify';
import useDebounce from '../../../../global/hooks/useDebounce';
import apiClient from '../../../../services/apiClient';

function ModalDropdown({
  label,
  onAdd = null,
  onSelect = null,
  onClose = null,
  options = [],
  disabled = false,
  showMatchesFound = false,
  url = null,
  transformOptionsObj = null,
  searchParamName = 'search_query',
  isShowRefreshButton = false,
}) {
  const [isOpen, setIsOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedOption, setSelectedOption] = useState(null);
  const updatedValue = useRef(null);
  const [filteredOptions, setFilteredOptions] = useState([]);
  const { globSelectedBusiness } = useAuth();
  const debouncedSearchQuery = useDebounce(searchQuery, 600);

  const {
    data: infiniteData,
    isLoading,
    isFetchingNextPage,
    hasNextPage,
    fetchNextPage,
    refetch,
    isFetching,
  } = useInfiniteQuery({
    queryKey: ['modal-dropdown', url, debouncedSearchQuery],
    queryFn: async ({ pageParam = 1 }) => {
      try {
        const res = await apiClient.get(url, {
          params: {
            [searchParamName]: debouncedSearchQuery,
            page: pageParam,
          },
        });

        let items = [];
        let nextPage = null;

        if (Array.isArray(res.data)) {
          items = res.data;
        } else if (Array.isArray(res.results)) {
          items = res.results;
        }

        if (res.next) {
          const nextUrl = new URL(res.next);
          nextPage = parseInt(nextUrl.searchParams.get('page'), 10) || null;
        }

        return {
          items,
          nextPage,
          count: res?.pagination?.total || res?.count || 0,
        };
      } catch (error) {
        const errorMessage = getErrorMessage(error);
        toast.error(errorMessage);
        throw error;
      }
    },
    getNextPageParam: (lastPage) => lastPage?.nextPage ?? undefined,
    enabled: isOpen && !!url,
    staleTime: 5 * 60 * 1000,
    retry: false,
  });

  const apiResults = useMemo(() => infiniteData?.pages?.flatMap((page) => page.items || []) ?? [], [infiniteData]);

  useEffect(() => {
    if (options?.length > 0) {
      const filtered = searchQuery
        ? options.filter((option) => option.label.toLowerCase().includes(searchQuery.toLowerCase()))
        : [...options];
      setFilteredOptions(filtered);
    }
  }, [searchQuery, options]);

  const handleClose = useCallback(() => {
    setIsOpen(false);
    setSearchQuery('');
    setSelectedOption(updatedValue.current);
    onClose && onClose();
  }, [onClose]);

  const transformedOptions = useMemo(() => {
    const transformedApiOptions = url
      ? apiResults.map((item) => ({
          key: (item?.[transformOptionsObj?.key] || item?.id) ?? null,
          label: item?.[transformOptionsObj?.label] ?? '',
          value: (item?.[transformOptionsObj?.value] || item?.id) ?? null,
        }))
      : [];

    // If both URL and options are provided, combine them
    if (url && options.length > 0) {
      return [...transformedApiOptions, ...filteredOptions];
    }

    // If only URL is provided, return transformed API options
    if (url) {
      return transformedApiOptions;
    }

    // If only options are provided, return filtered options
    return filteredOptions?.length > 0 ? filteredOptions : [];
  }, [url, apiResults, filteredOptions, options, transformOptionsObj]);

  const { exactMatches, probableMatches } = useMemo(() => {
    if (options?.length < 1) {
      return { exactMatches: [], probableMatches: [] };
    }

    return filteredOptions.reduce(
      (acc, option) => {
        if (option.similarity_score === 1) {
          acc.exactMatches.push(option);
        } else {
          acc.probableMatches.push(option);
        }
        return acc;
      },
      { exactMatches: [], probableMatches: [] }
    );
  }, [filteredOptions, options]);

  const handleRefresh = useCallback(() => {
    fullRefreshZoho(globSelectedBusiness?.business_id)
      .then(() => {
        refetch();
        toast.success('Refreshed successfully');
      })
      .catch((err) => {
        const errorMessage = getErrorMessage(err);
        toast.error(errorMessage);
      });
  }, [globSelectedBusiness?.business_id]);

  return (
    <>
      {/* dropdown button that triggers modal */}
      <div
        className={`flex items-center my-2 ml-1 text-accent2 cursor-pointer select-none ${
          disabled ? 'opacity-50 pointer-events-none cursor-not-allowed' : ''
        }`}
        onClick={() => setIsOpen(true)}
      >
        <div className="flex items-center justify-center gap-2 text-nowrap">
          {showMatchesFound && <span className="text-accent2 font-semibold">{options?.length || 0} matches found</span>}
          <span className="flex items-center text-nowrap border-b border-accent2">
            {label} <ChevronRight className="w-4 h-4 " />
          </span>
        </div>
      </div>

      <Modal dimmer="blurring" size="small" open={isOpen} onClose={handleClose}>
        <div className={style.dropdownContent}>
          <div className="flex justify-between items-center w-full">
            <h3 className="m-0  font-semibold text-xl text-[#101828]">Select an option from below</h3>
            {isShowRefreshButton && (
              <Button
                className="flex items-center gap-2 px-4 py-2 !rounded-full !bg-accent1-bg border !border-accent1-border text-lg !text-primary-color font-semibold cursor-pointer hover:!bg-accent1-bg-hover"
                onClick={handleRefresh}
                disabled={isFetching || isFetchingNextPage}
              >
                <RefreshCcw className={`w-6 h-6 ${isFetching && !isLoading ? 'animate-spin' : ''}`} /> Refresh
              </Button>
            )}
          </div>

          <div className="relative w-full">
            <input
              className={style.inputField}
              type="text"
              placeholder="Search"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
            <span className="absolute left-4 top-1/2 -translate-y-1/2 flex items-center justify-center">
              <Search className="w-6 h-6" />
            </span>
            {(isLoading || (isFetching && !isFetchingNextPage)) && (
              <span className="absolute right-4 top-1/2 -translate-y-1/2 flex items-center justify-center">
                <div className="animate-spin h-5 w-5 border-2 border-[#7e6607] rounded-full border-t-transparent"></div>
              </span>
            )}
          </div>

          {/* add new button */}
          {onAdd && (
            <Button onClick={() => onAdd()} disabled={disabled} className={style.addBtn}>
              <Plus className="w-6 h-6" />
              Add New
            </Button>
          )}

          {/* exact matches */}
          {exactMatches.length > 0 && (
            <div className={style.optionsContainer}>
              <div className={style.optionHeader}>
                <SuccessIcon className="w-7 h-7" />
                <span>Exact Matches ({exactMatches.length})</span>
              </div>
              <div className={style.optionList}>
                {exactMatches.map((option) => (
                  <Option
                    key={`${option.key}-exactMatches`}
                    label={option.label}
                    isChecked={selectedOption?.value === option?.value}
                    className={`${style.option} ${selectedOption?.value === option?.value ? style.selected : ''}`}
                    onClick={() => setSelectedOption(option)}
                  />
                ))}
              </div>
            </div>
          )}

          {/* probable matches */}
          {probableMatches.length > 0 && (
            <div className={style.optionsContainer}>
              <div className={style.optionHeader}>
                <WarningIcon className="w-7 h-7" />
                <span>Probable Matches ({probableMatches.length})</span>
              </div>
              <div className={style.optionList}>
                {probableMatches.map((option) => (
                  <Option
                    key={`${option.key}-probableMatches`}
                    label={option.label}
                    isChecked={selectedOption?.value === option?.value}
                    className={`${style.option} ${selectedOption?.value === option?.value ? style.selected : ''}`}
                    onClick={() => setSelectedOption(option)}
                  />
                ))}
              </div>
            </div>
          )}

          {isLoading ? (
            <div className="flex items-center justify-center py-4">Loading options...</div>
          ) : transformedOptions.length === 0 ? (
            <div className="flex items-center justify-center py-4">No results found</div>
          ) : (
            <div className={style.optionsContainer}>
              <div className={style.optionHeader}>
                <span>All Options ({infiniteData?.pages?.[0]?.count ?? transformedOptions.length ?? 0})</span>
              </div>
              <div className={style.optionList}>
                {transformedOptions.map((option) => (
                  <Option
                    key={`${option.key}-allOptions`}
                    label={option.label}
                    isChecked={selectedOption?.value === option?.value}
                    className={`${style.option} ${selectedOption?.value === option?.value ? style.selected : ''}`}
                    onClick={() => setSelectedOption(option)}
                  />
                ))}
                {hasNextPage && (
                  <div className="w-full flex justify-center mt-2">
                    <Button
                      onClick={() => fetchNextPage()}
                      disabled={isFetchingNextPage}
                      className="px-4 py-2 bg-accent1-bg border border-accent1-border text-primary-color font-semibold rounded hover:bg-accent1-bg-hover"
                    >
                      {isFetchingNextPage ? 'Loading...' : 'Load More'}
                    </Button>
                  </div>
                )}
              </div>
            </div>
          )}
        </div>

        <div className={style.actionContainer}>
          <Button className={style.cancelButton} onClick={handleClose}>
            Cancel
          </Button>
          <Button
            className={style.updateButton}
            onClick={() => {
              onSelect && onSelect(selectedOption);
              updatedValue.current = selectedOption;
              handleClose();
            }}
            disabled={!selectedOption}
          >
            Select
          </Button>
        </div>
      </Modal>
    </>
  );
}

export default ModalDropdown;

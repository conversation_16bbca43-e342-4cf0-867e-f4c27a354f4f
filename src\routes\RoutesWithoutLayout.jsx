import React from 'react';
import { Route } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import SuperuserRoutes from './SuperuserRoutes';
import { SuspenseWrapper } from '../components/utils/JSXUtils';

const ProfileScreen = React.lazy(() => import('../components/screens/profile/ProfileScreen'));
const UserListScreen = React.lazy(() => import('../components/screens/UserListScreen'));
const AiExtractionScreen = React.lazy(() => import('../components/screens/ai-extraction/AiExtractionScreen'));

function RoutesWithoutLayout() {
  const { roleType } = useAuth();

  return (
    <>
      {/* Admin routes */}
      {roleType === 'admin' && SuperuserRoutes()}
      <Route
        path="/profile"
        element={
          <SuspenseWrapper>
            <ProfileScreen />
          </SuspenseWrapper>
        }
      />
      <Route
        path="/usersList"
        element={
          <SuspenseWrapper>
            <UserListScreen />
          </SuspenseWrapper>
        }
      />
      <Route
        path="/ai-extraction/f/:fileId/b/:businessId/p/:invoicePage"
        element={
          <SuspenseWrapper>
            <AiExtractionScreen />
          </SuspenseWrapper>
        }
      />
    </>
  );
}

export default RoutesWithoutLayout;

import React, { useMemo, useState } from 'react';
import { Autocomplete, TextField, CircularProgress, Button } from '@mui/material';
import { useInfiniteQuery } from '@tanstack/react-query';
import apiClient from '../../services/apiClient';
import { useAuth } from '../../../contexts/AuthContext';
import useDebounce from '../../global/hooks/useDebounce';

export default function AutoConfigDropdown({
  url,
  options = null,
  optionLabel = 'value',
  optionValue = 'uuid_id',
  searchParam = 'search',
  isShow = true,
  access = 'RW',
  dropdown_selector = 'All',
  onSelect,
  value,
  size = 'small',
  ...props
}) {
  const { globSelectedBusiness } = useAuth();
  const [inputValue, setInputValue] = useState('');
  const debounceValue = useDebounce(inputValue, 500);

  const { data, fetchNextPage, hasNextPage, isFetchingNextPage, isLoading } = useInfiniteQuery({
    queryKey: ['search-autocomplete', url, debounceValue],
    queryFn: async () => {
      const res = await apiClient.get(url, {
        params: {
          [searchParam]: debounceValue,
          include: dropdown_selector,
        },
      });

      let items = [];
      let nextPage = null;

      if (Array.isArray(res.data)) {
        items = res.data;
        const pagination = res.pagination || {};
        if (pagination.page < Math.ceil(pagination.total / pagination.page_size)) {
          nextPage = pagination.page + 1;
        }
      } else if (Array.isArray(res.results)) {
        items = res.results;
        if (res.next) {
          const nextUrl = new URL(res.next);
          const pageParam = nextUrl.searchParams.get('page');
          nextPage = pageParam ? parseInt(pageParam) : null;
        }
      }

      return {
        items: items,
        nextPage,
      };
    },
    getNextPageParam: (lastPage) => lastPage.nextPage,
    enabled:
      !!globSelectedBusiness?.business_id &&
      !options &&
      isShow &&
      access === 'RW' &&
      dropdown_selector.toLowerCase() !== 'as per list',
    staleTime: 5 * 60 * 1000,
  });

  const finalOptions = useMemo(() => {
    if (options || dropdown_selector.toLowerCase() === 'as per list') return options;
    const flatOptions = data?.pages.flatMap((page) => page.items) || [];
    return hasNextPage ? [...flatOptions, { loadMore: true, key: 'load-more' }] : flatOptions;
  }, [data, hasNextPage, options, dropdown_selector]);

  const selectedOption = useMemo(() => {
    if (!value) return null;
    return finalOptions?.find((option) => option[optionValue] === value);
  }, [value, finalOptions, optionValue]);

  if (!isShow) return <></>;

  return (
    <Autocomplete
      options={finalOptions}
      value={selectedOption}
      getOptionLabel={(option) => (option.loadMore ? '' : option?.[optionLabel] || '')}
      loading={isLoading}
      autoHighlight
      openOnFocus
      onInputChange={(e, value) => setInputValue(value)}
      disabled={access !== 'RW' || props.disabled}
      size={size}
      onChange={(e, value) => {
        if (value?.loadMore) {
          // Prevent selecting the fake loadMore option
          return;
        }
        onSelect?.(value);
      }}
      renderInput={(params) => (
        <TextField
          {...params}
          {...props}
          slotProps={{
            inputAdornment: {
              endAdornment: (
                <>
                  {(isLoading || isFetchingNextPage) && <CircularProgress color="inherit" size={20} />}
                  {params.InputProps?.endAdornment}
                </>
              ),
            },
          }}
        />
      )}
      renderOption={(props, option) => {
        if (option.loadMore) {
          return (
            <li {...props} key="load-more" className="flex justify-center">
              <Button
                size="small"
                onClick={(e) => {
                  e.stopPropagation();
                  fetchNextPage();
                }}
                disabled={isFetchingNextPage}
              >
                {isFetchingNextPage ? 'Loading...' : 'Load More'}
              </Button>
            </li>
          );
        }
        return <li {...props}>{option?.[optionLabel]}</li>;
      }}
    />
  );
}

import React, { useEffect, useState, useRef, useCallback } from 'react';
import { resturls } from '../../../utils/apiurls';
import { useNavigate } from 'react-router-dom';
import style from './scss/inventoryReport.module.scss';
import { mediaBreakpoint } from '../../../global/MediaBreakPointes';
import GlobalService from '../../../services/GlobalServices';
import ls from 'local-storage';
import debounce from 'lodash.debounce';
import RenderDownloadModal from '../../../utils/reportUtils/RenderDownloadModal';
import DesktopContent from './component/DesktopContent';
import MobileContent from './component/MobileContent';
import PaginationContent from '../../../utils/reportUtils/PaginationContent';
import { BackIcon } from '../../../../assets/svgs';
import ReportSortFilter from '../../../global/components/ReportSortFilter';

const InventoryReport = () => {
  const [isLoading, setLoading] = useState(false);
  // const [reload, setReload] = useState(false);
  const [downloadModal, setDownloadModal] = useState(false);
  const isResponsive = mediaBreakpoint?.mobile > window.innerWidth;
  const navigate = useNavigate();
  const [searchTerm, setSearchTerm] = useState('');
  const [openDropdown, setOpenDropdown] = useState(false);
  const [filteredItems, setFilteredItems] = useState({});
  const [activePage, setActivePage] = useState(1);
  const [selectedSortOption, setSelectedSortOption] = useState({ name: null, value: null });
  // Handle the search input change and update the results
  const [inventoryDetails, setInventoryDetails] = useState({});
  const debounceTimeout = useRef(null);
  const business_id = ls.get('selectedBusiness')?.business_id;

  const obtainInventoryReportDetails = () => {
    // Check if the data exists in local storage
    const storedInventoryData = ls.get('inventoryDetails');
    const currentDate = new Date();

    if (
      !storedInventoryData ||
      !storedInventoryData.expiry_at ||
      new Date(storedInventoryData.expiry_at) < currentDate
    ) {
      ls.remove('inventoryDetails');

      fetchInventoryReportData();
    } else {
      setInventoryDetails(storedInventoryData.data);
      setActivePage(storedInventoryData?.page || 1);
    }
  };

  useEffect(() => {
    obtainInventoryReportDetails();
  }, []);

  useEffect(() => {
    return () => {
      clearTimeout(debounceTimeout.current);
    };
  }, []);

  const fetchInventoryReportData = (quantitySort = '', search, valueSort = '', page) => {
    setLoading(true);
    const searchItem = search !== undefined ? search : searchTerm;
    const queryParams = new URLSearchParams();
    if (searchItem) queryParams.append('item_name', searchItem);
    if (quantitySort) queryParams.append('quantity_sort', quantitySort);
    if (valueSort) queryParams.append('value_sort', valueSort);
    if (page) queryParams.append('page', page);
    if (business_id) queryParams.append('business_id', business_id);

    GlobalService.generalSelect(
      (respdata) => {
        if (respdata?.data) {
          const { expiry_at } = respdata;
          const dataToStore = {
            data: respdata,
            expiry_at,
            page,
          };
          ls.set('inventoryDetails', dataToStore);
          setInventoryDetails(respdata);
          setFilteredItems(respdata);
          setLoading(false);
        }
      },
      `${resturls.getInventoryReportData}?${queryParams.toString()}`,
      {},
      'GET'
    );
  };

  const handleSearch = (e) => {
    const { value } = e.target;
    setSearchTerm(value);
    debouncedSearch(value);
  };

  const debouncedSearch = useCallback(
    debounce((query) => {
      let quantitySort =
        selectedSortOption.name === 'Quantity'
          ? selectedSortOption.value === 'lowToHighQuantity'
            ? 'asc'
            : 'desc'
          : '';
      let valueSort =
        selectedSortOption.name === 'Value' ? (selectedSortOption.value === 'lowToHigh' ? 'asc' : 'desc') : '';
      fetchInventoryReportData(quantitySort, query.trim(), valueSort);
    }, 500), // Adjust delay as needed
    [selectedSortOption]
  );

  const handleRefresh = () => {
    ls.remove('inventoryDetails');
    fetchInventoryReportData();
  };

  const handleDropdownList = () => {
    setOpenDropdown(true);
  };
  const handleClose = () => {
    setOpenDropdown(false);
  };

  const inventorySort = [
    {
      title: 'Quantity',
      options: [
        { text: 'Low to High', value: 'lowToHighQuantity' },
        { text: 'High to Low', value: 'highToLowQuantity' },
      ],
    },
    {
      title: 'Value',
      options: [
        { text: 'Low to High', value: 'lowToHigh' },
        { text: 'High to Low', value: 'highToLow' },
      ],
    },
  ];

  const handleSelectDropdown = (selectedOption, name) => {
    setSelectedSortOption({ name: name, value: selectedOption.value });
  };

  const handleApplySort = () => {
    let quantitySort =
      selectedSortOption?.name === 'Quantity'
        ? selectedSortOption.value === 'lowToHighQuantity'
          ? 'asc'
          : 'desc'
        : '';
    let valueSort =
      selectedSortOption?.name === 'Value' ? (selectedSortOption.value === 'lowToHigh' ? 'asc' : 'desc') : '';
    fetchInventoryReportData(quantitySort, searchTerm, valueSort);
    setOpenDropdown(false);
  };

  const handleClear = () => {
    fetchInventoryReportData();
    setSelectedSortOption({ name: null, vlaue: null });
    setOpenDropdown(false);
  };

  function getCurrentFormattedDate() {
    const months = [
      'January',
      'February',
      'March',
      'April',
      'May',
      'June',
      'July',
      'August',
      'September',
      'October',
      'November',
      'December',
    ];

    const now = new Date();
    const day = now.getDate();
    const month = months[now.getMonth()];
    const year = now.getFullYear();

    return `${day}-${month}-${year}`;
  }
  const handleDownload = () => {
    const sortOrder = selectedSortOption?.text === 'lowtohigh' ? 'asc' : 'desc';
    const valueSort = selectedSortOption?.value === 'lowtohigh' ? 'asc' : 'desc';
    GlobalService.generalSelect(
      (respdata, error) => {
        // Updated to handle errors
        if (error) {
          console.error('Download failed:', error);
          alert('Failed to download the report. Please try again.');
          return; // Stop execution if error occurs
        }

        if (respdata) {
          // Convert response to a Blob with 'text/csv' MIME type
          const blob = new Blob([respdata], { type: 'text/csv' });

          // Create an anchor element to trigger the download
          const link = document.createElement('a');
          link.href = URL.createObjectURL(blob);
          getCurrentFormattedDate();
          link.download = `Inventory_Report_${getCurrentFormattedDate()}.csv`;
          document.body.appendChild(link);
          link.click();

          URL.revokeObjectURL(link.href);
          document.body.removeChild(link);
          setDownloadModal(false);
        }
      },

      `${resturls.downloadInventoryReport}?business_id=${business_id}&item_name=${selectedSortOption?.value}&quantity_sort=${sortOrder}&value_sort=${valueSort}"}`,
      {},
      'GET'
    );
  };

  const handlePagination = (newPage) => {
    setActivePage(newPage);
    let quantitySort =
      selectedSortOption?.name === 'Quantity'
        ? selectedSortOption.value === 'lowToHighQuantity'
          ? 'asc'
          : 'desc'
        : '';
    let valueSort =
      selectedSortOption?.name === 'Value' ? (selectedSortOption.value === 'lowToHigh' ? 'asc' : 'desc') : '';
    fetchInventoryReportData(quantitySort, searchTerm, valueSort, newPage);
  };

  const renderPagination = () => {
    if (!inventoryDetails?.pagination?.total_pages || inventoryDetails?.pagination?.total_pages <= 1) {
      return;
    }
    return (
      <div className={style.paginationWrapper}>
        <PaginationContent
          activePage={activePage}
          totalPages={inventoryDetails?.pagination?.total_pages}
          pageChangeFunction={handlePagination}
        />
      </div>
    );
  };

  const renderDownloadModal = () => {
    return (
      <RenderDownloadModal
        isOpen={downloadModal}
        onClose={() => setDownloadModal(false)}
        content={`Your Inventory Report is prepared and ready for download. You can save it for further review`}
        downloadFunction={handleDownload}
        checkBoxDisable={true}
      />
    );
  };

  if (isResponsive) {
    return (
      <>
        <div className={style.mobileViewContainer}>
          <div className={style.backIcon} onClick={() => navigate(-1)}>
            <BackIcon />
          </div>
          <div className={style.rightContentWrapper}>
            <MobileContent
              filteredItems={filteredItems}
              inventoryDetails={inventoryDetails}
              handleRefresh={handleRefresh}
              setDownloadModal={setDownloadModal}
              isLoading={isLoading}
              handleDropdownList={handleDropdownList}
              handleApplySort={handleApplySort}
              handleClear={handleClear}
              openDropdown={openDropdown}
              renderPagination={renderPagination}
              setOpenDropdown={setOpenDropdown}
              handleSearch={handleSearch}
              searchTerm={searchTerm}
            />
          </div>
          <ReportSortFilter
            sortOption={inventorySort}
            handleSelectDropdown={handleSelectDropdown}
            selectedTimeline={selectedSortOption}
            handleApplySort={handleApplySort}
            handleClear={handleClear}
            openDropdown={openDropdown}
            handleClose={handleClose}
          />
          {renderDownloadModal()}
        </div>
      </>
    );
  }

  return (
    <>
      <DesktopContent
        filteredItems={filteredItems}
        inventoryDetails={inventoryDetails}
        handleRefresh={handleRefresh}
        setDownloadModal={setDownloadModal}
        isLoading={isLoading}
        handleDropdownList={handleDropdownList}
        renderPopupContent={
          <ReportSortFilter
            sortOption={inventorySort}
            handleSelectDropdown={handleSelectDropdown}
            selectedTimeline={selectedSortOption}
            handleApplySort={handleApplySort}
            handleClear={handleClear}
          />
        }
        openDropdown={openDropdown}
        setOpenDropdown={setOpenDropdown}
        handleSearch={handleSearch}
        searchTerm={searchTerm}
      />
      {renderDownloadModal()}
      {renderPagination()}
    </>
  );
};
export default InventoryReport;

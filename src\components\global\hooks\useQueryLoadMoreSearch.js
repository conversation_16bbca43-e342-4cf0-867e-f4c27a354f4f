import { useMemo } from 'react';
import { useInfiniteQuery } from '@tanstack/react-query';
import apiClient from '../../services/apiClient';
import useDebounce from './useDebounce';

const useQueryLoadMoreSearch = ({
  url,
  searchParam = 'search',
  searchValue = '',
  debounceDelay = 500,
  transformOptions = { key: 'id', label: 'name', value: 'id' },
  enabled = true,
  staleTime = 5 * 60 * 1000,
  ...queryOptions
}) => {
  const debouncedSearchValue = useDebounce(searchValue, debounceDelay);

  const { data, fetchNextPage, hasNextPage, isFetchingNextPage, isLoading, error, refetch } = useInfiniteQuery({
    queryKey: ['query-pagination-search', url, debouncedSearchValue],
    queryFn: async ({ pageParam = 1 }) => {
      const res = await apiClient.get(url, {
        params: {
          [searchParam]: debouncedSearchValue,
          page: pageParam,
        },
      });

      let items = [];
      let nextPage = null;

      if (Array.isArray(res.data)) {
        items = res.data;
        const pagination = res.pagination || {};
        if (
          pagination.page &&
          pagination.total &&
          pagination.page_size &&
          pagination.page < Math.ceil(pagination.total / pagination.page_size)
        ) {
          nextPage = pagination.page + 1;
        }
      } else if (Array.isArray(res.results)) {
        items = res.results;
        if (res.next) {
          const nextUrl = new URL(res.next);
          const pageParam = nextUrl.searchParams.get('page');
          nextPage = pageParam ? parseInt(pageParam) : null;
        }
      }

      return {
        items: items.map((item) => ({
          key: item[transformOptions.key] ?? null,
          label: item[transformOptions.label] ?? '',
          value: item[transformOptions.value] ?? null,
          ...item,
        })),
        nextPage,
      };
    },
    getNextPageParam: (lastPage) => lastPage.nextPage,
    enabled: enabled && !!url,
    staleTime,
    ...queryOptions,
  });

  const options = useMemo(() => {
    const flatOptions = data?.pages.flatMap((page) => page.items) || [];
    return hasNextPage ? [...flatOptions, { loadMore: true }] : flatOptions;
  }, [data, hasNextPage]);

  const loadMore = () => {
    if (hasNextPage && !isFetchingNextPage) {
      fetchNextPage();
    }
  };

  return {
    options,
    data: data?.pages.flatMap((page) => page.items) || [],
    hasNextPage,
    isFetchingNextPage,
    isLoading,
    error,
    loadMore,
    refetch,
  };
};

export default useQueryLoadMoreSearch;

import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import style from './scss/profileScreen.module.scss';
import { useAuth } from '../../contexts/AuthContext';
import Avatar from 'react-avatar';
import { Image, Loader, Pagination } from 'semantic-ui-react';
import ls from 'local-storage';
import { processLogout } from '../utils';
import GlobalService from '../services/GlobalServices';
import { resturls } from '../utils/apiurls';
import { decryptData } from '../utils/cryptoUtils';

const ProfileScreen = () => {
  const navigate = useNavigate();
  const { userInfo, businessList } = useAuth();
  const [organisationList, setOrganisationList] = useState([]);
  const [organization, setOrganisation] = useState(false);
  const [userList, setUserList] = useState([]);
  const [UserData, SetUserData] = useState([]);
  const [OrganisationCount, SetOrganisationCount] = useState();
  const [paginationInfo, setPaginationInfo] = useState();
  const [activePage, setActivePage] = useState(1);
  const [isLoading, setIsLoading] = useState(1);

  const roleEncripted = ls.get('access_token')?.role;
  const role = roleEncripted && decryptData(roleEncripted);

  useEffect(() => {
    setIsLoading(true);
    GlobalService.generalSelect(
      (respdata) => {
        const { results } = respdata;
        // const matchedList = results?.find((info) => info?.business_id === data?.business_id);
        setOrganisationList(results);
        SetOrganisationCount(respdata.count);
        setPaginationInfo(respdata);
        setIsLoading(false);
      },
      `${resturls.createBusiness}`,
      {},
      'GET'
    );
  }, []);
  useEffect(() => {
    const userId = userInfo?.userId || userInfo?.user_id;
    setIsLoading(true);
    if (userId) {
      const url = role !== 'superuser' ? `${resturls.profilefetch}/` : `${resturls.profilefetch}/?user_id=${userId}`;

      GlobalService.generalSelect(
        (respdata) => {
          if (respdata?.results) {
            SetUserData(respdata.results);
            setIsLoading(false);
          }
        },
        url,
        {},
        'GET'
      );
    }
  }, [userInfo, role, resturls.profilefetch]);

  const userName = UserData[0]?.full_name;
  const handleBack = () => {
    if (organization) {
      setOrganisation(false);
    } else {
      navigate('/');
    }
  };

  const backIcon = () => (
    <svg
      width="16"
      height="14"
      viewBox="0 0 16 14"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      onClickCapture={handleBack}
    >
      <path
        fill-rule="evenodd"
        clip-rule="evenodd"
        d="M7.53033 0.46967C7.82322 0.762563 7.82322 1.23744 7.53033 1.53033L2.81066 6.25H15C15.4142 6.25 15.75 6.58579 15.75 7C15.75 7.41421 15.4142 7.75 15 7.75H2.81066L7.53033 12.4697C7.82322 12.7626 7.82322 13.2374 7.53033 13.5303C7.23744 13.8232 6.76256 13.8232 6.46967 13.5303L0.46967 7.53033C0.176777 7.23744 0.176777 6.76256 0.46967 6.46967L6.46967 0.46967C6.76256 0.176777 7.23744 0.176777 7.53033 0.46967Z"
        fill="#0A0D12"
      />
    </svg>
  );

  const logoutIcon = () => (
    <svg width="16" height="15" viewBox="0 0 16 15" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        fill-rule="evenodd"
        clip-rule="evenodd"
        d="M1.29159 1.54159C1.67839 1.1548 2.20299 0.9375 2.75 0.9375H8C8.54701 0.9375 9.07161 1.1548 9.45841 1.54159C9.8452 1.92839 10.0625 2.45299 10.0625 3V4.5C10.0625 4.81066 9.81066 5.0625 9.5 5.0625C9.18934 5.0625 8.9375 4.81066 8.9375 4.5V3C8.9375 2.75136 8.83873 2.5129 8.66291 2.33709C8.4871 2.16127 8.24864 2.0625 8 2.0625H2.75C2.50136 2.0625 2.2629 2.16127 2.08709 2.33709C1.91127 2.5129 1.8125 2.75136 1.8125 3V12C1.8125 12.2486 1.91127 12.4871 2.08709 12.6629C2.2629 12.8387 2.50136 12.9375 2.75 12.9375H8C8.24864 12.9375 8.4871 12.8387 8.66291 12.6629C8.83873 12.4871 8.9375 12.2486 8.9375 12V10.5C8.9375 10.1893 9.18934 9.9375 9.5 9.9375C9.81066 9.9375 10.0625 10.1893 10.0625 10.5V12C10.0625 12.547 9.8452 13.0716 9.45841 13.4584C9.07161 13.8452 8.54701 14.0625 8 14.0625H2.75C2.20299 14.0625 1.67839 13.8452 1.29159 13.4584C0.904798 13.0716 0.6875 12.547 0.6875 12V3C0.6875 2.45299 0.904798 1.92839 1.29159 1.54159ZM12.1023 4.85225C12.3219 4.63258 12.6781 4.63258 12.8977 4.85225L15.1477 7.10225C15.3674 7.32192 15.3674 7.67808 15.1477 7.89775L12.8977 10.1477C12.6781 10.3674 12.3219 10.3674 12.1023 10.1477C11.8826 9.92808 11.8826 9.57192 12.1023 9.35225L13.392 8.0625H4.25C3.93934 8.0625 3.6875 7.81066 3.6875 7.5C3.6875 7.18934 3.93934 6.9375 4.25 6.9375H13.392L12.1023 5.64775C11.8826 5.42808 11.8826 5.07192 12.1023 4.85225Z"
        fill="#B42318"
      />
    </svg>
  );

  const defaultLogo = () => (
    <svg width="16" height="15" viewBox="0 0 16 15" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        fill-rule="evenodd"
        clip-rule="evenodd"
        d="M2.26126 1.26126C2.78871 0.733816 3.50408 0.4375 4.25 0.4375H7.25C7.99592 0.4375 8.71129 0.733816 9.23874 1.26126C9.76618 1.78871 10.0625 2.50408 10.0625 3.25V13.1875H12.6875V11.2343C12.3518 11.1392 12.0427 10.9595 11.7916 10.7084C11.4048 10.3216 11.1875 9.79701 11.1875 9.25V7.75C11.1875 7.20299 11.4048 6.67839 11.7916 6.29159C12.1784 5.9048 12.703 5.6875 13.25 5.6875C13.797 5.6875 14.3216 5.9048 14.7084 6.29159C15.0952 6.67839 15.3125 7.20299 15.3125 7.75V9.25C15.3125 9.79701 15.0952 10.3216 14.7084 10.7084C14.4573 10.9595 14.1482 11.1392 13.8125 11.2343V13.1875H14.75C15.0607 13.1875 15.3125 13.4393 15.3125 13.75C15.3125 14.0607 15.0607 14.3125 14.75 14.3125H1.25C0.93934 14.3125 0.6875 14.0607 0.6875 13.75C0.6875 13.4393 0.93934 13.1875 1.25 13.1875H1.4375V3.25C1.4375 2.50408 1.73382 1.78871 2.26126 1.26126ZM2.5625 13.1875H5.1875V10.75C5.1875 10.4393 5.43934 10.1875 5.75 10.1875C6.06066 10.1875 6.3125 10.4393 6.3125 10.75V13.1875H8.9375V3.25C8.9375 2.80245 8.75971 2.37323 8.44324 2.05676C8.12678 1.74029 7.69755 1.5625 7.25 1.5625H4.25C3.80245 1.5625 3.37323 1.74029 3.05676 2.05676C2.74029 2.37323 2.5625 2.80245 2.5625 3.25V13.1875ZM4.4375 4.75C4.4375 4.43934 4.68934 4.1875 5 4.1875H6.5C6.81066 4.1875 7.0625 4.43934 7.0625 4.75C7.0625 5.06066 6.81066 5.3125 6.5 5.3125H5C4.68934 5.3125 4.4375 5.06066 4.4375 4.75ZM13.25 6.8125C13.0014 6.8125 12.7629 6.91127 12.5871 7.08709C12.4113 7.2629 12.3125 7.50136 12.3125 7.75V9.25C12.3125 9.49864 12.4113 9.7371 12.5871 9.91291C12.7629 10.0887 13.0014 10.1875 13.25 10.1875C13.4986 10.1875 13.7371 10.0887 13.9129 9.91291C14.0887 9.7371 14.1875 9.49864 14.1875 9.25V7.75C14.1875 7.50136 14.0887 7.2629 13.9129 7.08709C13.7371 6.91127 13.4986 6.8125 13.25 6.8125ZM4.4375 7.75C4.4375 7.43934 4.68934 7.1875 5 7.1875H6.5C6.81066 7.1875 7.0625 7.43934 7.0625 7.75C7.0625 8.06066 6.81066 8.3125 6.5 8.3125H5C4.68934 8.3125 4.4375 8.06066 4.4375 7.75Z"
        fill="#0A0D12"
      />
    </svg>
  );

  const viewIcon = () => (
    <svg width="7" height="12" viewBox="0 0 7 12" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        fill-rule="evenodd"
        clip-rule="evenodd"
        d="M0.558058 0.558058C0.802136 0.313981 1.19786 0.313981 1.44194 0.558058L6.44194 5.55806C6.68602 5.80214 6.68602 6.19786 6.44194 6.44194L1.44194 11.4419C1.19786 11.686 0.802136 11.686 0.558058 11.4419C0.313981 11.1979 0.313981 10.8021 0.558058 10.5581L5.11612 6L0.558058 1.44194C0.313981 1.19786 0.313981 0.802136 0.558058 0.558058Z"
        fill="#363F72"
      />
    </svg>
  );

  const mapIcon = () => (
    <svg width="13" height="18" viewBox="0 0 13 18" fill="none" xmlns="http://www.w3.org/2000/svg">
      <g clip-path="url(#clip0_941_57585)">
        <path
          d="M3.67383 13.333C4.19621 13.9965 4.67168 14.6953 5.09679 15.4245C5.45973 16.1102 5.61103 16.5752 5.87627 17.4015C6.03894 17.858 6.18595 17.9943 6.50204 17.9943C6.84647 17.9943 7.00274 17.7625 7.12347 17.404C7.37419 16.6237 7.57092 16.0282 7.88129 15.4655C8.49033 14.3791 9.24711 13.4137 9.99071 12.4859C10.192 12.2233 11.4936 10.6935 12.0795 9.48642C12.0795 9.48642 12.7997 8.16045 12.7997 6.30861C12.7997 4.57639 12.0894 3.375 12.0894 3.375L10.0447 3.92069L8.80287 7.17987L8.49564 7.62928L8.43422 7.71069L8.35251 7.81261L8.20909 7.9754L8.00414 8.17958L6.89835 9.07732L4.13373 10.668L3.67383 13.333Z"
          fill="#34A853"
        />
        <path
          d="M0.821289 9.26434C1.49603 10.8001 2.79718 12.1502 3.6775 13.3349L8.35333 7.81556C8.35333 7.81556 7.69459 8.67407 6.49966 8.67407C5.16863 8.67407 4.09335 7.61491 4.09335 6.27938C4.09335 5.36354 4.64628 4.73438 4.64628 4.73438L1.47221 5.5819L0.821289 9.26434Z"
          fill="#FBBC04"
        />
        <path
          d="M8.40497 0.283203C9.95792 0.782151 11.2871 1.82963 12.0911 3.37425L8.35417 7.81186C8.35417 7.81186 8.9071 7.17132 8.9071 6.26116C8.9071 4.89454 7.75228 3.87359 6.50473 3.87359C5.32501 3.87359 4.64746 4.73067 4.64746 4.73067V1.93503L8.40497 0.283203Z"
          fill="#4285F4"
        />
        <path
          d="M1.67773 2.24461C2.60526 1.13876 4.23736 0 6.48744 0C7.57911 0 8.40156 0.285597 8.40156 0.285597L4.64439 4.73376H1.9824L1.67773 2.24461Z"
          fill="#1A73E8"
        />
        <path
          d="M0.820942 9.26399C0.820942 9.26399 0.200195 8.05265 0.200195 6.29777C0.200195 4.63917 0.847225 3.18936 1.6775 2.24414L4.64627 4.73368L0.820942 9.26399Z"
          fill="#EA4335"
        />
      </g>
      <defs>
        <clipPath id="clip0_941_57585">
          <rect width="12.6" height="18" fill="white" transform="translate(0.200195)" />
        </clipPath>
      </defs>
    </svg>
  );

  const handleSwitchingScreen = (data) => {
    setIsLoading(true);
    setOrganisation(data);
    const newUserList = [data?.business_superuser, ...(data?.business_users || [])];
    setUserList(newUserList);
    setIsLoading(false);
  };

  const renderOrganization = () => {
    const itemsPerPage = 10;
    const totalPages = Math.ceil(paginationInfo?.count / itemsPerPage);
    const handlePaginationChange = (e, { activePage }) => {
      setActivePage(activePage);
      // const selected = paginationInfo?.next || paginationInfo?.previous;
      // const bisnsId = selected?.split("api/get-tickets/")[1]?.split("/")[0]?.split("?")[0];
      const endpoint = `${resturls.createBusiness}?page=${activePage}`;
      setIsLoading(true);
      GlobalService.generalSelect(
        (respdata) => {
          console.log(respdata, 'handlePaginationChange');
          if (respdata && respdata.results) {
            setOrganisationList(respdata.results);
            setPaginationInfo(respdata);
            setIsLoading(false);
          } else {
            console.warn('No results found in response:', respdata);
            setOrganisationList([]);
          }
        },
        endpoint,
        {},
        'GET'
      );
    };
    if (isLoading) {
      return (
        <div className={style.loaderContainer}>
          <Loader active center />
        </div>
      );
    }
    return (
      <div>
        {organisationList?.map((data) => (
          <div className={style.orgItem}>
            <div className={style.leftContent}>
              <div className={style.logo}>{data?.image ? <Image src={data?.image} /> : defaultLogo()}</div>
              <div className={style.content}>
                <p className={style.name}>{data?.name}</p>
                {/* <p>{data?.business_id}</p> */}
              </div>
            </div>
            <p className={style.viewDetail} onClickCapture={() => handleSwitchingScreen(data)}>
              View details {viewIcon()}
            </p>
          </div>
        ))}
        <div className={style.paginationWrapper}>
          {paginationInfo?.count > 10 && organisationList?.length > 0 && (
            <Pagination activePage={activePage} totalPages={totalPages} onPageChange={handlePaginationChange} />
          )}
        </div>
      </div>
    );
  };

  const renderOrganizationDeatils = () => {
    return (
      <>
        <div className={style.detailWrapper}>
          <div className={style.detailsContainer}>
            <div className={style.profileWrapper}>
              <div className={style.orgLogo}>
                {organization?.image ? <Image src={organization?.image} /> : defaultLogo()}
              </div>
              <h4>{organization?.name}</h4>
            </div>
            <hr />
            <div className={style.details}>
              <p className={style.label}>Address</p>
              <p className={style.address}>
                {organization?.address}{' '}
                <div className={style.mapIcon}>
                  <a target="_blank" href={organization?.google_map_link} rel="noreferrer">
                    {mapIcon()}
                  </a>
                </div>
              </p>
              <p className={style.label}>Industry Type</p>
              <p>{organization?.business_type?.name || '-'}</p>
              <p className={style.label}>GST Number</p>
              <p>{organization?.gst}</p>
            </div>
          </div>
        </div>
        <div className={style.userList}>
          <p>Organisation Members{` (${userList?.length})`}</p>
          {userList?.length > 0 &&
            userList?.map((user) => (
              <div className={style.userItem}>
                <Avatar className={style.avatarProfile} color="#EAECF5" name={user?.user_full_name} />
                <div className={style.info}>
                  <p>{user?.user_full_name}</p>
                  <p>#{user?.user_id}</p>
                </div>
                {user?.user_role === 'business_superuser' && (
                  <div className={style.superUser}>
                    {' '}
                    <p>Superuser</p>{' '}
                  </div>
                )}
              </div>
            ))}
        </div>
      </>
    );
  };

  if (isLoading) {
    return (
      <div style={{ height: '100vh', display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
        <Loader active inline="centered" size="large" />
      </div>
    );
  }

  return (
    <div className={style.profileScreen}>
      <div className={style.header}>
        {backIcon()}
        <p>Your Profile</p>
      </div>
      <div className={style.contentWrapper}>
        {organization ? (
          renderOrganizationDeatils()
        ) : (
          <>
            <div className={style.detailWrapper}>
              <div className={style.detailsContainer}>
                <div className={style.profileWrapper}>
                  <Avatar
                    className={style.avatarProfile}
                    onClick={() => navigate('/profile')}
                    color="#EAECF5"
                    name={userName || 'User'}
                  />
                  <h4>{userName}</h4>
                </div>
                <hr />
                <div className={style.details}>
                  <p className={style.label}>User ID</p>
                  <p>{UserData[0]?.id || userInfo?.userId}</p>
                  <p className={style.label}>Mobile Number</p>
                  <p>{UserData[0]?.whatsapp || 'Not available'}</p>
                </div>
                <p className={style.logoutBtn} onClickCapture={() => processLogout()}>
                  {logoutIcon()}Logout
                </p>
              </div>
            </div>

            {role !== 'superuser' && (
              <div className={style.orgListWrapper}>
                {OrganisationCount ? (
                  <>
                    <p>Organisations{` (${OrganisationCount})`}</p>
                    {organisationList?.length > 0 && renderOrganization()}
                  </>
                ) : null}
              </div>
            )}
          </>
        )}
      </div>
    </div>
  );
};

export default ProfileScreen;

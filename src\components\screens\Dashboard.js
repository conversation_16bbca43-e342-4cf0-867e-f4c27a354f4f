import React from 'react';
import { useAuth } from '../../contexts/AuthContext';
import { Card } from 'semantic-ui-react';
import ls from 'local-storage';
import Header from '../global/Header';
import style from './scss/homeScreen.module.scss';

const Dashboard = () => {
  const { userInfo } = useAuth();
  const userName = ls.get('userName');

  return (
    <div>
      <Header />
      <div className={style.cardWrapper}>
        <Card fluid className={style.infoCard}>
          <Card.Content>
            <div>
              <h4>Welcome</h4>
              <h4 className={style.userName}>{userName}</h4>
              <p>(Role : {userInfo?.role})</p>
              <p>
                Your dashboard is currently a work in progress and will be ready soon. In the meantime, please let us
                know if you need any assistance or have any questions.
              </p>
            </div>
          </Card.Content>
        </Card>
      </div>
    </div>
  );
};

export default Dashboard;

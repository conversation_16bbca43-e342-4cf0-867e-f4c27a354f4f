import React, { useState } from 'react';
import DataTable from '../../../custom-components/DataTable';
import { useAuth } from '../../../../contexts/AuthContext';
import DownloadBtn from '../../../custom-components/DonwloadBtn';
import UploadBtn from '../../../custom-components/UploadBtn';
import { useOutletContext } from 'react-router-dom';
import { Button, FormControlLabel, Radio, RadioGroup, Typography } from '@mui/material';
import AddItemForm from '../../ai-extraction/form-sections/components/AddItemForm';

const COLUMNS = [
  {
    field: 'name',
    headerName: 'Name',
  },
  {
    field: 'ledger_name',
    headerName: 'Ledger Name',
  },
  {
    field: 'group_name',
    headerName: 'Group Name',
  },
  {
    field: 'created_at',
    headerName: 'Created At',
  },
  {
    field: 'updated_at',
    headerName: 'Updated At',
  },
];

function Item() {
  const { globSelectedBusiness } = useAuth();
  const { businessPreferences, isZoho } = useOutletContext();
  const [open, setOpen] = useState(false);
  const [viewType, setViewType] = useState('items-list');

  const handleViewTypeChange = (event) => {
    setViewType(event.target.value);
  };

  return (
    <DataTable
      title={viewType === 'items-list' ? 'List of Items' : 'List of Items Aliases'}
      url={`api/v1/ai_invoice/ledgers?business_id=${globSelectedBusiness?.business_id}`}
      columns={COLUMNS}
    >
      <DataTable.BeforeSearch>
        <div className="flex items-center gap-2">
          <Typography variant="p">Select View Type: </Typography>
          <RadioGroup row name="view-type" value={viewType} onChange={handleViewTypeChange}>
            <FormControlLabel value="items-list" control={<Radio size="small" />} label="Items List" />
            <FormControlLabel value="items-aliases" control={<Radio size="small" />} label="Items Aliases" />
          </RadioGroup>
        </div>
      </DataTable.BeforeSearch>
      {!businessPreferences?.enable_auto_sync_master && (
        <>
          <DownloadBtn className="!rounded-md" />
          <UploadBtn className="!rounded-md" />
        </>
      )}
      {isZoho && (
        <>
          <Button variant="contained" className="!rounded-md" onClick={() => setOpen(true)}>
            Add Item
          </Button>
          <AddItemForm open={open} onClose={() => setOpen(false)} />
        </>
      )}
    </DataTable>
  );
}

export default Item;

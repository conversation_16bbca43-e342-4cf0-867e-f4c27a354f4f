import React, { useState, useRef, useEffect, useCallback } from 'react';
import './ExpandableSection.scss';
import { Check, ChevronDown } from 'lucide-react';
import { ErrorIcon } from '../../../../assets/svgs';
import { toast } from 'react-toastify';

function ExpandableSection({
  title,
  children,
  expandedSections,
  setExpandedSections,
  sectionId,
  errorCount = 0,
  isMarkedAsValidated = false,
  invoiceType,
  warningCount = 0, //for future use-case
  hasMissingFields = false,
}) {
  const [isExpanded, setIsExpanded] = useState(expandedSections?.has(sectionId));
  const contentRef = useRef(null);
  const sectionRef = useRef(null);
  const isFirstRender = useRef(true);

  useEffect(() => {
    if (invoiceType) {
      setIsExpanded(expandedSections?.has(sectionId));
    }
  }, [expandedSections, sectionId]);

  useEffect(() => {
    const content = contentRef.current;
    if (!content || !invoiceType) return;
    let scrollTimeout;
    let hideTimeout;

    if (isExpanded) {
      content.style.maxHeight = `${content.scrollHeight}px`;
      content.style.opacity = '1';

      scrollTimeout = setTimeout(() => {
        if (sectionRef.current) {
          const rect = sectionRef.current.getBoundingClientRect();
          const headerHeight = document.getElementById('form-header')?.getBoundingClientRect().height || 0;
          const footerHeight = document.getElementById('form-footer')?.getBoundingClientRect().height || 0;
          const isInViewport = rect.top >= headerHeight && rect.bottom <= window.innerHeight - footerHeight;

          if (!isInViewport && !isFirstRender.current) {
            const scrollContainer = document.getElementById('ai-extraction-form-container');
            if (scrollContainer) {
              const elementTop = sectionRef.current.offsetTop;
              const targetScroll = elementTop - headerHeight;
              scrollContainer.scrollTo({
                top: targetScroll,
                behavior: 'smooth',
              });
            } else {
              // Fallback to default scrollIntoView if container not found
              sectionRef.current.scrollIntoView({
                behavior: 'smooth',
                block: 'nearest',
              });
            }
          }
          isFirstRender.current = false;
        }
        content.style.maxHeight = 'none';
      }, 300);
    } else {
      content.style.maxHeight = `${content.scrollHeight}px`;
      hideTimeout = setTimeout(() => {
        content.style.maxHeight = '0px';
        content.style.opacity = '0';
      }, 100);

      // Wait for collapse to finish then scroll into view if needed
      scrollTimeout = setTimeout(() => {
        if (sectionRef.current) {
          const rect = sectionRef.current.getBoundingClientRect();
          const isInViewport = rect.top >= -100 && rect.bottom <= window.innerHeight;

          if (!isInViewport) {
            sectionRef.current.scrollIntoView({
              behavior: 'smooth',
              block: 'start',
            });
          }
        }
      }, 400);
    }

    return () => {
      clearTimeout(scrollTimeout);
      clearTimeout(hideTimeout);
    };
  }, [isExpanded]);

  const handleExpand = useCallback(() => {
    if (invoiceType) {
      setExpandedSections((prev) => {
        const newSet = new Set(prev);
        if (prev.has(sectionId)) {
          newSet.delete(sectionId);
        } else {
          newSet.add(sectionId);
        }
        return newSet;
      });
    } else {
      toast.info('Please select category');
    }
  }, [sectionId, setExpandedSections, invoiceType]);

  return (
    <div className="expandable-section" ref={sectionRef}>
      <div id={sectionId} className={`section-header`} onClick={handleExpand}>
        <h3 className="section-title">{title}</h3>
        <div className="flex gap-2 items-center">
          <div className="flex justify-center items-center gap-2">
            {errorCount > 0 && (
              <div className="status-indicator error">
                <ErrorIcon className="w-6 h-6" /> {errorCount}
              </div>
            )}
            {hasMissingFields && (
              <div className="status-indicator missing">
                <span className="w-2 h-2 bg-amber-500 rounded-full inline-block mr-1"></span>
                <span>Required fields missing</span>
              </div>
            )}
            {isMarkedAsValidated && (
              <div className="flex items-center justify-center gap-1 text-[#083A1C] rounded-full p-2 border-2 border-[#D5D7DA]">
                <Check className="w-5 h-5" /> Validated
              </div>
            )}
          </div>
          <div className={`chevron-icon ${isExpanded ? 'rotated' : ''}`}>
            <ChevronDown />
          </div>
        </div>
      </div>

      <div ref={contentRef} className="section-content-wrapper">
        <div className="section-content">{children}</div>
      </div>
    </div>
  );
}

export default ExpandableSection;

@import '../../../../../../assets/scss/main.scss';

.ticketListContainer {
  width: 100%;
  background-color: $white;
  border-radius: 10px;
  box-shadow: 0 0 1px 1px $borderColor;
  margin-top: 1em;
  position: relative;

  .ticketListHeader {
    padding: 2em;
    display: flex;
    justify-content: space-between;
    .headerLeft {
      display: flex;
      align-items: center;
      align-content: center;

      .orgCount {
        font-size: 0.8em !important;
        height: 25px;
        width: 35px;
        background-color: #f9f5ff;
        color: #6941c6;
        display: flex;
        justify-content: center;
        align-items: center;
        border-radius: 20px;
        margin-left: 10px;
      }
    }
    .headerRight {
      display: flex;
      gap: 1em;
      .downloadBtn {
        display: flex;
        gap: 1em;
        align-items: center;
        width: 10em;
        height: 2.5em;
        border-radius: 35px;
        border: 1px solid #d5d7da;
        justify-content: center;
        color: #293056;
        font-size: 1.4em;
        svg {
          width: 25px;
          height: 25px;
        }
      }
      .viewAllBtn {
        display: flex;
        gap: 1em;
        align-items: center;
        width: 8em;
        height: 2.5em;
        border-radius: 35px;
        border: 1px solid #9ea5d1;
        justify-content: center;
        color: #293056;
        background-color: #eaecf5;
        font-size: 1.4em;
        cursor: pointer;
        svg {
          width: 25px;
          height: 25px;
        }
        @include for_media(mobileScreen) {
          font-size: 1.1em;
        }
      }
      .addBtn {
        display: flex;
        gap: 1em;
        align-items: center;
        width: 10em;
        height: 2.5em;
        border-radius: 35px;
        border: 1px solid #d5d7da;
        justify-content: center;
        color: $white;
        background-color: #4e5ba6;
        font-size: 1.4em;
        svg {
          width: 25px;
          height: 25px;
        }
      }
    }
  }

  .searchWrapper {
    padding: 1em 2em;
    display: flex;
    gap: 2em;
    justify-content: space-between;
    .leftFilter {
      display: flex;
      gap: 2em;
      width: 100%;
      .searchInput {
        width: 35% !important;
        height: 3.5em !important;
        border-radius: 10px;
        box-shadow: 0 0 1px 1px #e9eaeb;
        input {
          background-color: #fdfdfd !important;
          color: #717680 !important;
          border-radius: 10px !important;
        }
      }
      .statusDropdown {
        height: 3.1em !important;
        border-radius: 10px;
        min-width: 10em;
        box-shadow: 0 0 1px 1px #e9eaeb;
        background-color: #fafafa !important;
        font-size: 1.1rem !important;
        svg {
          path {
            fill: #0a0d12;
          }
        }
        display: flex !important;
        align-items: center;
        justify-content: space-evenly;
      }
    }
    .rightFilter {
      .createTicketBtn {
        height: 3.2em;
        width: 3.2em;
        border-radius: 100%;
        background-color: #4e5ba6;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        svg {
          width: 2.2em;
          height: 2.2em;
        }
      }
    }
  }

  .ticketListMainContainer {
    width: 100%;
    display: flex;
    align-items: flex-start;
    justify-self: left;
    flex-direction: column;
    gap: 2em;
    padding-bottom: 2em;
    min-height: 41vh;
    .emptyWrapper {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
    }
  }

  .tableWrapper::-webkit-scrollbar {
    display: none; /* For Chrome, Safari, and Opera */
  }

  .tableWrapper {
    padding: 1em 0;
    max-height: 59vh;
    overflow-y: auto;
    -ms-overflow-style: none; /* For IE and Edge */
    scrollbar-width: none; /* For Firefox */

    table {
      thead {
        background-color: #f5f5f5 !important;
        height: 5em !important;
      }
      th {
        color: #717680 !important;
        padding: 1em !important;
        font-size: 1rem !important;
      }
      td {
        padding: 1em !important;
        color: #717680;
        p {
          font-size: 1.1em !important;
        }
      }
      .subjectheaderRow {
        text-align: start !important;
        padding: 1em !important;
      }
      .subjectRow {
        text-align: start !important;
        display: flex;
        align-items: center;
        gap: 1em;
        padding: 1em !important;
        font-weight: 900;
        font-size: 1rem !important;
        .logo {
          border: 2px solid $borderColor;
          border-radius: 10px;
          width: 45px;
          height: 45px;
          display: flex;
          justify-content: center;
          align-items: center;
          background-color: #f5f5f5;
          svg {
            width: 25px;
            height: 25px;
          }
          img {
            width: 100%;
            height: 100%;
            border-radius: 10px;
          }
          svg,
          path {
            fill: #0a0d12;
          }
        }
      }
      tbody {
        tr {
          background-color: $white;
          border-radius: 20px !important;
        }
      }
    }
  }

  .loaderWrapper {
    display: flex;
    align-items: center;
    justify-content: center;
  }
}

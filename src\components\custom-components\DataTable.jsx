import React, { useState, <PERSON> } from 'react';
import {
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Pagination,
  TextField,
  InputAdornment,
  Typography,
  Box,
} from '@mui/material';
import { Search } from '@mui/icons-material';
import useQueryPaginationSearch from '../global/hooks/useQueryPaginationSearch';

/**
 * DataTable - A reusable component that fetches data with built-in search and pagination
 *
 * @param {Object} props
 * @param {string} props.url - API endpoint URL
 * @param {string} [props.searchParam="search"] - Query parameter name for search
 * @param {Array<Object>} props.columns - Column definitions with { field, headerName, width, renderCell }
 * @param {string} props.idField - Field to use as unique identifier
 * @param {Object} [props.additionalParams={}] - Additional parameters to send with API requests
 * @param {Function} [props.onRowClick] - Callback when a row is clicked
 * @param {string} [props.emptyMessage="No records found"] - Message to display when no data is available
 * @param {string} [props.title] - Optional table title
 * @param {Object} [props.transformOptions] - Options for transforming data (key, label, value)
 * @param {React.ReactNode} props.children - Can include BeforeSearch component for content before search
 */
const DataTable = ({
  children,
  url,
  searchParam = 'search',
  columns,
  idField = 'id',
  additionalParams = {},
  onRowClick,
  emptyMessage = 'No records found',
  title,
  transformOptions = { key: 'id', label: 'name', value: 'id' },
}) => {
  const [searchTerm, setSearchTerm] = useState('');
  const {
    data: rows,
    isLoading,
    error,
    currentPage,
    totalPages,
    goToPage,
  } = useQueryPaginationSearch({
    url,
    searchParam,
    searchValue: searchTerm,
    debounceDelay: 500,
    transformOptions,
    enabled: true,
    initialPage: 1,
    ...additionalParams,
  });

  const beforeSearchContent = Children.toArray(children).find((child) => child.type === DataTable.BeforeSearch);
  const otherChildren = Children.toArray(children).filter((child) => child.type !== DataTable.BeforeSearch);

  if (error) {
    return (
      <Paper className="p-6 border border-error-border bg-error-bg rounded-lg">
        <Box className="flex items-center gap-3">
          <Box className="w-2 h-2 bg-error rounded-full flex-shrink-0" />
          <Typography variant="body1" className="text-error font-medium">
            Error loading data: {error.message}
          </Typography>
        </Box>
      </Paper>
    );
  }

  return (
    <Paper className="overflow-hidden flex flex-col h-full shadow-sm border border-gray-200 rounded-lg">
      {/* Compact Header Section */}
      <Box className="flex-shrink-0 bg-gray-50/50 border-b border-gray-100">
        <Box className="px-6 py-3">
          <Box className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3">
            {/* Left side - Title and BeforeSearch content */}
            <Box className="flex items-center gap-4 flex-wrap">
              {title && (
                <Typography variant="h6" className="font-semibold text-primary-color whitespace-nowrap">
                  {title}
                </Typography>
              )}
              {beforeSearchContent && beforeSearchContent.props.children}
            </Box>

            {/* Right side - Search and action buttons */}
            <Box className="flex items-center gap-3 flex-wrap">
              <TextField
                placeholder="Search..."
                variant="outlined"
                size="small"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="min-w-[250px]"
                sx={{
                  '& .MuiOutlinedInput-root': {
                    backgroundColor: 'white',
                    borderRadius: '8px',
                    '&:hover .MuiOutlinedInput-notchedOutline': {
                      borderColor: '#4AC2FF',
                    },
                    '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
                      borderColor: '#4AC2FF',
                      borderWidth: '2px',
                    },
                  },
                }}
                slotProps={{
                  input: {
                    startAdornment: (
                      <InputAdornment position="start">
                        <Search className="text-gray-400" fontSize="small" />
                      </InputAdornment>
                    ),
                  },
                }}
              />
              {otherChildren}
            </Box>
          </Box>
        </Box>
      </Box>

      {/* Table Content */}
      <Box className="flex-1 overflow-hidden">
        <TableContainer className="h-full">
          <Table stickyHeader>
            <TableHead>
              <TableRow>
                {columns.map((column) => (
                  <TableCell
                    key={column.field}
                    sx={{
                      backgroundColor: '#F8FAFC',
                      borderBottom: '2px solid #E2E8F0',
                      fontSize: '0.875rem',
                      fontWeight: 600,
                      padding: '16px 20px',
                      color: '#475569',
                      textTransform: 'uppercase',
                      letterSpacing: '0.05em',
                      width: column.width,
                      position: 'sticky',
                      top: 0,
                      zIndex: 10,
                    }}
                  >
                    {column.headerName}
                  </TableCell>
                ))}
              </TableRow>
            </TableHead>

            <TableBody>
              {isLoading && !rows.length ? (
                // Loading skeleton rows
                Array.from({ length: 5 }).map((_, index) => (
                  <TableRow key={`skeleton-${index}`}>
                    {columns.map((column) => (
                      <TableCell
                        key={`skeleton-${index}-${column.field}`}
                        sx={{ padding: '16px 20px' }}
                      >
                        <Box className="animate-pulse">
                          <Box className="h-4 bg-gray-200 rounded w-3/4" />
                        </Box>
                      </TableCell>
                    ))}
                  </TableRow>
                ))
              ) : rows.length > 0 ? (
                rows.map((row, index) => (
                  <TableRow
                    key={row[idField] || row.key || index}
                    onClick={() => onRowClick && onRowClick(row)}
                    sx={{
                      cursor: onRowClick ? 'pointer' : 'default',
                      '&:hover': {
                        backgroundColor: onRowClick ? '#F8FAFC' : 'transparent',
                      },
                      '&:nth-of-type(even)': {
                        backgroundColor: '#FAFBFC',
                      },
                      transition: 'background-color 0.2s ease',
                    }}
                  >
                    {columns.map((column) => (
                      <TableCell
                        key={`${row[idField] || row.key || index}-${column.field}`}
                        sx={{
                          fontSize: '0.875rem',
                          padding: '16px 20px',
                          lineHeight: 1.5,
                          color: '#334155',
                          borderBottom: '1px solid #F1F5F9',
                        }}
                      >
                        {column.renderCell ? column.renderCell(row) : row[column.field] || (
                          <span className="text-gray-400 italic">--</span>
                        )}
                      </TableCell>
                    ))}
                  </TableRow>
                ))
              ) : (
                <TableRow>
                  <TableCell
                    colSpan={columns.length}
                    sx={{
                      textAlign: 'center',
                      padding: '48px 20px',
                      color: '#64748B',
                      fontSize: '0.875rem',
                    }}
                  >
                    <Box className="flex flex-col items-center gap-3">
                      <Box className="w-12 h-12 bg-gray-100 rounded-full flex items-center justify-center">
                        <Search className="text-gray-400" fontSize="medium" />
                      </Box>
                      <Typography variant="body2" className="text-gray-500">
                        {emptyMessage}
                      </Typography>
                    </Box>
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </TableContainer>
      </Box>

      {/* Pagination Section */}
      {totalPages > 1 && (
        <Box className="flex-shrink-0 bg-white border-t border-gray-100 px-6 py-4">
          <Box className="flex items-center justify-between">
            <Typography variant="body2" className="text-gray-600">
              Showing page {currentPage} of {totalPages}
            </Typography>
            <Pagination
              count={totalPages}
              page={currentPage}
              onChange={(_, newPage) => goToPage(newPage)}
              color="primary"
              size="small"
              sx={{
                '& .MuiPaginationItem-root': {
                  borderRadius: '6px',
                  '&.Mui-selected': {
                    backgroundColor: '#4AC2FF',
                    color: 'white',
                    '&:hover': {
                      backgroundColor: '#2186D0',
                    },
                  },
                },
              }}
            />
          </Box>
        </Box>
      )}
    </Paper>
  );
};

// BeforeSearch component for better organization
DataTable.BeforeSearch = ({ children }) => children;

export default DataTable;

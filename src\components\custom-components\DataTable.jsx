import React, { useState, <PERSON> } from 'react';
import {
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Pagination,
  TextField,
  InputAdornment,
  CircularProgress,
  Typography,
  Box,
} from '@mui/material';
import { Search } from '@mui/icons-material';
import useQueryPaginationSearch from '../global/hooks/useQueryPaginationSearch';

/**
 * DataTable - A reusable component that fetches data with built-in search and pagination
 *
 * @param {Object} props
 * @param {string} props.url - API endpoint URL
 * @param {string} [props.searchParam="search"] - Query parameter name for search
 * @param {Array<Object>} props.columns - Column definitions with { field, headerName, width, renderCell }
 * @param {string} props.idField - Field to use as unique identifier
 * @param {Object} [props.additionalParams={}] - Additional parameters to send with API requests
 * @param {Function} [props.onRowClick] - Callback when a row is clicked
 * @param {string} [props.emptyMessage="No records found"] - Message to display when no data is available
 * @param {string} [props.title] - Optional table title
 * @param {Object} [props.transformOptions] - Options for transforming data (key, label, value)
 * @param {React.ReactNode} props.children - Can include BeforeSearch component for content before search
 */
const DataTable = ({
  children,
  url,
  searchParam = 'search',
  columns,
  idField = 'id',
  additionalParams = {},
  onRowClick,
  emptyMessage = 'No records found',
  title,
  transformOptions = { key: 'id', label: 'name', value: 'id' },
}) => {
  const [searchTerm, setSearchTerm] = useState('');
  const {
    data: rows,
    isLoading,
    error,
    currentPage,
    totalPages,
    goToPage,
  } = useQueryPaginationSearch({
    url,
    searchParam,
    searchValue: searchTerm,
    debounceDelay: 500,
    transformOptions,
    enabled: true,
    initialPage: 1,
    ...additionalParams,
  });

  const beforeSearchContent = Children.toArray(children).find((child) => child.type === DataTable.BeforeSearch);

  const otherChildren = Children.toArray(children).filter((child) => child.type !== DataTable.BeforeSearch);

  if (error) {
    return (
      <Box className="p-4 text-red-500 border border-red-300 rounded bg-red-50">
        <Typography>Error loading data: {error.message}</Typography>
      </Box>
    );
  }

  return (
    <Paper className="overflow-hidden flex flex-col h-full">
      {/* Sticky Header Section */}
      <Box className="flex-shrink-0 bg-white border-b">
        <Box className="px-4 pb-2 flex justify-between items-center gap-4">
          {title && (
            <Typography variant="h6" className="font-medium">
              {title}
            </Typography>
          )}

          <div className="flex items-center gap-2">
            {beforeSearchContent && beforeSearchContent.props.children}
            <TextField
              placeholder="Search..."
              variant="outlined"
              size="small"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="min-w-[250px]"
              slotProps={{
                input: {
                  startAdornment: (
                    <InputAdornment position="start">
                      <Search />
                    </InputAdornment>
                  ),
                },
              }}
            />

            {otherChildren}
          </div>
        </Box>
      </Box>

      {/* Scrollable Table Body */}
      <Box className="flex-1 overflow-hidden">
        {isLoading && !rows.length ? (
          <Box className="flex justify-center items-center p-8">
            <CircularProgress />
          </Box>
        ) : (
          <TableContainer className="h-full">
            <Table stickyHeader>
              <TableHead>
                <TableRow>
                  {columns.map((column) => (
                    <TableCell
                      key={column.field}
                      className="!bg-gray-bg"
                      sx={{
                        fontSize: '1rem',
                        padding: '.7rem',
                        fontWeight: 600,
                        width: column.width,
                      }}
                    >
                      {column.headerName}
                    </TableCell>
                  ))}
                </TableRow>
              </TableHead>

              <TableBody>
                {rows.length > 0 ? (
                  rows.map((row) => (
                    <TableRow
                      hover
                      key={row[idField] || row.key}
                      onClick={() => onRowClick && onRowClick(row)}
                      className={onRowClick ? 'cursor-pointer' : ''}
                    >
                      {columns.map((column) => (
                        <TableCell
                          key={`${row[idField] || row.key}-${column.field}`}
                          sx={{
                            fontSize: '1rem',
                            padding: '0.67rem',
                            lineHeight: 1.2,
                          }}
                        >
                          {column.renderCell ? column.renderCell(row) : row[column.field] || '--'}
                        </TableCell>
                      ))}
                    </TableRow>
                  ))
                ) : (
                  <TableRow>
                    <TableCell colSpan={columns.length} className="text-center py-8">
                      {isLoading ? <CircularProgress size={24} /> : emptyMessage}
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </TableContainer>
        )}
      </Box>

      {/* Sticky Pagination Section */}
      {totalPages > 1 && (
        <Box className="flex-shrink-0 bg-white border-t p-4 flex justify-end">
          <Pagination
            count={totalPages}
            page={currentPage}
            onChange={(event, newPage) => goToPage(newPage)}
            color="primary"
            size="small"
          />
        </Box>
      )}
    </Paper>
  );
};

export default DataTable;

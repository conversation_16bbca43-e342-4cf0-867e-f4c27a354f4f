import React from 'react';
import styles from './InvoicePreview.module.scss';

function InvoicePreview({ fileUrl, loading = false }) {
  return (
    <div className={styles.invoicesViewContainer}>
      {loading ? (
        <div className="blankSkeleton" />
      ) : fileUrl ? (
        fileUrl.toLowerCase().endsWith('.pdf') ? (
          <object data={fileUrl} type="application/pdf" width="100%" height="100%">
            <p>
              Unable to display PDF!
              <br />
              Click{' '}
              <a href={fileUrl} target="_blank" rel="noopener noreferrer">
                here
              </a>{' '}
              to download or view it directly.
            </p>
          </object>
        ) : (
          <div className={styles.mainImageContainer}>
            <img src={fileUrl} alt="Invoice" className={styles.mainInvoiceImage} />
          </div>
        )
      ) : (
        <div className="flex items-center justify-center h-full w-full text-gray-500">No invoice file to display</div>
      )}
    </div>
  );
}

export default InvoicePreview;

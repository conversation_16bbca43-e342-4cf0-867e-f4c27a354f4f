import React from 'react';
import Empty from './Empty';
import { CircularProgress } from '@mui/material';

function LoadingWrapper({
  children,
  loading,
  error,
  LoaderWrapperClass,
  isRenderEmpty = false,
  doNotRenderChildIf = false,
  renderEmpty = null,
  minHeight = false,
  useBlankSkeleton = false,
  ...loaderProps
}) {
  if (loading) {
    return useBlankSkeleton ? (
      <div className="blankSkeleton" />
    ) : (
      <div className="flex justify-center items-center h-full absolute top-0 left-0 w-full">
        <CircularProgress size={50} color="primary" />
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex flex-col justify-center items-center h-full m-0 absolute top-0 left-0 w-full">
        <p className="text-red-600">Error: {String(error)}</p>
        <div
          className={'bg-blue-950 text-white text-lg py-2 px-5 rounded-lg cursor-pointer select-none'}
          onClick={() => window.location.reload()}
        >
          Retry
        </div>
      </div>
    );
  }

  if (renderEmpty && isRenderEmpty) {
    return <Empty title={renderEmpty.title} description={renderEmpty.description} />;
  }

  if (!doNotRenderChildIf) {
    return children;
  }

  return <></>;
}

export default LoadingWrapper;

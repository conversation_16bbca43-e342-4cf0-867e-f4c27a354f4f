export const getErrorMessage = (error, defaultMessage = 'An error occurred') => {
  const message =
    error?.response?.data?.detail?.message ||
    error?.response?.data?.detail ||
    error?.response?.data?.message ||
    error?.message ||
    defaultMessage;

  if (typeof message !== 'string') {
    return defaultMessage;
  }
  return message;
};

/**
 * Extracts filename from HTTP headers (Content-Disposition).
 * Supports both `filename=` and `filename*=` (RFC 5987) formats.
 *
 * @param {Headers|Object} headers - Headers object (browser Fetch Headers or plain object like Axios).
 * @returns {string} - Extracted filename or empty string if not found.
 */
export function extractFilenameFromHeaders(headers) {
  let contentDisposition = '';

  // Normalize for both Headers (Fetch API) and plain objects (like Axios)
  if (typeof headers.get === 'function') {
    contentDisposition = headers.get('Content-Disposition') || headers.get('content-disposition') || '';
  } else if (typeof headers === 'object') {
    contentDisposition = headers['Content-Disposition'] || headers['content-disposition'] || '';
  }

  if (!contentDisposition) return '';

  // Prefer RFC 5987 format: filename*=UTF-8''<encoded>
  const utf8Match = contentDisposition.match(/filename\*=UTF-8''([^;\r\n]+)/i);
  if (utf8Match) return decodeURIComponent(utf8Match[1]);

  // Fallback to regular filename="<filename>" or filename=<filename>
  const asciiMatch = contentDisposition.match(/filename="?([^"]+)"?/i);
  if (asciiMatch) return asciiMatch[1];

  return '';
}

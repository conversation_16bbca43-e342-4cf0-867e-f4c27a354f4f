@import '../../../assets/scss/main.scss';

.filterBtn {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0.6em 0.8em;
  border-radius: 1em;
  max-width: 8em;
  color: $primaryColor;
  background-color: $accentBgColor1;
  border: 2px solid $accentBorder1;
  transition-property: all;
  transition-duration: 200ms;
  cursor: pointer;
  -webkit-user-select: none;
  user-select: none;
  @include clickable;
  p {
    font-size: 1.3em;
    margin-left: 0.5em;
  }
  svg {
    width: 21px;
    height: 21px;
    fill: $primaryColor;
  }
}

.activeFilterBtn {
  background-color: $accentColor2;
  border-color: $accentBorder2;
  color: $white;
  transform: scale(0.98);
  transition: all 0.1s ease;
  svg {
    fill: $white;
  }
}

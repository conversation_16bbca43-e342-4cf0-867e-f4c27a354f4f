import React from 'react';
import { Box } from '@mui/material';
import ArrowU<PERSON><PERSON>Icon from '@mui/icons-material/ArrowUpward';
import ArrowDownwardIcon from '@mui/icons-material/ArrowDownward';
import UnfoldMoreIcon from '@mui/icons-material/UnfoldMore';

export default function SortIndicator({ direction = null }) {
  return (
    <Box display="flex" alignItems="center" justifyContent="center">
      {direction === 'asc' && <ArrowUpwardIcon fontSize="small" className="text-primary-color" />}
      {direction === 'desc' && <ArrowDownwardIcon fontSize="small" className="text-primary-color" />}
      {!direction && <UnfoldMoreIcon fontSize="small" />}
    </Box>
  );
}

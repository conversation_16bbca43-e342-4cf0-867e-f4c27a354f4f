import React, { useState, useRef, useEffect, useMemo } from 'react';
import { ChevronDown, Check, Loader2, Lock } from 'lucide-react';

/**
 * SelectOption - A reusable dropdown select component
 * @param {Object} props - Component props
 * @param {string} props.label - Label for the select option
 * @param {Array} props.options - Array of option objects with value and label keys
 * @param {Function} [props.onChange] - Function to call when selection changes (optional)
 * @param {string|number} props.value - Currently selected value (for controlled component)
 * @param {string|number} props.defaultValue - Default selected value (for uncontrolled component)
 * @param {string} props.placeholder - Placeholder text when no option is selected
 * @param {string} props.className - Additional CSS classes for styling
 * @param {boolean} props.loading - Loading state of the select
 * @param {boolean} props.disabled - Read only state of the select
 */
const SelectOption = ({
  label,
  options = [],
  onChange = null,
  value,
  defaultValue,
  placeholder = 'Select an option',
  className = '',
  loading = false,
  disabled = false,
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [selectedValue, setSelectedValue] = useState(value ?? defaultValue);
  const dropdownRef = useRef(null);

  useEffect(() => {
    if (value !== undefined) {
      setSelectedValue(value);
    }
  }, [value]);

  const selectedOption = useMemo(
    () => options.find((option) => option.value === selectedValue || option.label === selectedValue),
    [selectedValue, options]
  );

  useEffect(() => {
    const handleClickOutside = (event) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  const handleSelect = (option) => {
    setSelectedValue(option.value);
    if (onChange) {
      onChange(option);
    }
    setIsOpen(false);
  };

  return (
    <div className={`relative ${className}`} ref={dropdownRef}>
      {label && <label className="block text-sm font-medium text-gray-700 mb-1">{label}</label>}

      {/* Select Option button */}
      <div
        type="button"
        className={`w-fit min-w-11 flex items-center justify-between gap-2 select-none bg-[#ECF7FF] border-2 border-[#C6E8FF] rounded-full py-1 px-2 text-lg text-nowrap ${
          disabled ? 'cursor-not-allowed opacity-70' : 'cursor-pointer'
        }`}
        onClick={() => !loading && !disabled && setIsOpen(!isOpen)}
      >
        <span className={selectedOption || disabled ? 'text-[#0B1A30]' : 'text-gray-400'}>
          {selectedOption ? selectedOption.label : placeholder}
        </span>
        {loading ? (
          <Loader2 className="h-5 w-5 text-[#0B1A30] animate-spin" />
        ) : disabled ? (
          <Lock className="h-4 w-4 text-[#0B1A30]" />
        ) : (
          <ChevronDown
            className={`h-5 w-5 ${
              selectedOption ? 'text-[#0B1A30]' : 'text-gray-400'
            } transition-transform duration-150 ${isOpen ? 'transform rotate-180' : ''}`}
          />
        )}
      </div>

      {/* dropdown */}
      {isOpen && !loading && !disabled && (
        <div className="absolute z-10 mt-1 w-fit bg-white shadow-lg rounded-md py-1 text-base ring-1 ring-black ring-opacity-5 overflow-auto max-h-60">
          {options.length > 0 ? (
            options.map((option) => (
              <div
                key={option.value}
                className={`flex items-center justify-between gap-2 cursor-pointer select-none py-2 px-3 hover:bg-[#ECF7FF] ${
                  option.value === selectedValue ? 'bg-[#F5FBFF] text-[#0B1A30]' : 'text-gray-900'
                }`}
                onClick={() => handleSelect(option)}
              >
                {option.label}
                {option.value === selectedValue && (
                  <span className="ml-auto text-[#0B1A30] flex items-center">
                    <Check className="h-5 w-5" />
                  </span>
                )}
              </div>
            ))
          ) : (
            <div className="px-3 py-2 text-gray-500">No options available</div>
          )}
        </div>
      )}
    </div>
  );
};

export default SelectOption;

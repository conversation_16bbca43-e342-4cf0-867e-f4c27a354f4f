import React from 'react';
import { Route } from 'react-router-dom';
import { SuspenseWrapper } from '../components/utils/JSXUtils';

const Login = React.lazy(() => import('../components/Login'));
const ForgetPassword = React.lazy(() => import('../components/ForgotPassword'));
const Error401 = React.lazy(() => import('../components/error-pages/Error401'));
const Error500 = React.lazy(() => import('../components/error-pages/Error500'));

function PublicRoutes() {
  return (
    <>
      <Route
        path="/login"
        element={
          <SuspenseWrapper>
            <Login />
          </SuspenseWrapper>
        }
      />
      <Route
        path="/forgetPassword/:reset?"
        element={
          <SuspenseWrapper>
            <ForgetPassword />
          </SuspenseWrapper>
        }
      />
      <Route
        path="/unauthorized"
        element={
          <SuspenseWrapper>
            <Error401 />
          </SuspenseWrapper>
        }
      />
      <Route
        path="/500"
        element={
          <SuspenseWrapper>
            <Error500 />
          </SuspenseWrapper>
        }
      />
    </>
  );
}

export default PublicRoutes;

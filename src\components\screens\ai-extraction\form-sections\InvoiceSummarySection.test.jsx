import React from 'react';
import { render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import InvoiceSummarySection from './InvoiceSummarySection';
import { sectionValidation } from '../../../services/aiServices';
import { removeObjectsFromJson } from '../../../utils/jsonUtils';

jest.mock('../../../services/aiServices', () => ({
  sectionValidation: jest.fn(),
}));

jest.mock('react-router-dom', () => ({
  ...jest.requireActual('react-router-dom'),
  useParams: () => ({
    fileId: 'mock-file-id',
    businessId: 'mock-business-id',
  }),
}));

const mockFormData = {
  invoice_summary: {
    invoice_amount: '1000.00',
    freight_charges: '100.00',
    frieght_charges_ledger_name: 'Freight Ledger',
    insurance_charges: '50.00',
    insurance_charges_ledger_name: 'Insurance Ledger',
    tcs: '20.00',
    tcs_ledger_name: 'TCS Ledger',
    other_charges: '30.00',
    other_charges_ledger_name: 'Other Charges Ledger',
    discount: '40.00',
    discount_ledger_name: 'Discount Ledger',
    round_off: '10.00',
    round_off_ledger_name: 'Round Off Ledger',
  },
};

const FormWrapper = ({ initialFormData, ...restProps }) => {
  const [formData, setFormData] = React.useState(initialFormData);
  const formAction = React.useCallback((action, section, field, value) => {
    let updatedSection;

    setFormData((prevFormData) => {
      const prevSection = prevFormData[section] || {};
      switch (action) {
        case 'FIELD_CHANGE':
          updatedSection = { ...prevSection, [field]: value };
          break;

        case 'UPDATE_SECTION':
          updatedSection = { ...prevSection, ...value };
          break;

        case 'HARD_UPDATE_SECTION':
          updatedSection = value;
          break;

        default:
          return prevFormData;
      }
      return {
        ...prevFormData,
        [section]: updatedSection,
      };
    });

    return updatedSection;
  }, []);

  return (
    <InvoiceSummarySection
      formData={formData}
      setFormData={setFormData}
      isReadOnly={false}
      invoiceType="purchase"
      formAction={formAction}
      {...restProps}
    />
  );
};

describe('InvoiceSummarySection', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders all fields with correct initial values', () => {
    render(<FormWrapper initialFormData={mockFormData} />);

    expect(screen.getByLabelText('Invoice Amount')).toHaveValue('1,000.00');
    expect(screen.getByLabelText('Freight Charges')).toHaveValue('100.00');
    expect(screen.getAllByLabelText('Ledger')[0]).toHaveValue('Freight Ledger');
    expect(screen.getByLabelText('Insurance Charges')).toHaveValue('50.00');
    expect(screen.getAllByLabelText('Ledger')[1]).toHaveValue('Insurance Ledger');
    expect(screen.getByLabelText('TCS')).toHaveValue('20.00');
    expect(screen.getAllByLabelText('Ledger')[2]).toHaveValue('TCS Ledger');
    expect(screen.getByLabelText('Other Charges')).toHaveValue('30.00');
    expect(screen.getAllByLabelText('Ledger')[3]).toHaveValue('Other Charges Ledger');
    expect(screen.getByLabelText('Discount')).toHaveValue('40.00');
    expect(screen.getAllByLabelText('Ledger')[4]).toHaveValue('Discount Ledger');
    expect(screen.getByLabelText('Round Off')).toHaveValue('10.00');
    expect(screen.getAllByLabelText('Ledger')[5]).toHaveValue('Round Off Ledger');
  });

  it('shows initial suggestions when available for invoice amount', () => {
    const formDataWithSuggestions = {
      invoice_summary: {
        ...mockFormData.invoice_summary,
        recommended_fields: [
          {
            invoice_amount: '2000.00',
          },
        ],
      },
    };

    render(<FormWrapper initialFormData={formDataWithSuggestions} />);
    expect(screen.getByTestId('copy-icon-invoice_amount')).toBeInTheDocument();
    expect(screen.getByTestId('paste-icon-invoice_amount')).toBeInTheDocument();
  });

  it('shows initial error messages & indicators correctly', async () => {
    const formDataWithErrors = {
      invoice_summary: {
        ...mockFormData.invoice_summary,
        error: {
          invoice_amount: {
            short_message: 'Invalid amount',
            long_message: 'Please enter a valid invoice amount',
          },
        },
      },
    };

    render(<FormWrapper initialFormData={formDataWithErrors} />);

    const errorIcons = screen.getAllByTestId('errorIcon');
    expect(errorIcons).toHaveLength(1);

    const amountTooltip = await screen.findByTestId('tooltip-invoice_amount');
    await userEvent.hover(amountTooltip);
    const tooltip = await screen.findByText('Invalid amount');
    expect(tooltip).toBeInTheDocument();
  });

  it('shows initial warning messages & indicators correctly', async () => {
    const formDataWithWarnings = {
      invoice_summary: {
        ...mockFormData.invoice_summary,
        warning: {
          invoice_amount: {
            short_message: 'Amount might be incorrect',
            long_message: 'Please verify the invoice amount',
          },
        },
      },
    };

    render(<FormWrapper initialFormData={formDataWithWarnings} />);

    const warningIcons = screen.getAllByTestId('warningIcon');
    expect(warningIcons).toHaveLength(1);

    const amountTooltip = await screen.findByTestId('tooltip-invoice_amount');
    await userEvent.hover(amountTooltip);
    const amountTooltipContent = await screen.findByText('Amount might be incorrect');
    expect(amountTooltipContent).toBeInTheDocument();
  });

  it('shows initial exact match indicators when present', async () => {
    const formDataWithSuccess = {
      invoice_summary: {
        ...mockFormData.invoice_summary,
        exact_match: {
          invoice_amount: true,
        },
      },
    };

    render(<FormWrapper initialFormData={formDataWithSuccess} />);

    const successIcons = screen.getAllByTestId('successIcon');
    expect(successIcons).toHaveLength(1);
  });

  it('handles suggestion paste functionality correctly for invoice amount', async () => {
    const formDataWithSuggestions = {
      invoice_summary: {
        ...mockFormData.invoice_summary,
        recommended_fields: [
          {
            invoice_amount: '3000.00',
          },
        ],
      },
    };

    render(<FormWrapper initialFormData={formDataWithSuggestions} />);

    const amountPasteIcon = screen.getByTestId('paste-icon-invoice_amount');

    await userEvent.click(amountPasteIcon);
    await waitFor(() => {
      expect(screen.getByLabelText('Invoice Amount')).toHaveValue('3,000.00');
    });
  });

  it('validates field on input change and blur for invoice amount', async () => {
    const newValue = '4000.00';
    render(<FormWrapper initialFormData={mockFormData} />);

    const mockResponse = {
      invoice_summary: {
        ...mockFormData.invoice_summary,
        invoice_amount: newValue,
        error: {
          invoice_amount: {
            short_message: 'Invalid invoice amount',
            long_message: 'Please enter a valid invoice amount',
          },
        },
      },
    };

    sectionValidation.mockResolvedValue(mockResponse);

    const amountInput = screen.getByLabelText('Invoice Amount');
    await userEvent.clear(amountInput);
    await userEvent.type(amountInput, '4000');
    await userEvent.tab();

    expect(amountInput).toHaveValue('4,000.00');

    const expectedPayload = {
      invoice_summary: {
        ...removeObjectsFromJson(mockFormData.invoice_summary, [
          'error',
          'warning',
          'exact_match',
          'recommended_fields',
        ]),
        invoice_amount: newValue,
      },
    };

    expect(sectionValidation).toHaveBeenCalledWith(
      'mock-file-id',
      'mock-business-id',
      'invoice_summary',
      'purchase',
      expectedPayload
    );

    await waitFor(() => {
      expect(screen.getByTestId('errorIcon')).toBeInTheDocument();
    });
  });
});

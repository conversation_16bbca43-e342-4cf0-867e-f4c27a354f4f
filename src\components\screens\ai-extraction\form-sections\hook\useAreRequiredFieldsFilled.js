import { useState, useMemo } from 'react';

/**
 * Custom hook to check if required fields are filled
 * @param {Object|Array} data - data to check
 * @param {Array} initialRequiredFields - Array of field names or objects with field and index properties
 * @param {boolean} isArrayData - Whether the data is an array of objects
 * @param {boolean} indexWise - Whether to check fields based on index mapping
 * @returns {[boolean, Function]} - Returns whether fields are filled and a function to update required fields
 */
function useAreRequiredFieldsFilled(data, initialRequiredFields = [], isArrayData = false, indexWise = false) {
  const [requiredFields, setRequiredFields] = useState(initialRequiredFields);

  const isRequiredFieldsFilled = useMemo(() => {
    if (!data) return false;

    /**
     * Handle index wise validation, where requiredFields are array of objects
     *  with field & index properties
     * Example: [{ field: 'name', index: 0 }, { field: 'price', index: 1 }]
     */
    if (isArrayData && Array.isArray(data) && indexWise) {
      if (data.length === 0) return true;

      // group required fields by index
      const fieldsByIndex = {};
      requiredFields.forEach((fieldObj) => {
        if (fieldObj?.index !== undefined && fieldObj?.field) {
          if (!fieldsByIndex[fieldObj.index]) {
            fieldsByIndex[fieldObj.index] = [];
          }
          fieldsByIndex[fieldObj.index].push(fieldObj.field);
        }
      });

      // Check each index that has required fields
      for (const indexStr in fieldsByIndex) {
        const index = parseInt(indexStr, 10);
        if (index >= data.length) continue;

        const item = data[index];
        const fieldsForThisIndex = fieldsByIndex[indexStr];

        // Check if all required fields for this index are filled
        const allFieldsFilled = fieldsForThisIndex.every((field) => {
          const value = item[field];
          return value !== undefined && value !== null && value.toString().trim() !== '';
        });

        if (!allFieldsFilled) return false;
      }

      return true;
    }

    // Handle regular array data (like product services items)
    if (isArrayData && Array.isArray(data)) {
      if (data.length === 0) return true;
      return data.every((item) =>
        requiredFields.every((field) => {
          const value = item[field];
          return value !== undefined && value !== null && value.toString().trim() !== '';
        })
      );
    }

    return requiredFields.every((field) => {
      const value = data[field];
      return value !== undefined && value !== null && value.toString().trim() !== '';
    });
  }, [data, requiredFields, isArrayData, indexWise]);

  return [isRequiredFieldsFilled, setRequiredFields];
}

export default useAreRequiredFieldsFilled;

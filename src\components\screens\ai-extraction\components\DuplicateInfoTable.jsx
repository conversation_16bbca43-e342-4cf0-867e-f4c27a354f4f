import { ChevronRight } from 'lucide-react';
import React from 'react';
import { useParams } from 'react-router-dom';

function renderCellContent(value) {
  return value ? <MatchIcon /> : <span className="text-base">--</span>;
}

function DuplicateInfoTable({ data }) {
  const { businessId, invoicePage } = useParams();

  const handleRowClick = (item) => {
    const url = `/ai-extraction/f/${item?.file_id}/b/${businessId}/p/${invoicePage}`;
    window.open(url, '_blank', 'noopener,noreferrer');
  };
  return (
    <div className="flex flex-col items-center justify-center px-4 overflow-x-auto">
      <div className="flex items-center justify-center my-4 w-full">
        <div className="h-[2px] bg-gray-300 flex-grow mx-2" />
        <div className="uppercase px-1 py-1 text-lg font-medium text-gray-900 flex items-center">
          <MatchIcon />
          <span className="tracking-wide">ATCHING FIELDS</span>
        </div>
        <div className="h-[2px] bg-gray-300 flex-grow mx-2" />
      </div>
      <table className="min-w-full divide-y divide-gray-200">
        <thead>
          <tr>
            <th className="px-3 py-2 text-center text-base font-medium text-gray-500 uppercase tracking-wider">
              Invoice Number
            </th>
            <th className="px-3 py-2 text-center text-base font-medium text-gray-500 uppercase tracking-wider">
              Supplier
            </th>
            <th className="px-3 py-2 text-center text-base font-medium text-gray-500 uppercase tracking-wider">
              Invoice Number
            </th>
            <th className="px-3 py-2 text-center text-base font-medium text-gray-500 uppercase tracking-wider">Date</th>
            <th className="px-3 py-2 text-center text-base font-medium text-gray-500 uppercase tracking-wider">
              Amount
            </th>
            <th className="px-3 py-2 w-10">{/* Empty header for arrow button */}</th>
          </tr>
        </thead>
        <tbody className="bg-white divide-y divide-gray-200">
          {data.map((item, index) => (
            <tr
              key={item?.file_id || index}
              className="bg-white cursor-pointer hover:bg-gray-50"
              onClick={() => handleRowClick(item)}
            >
              <td className="py-3 whitespace-nowrap text-sm text-gray-900 text-center">
                <span className="text-base">{item?.invoice_number || '--'}</span>
              </td>
              <td className="py-3 whitespace-nowrap text-sm text-gray-900 text-center">
                {renderCellContent(item?.match_supplier_name)}
              </td>
              <td className="py-3 whitespace-nowrap text-sm text-gray-900 text-center">
                {renderCellContent(item?.match_invoice_number)}
              </td>
              <td className="py-3 whitespace-nowrap text-sm text-gray-900 text-center">
                {renderCellContent(item?.match_invoice_date)}
              </td>
              <td className="py-3 whitespace-nowrap text-sm text-gray-900 text-center">
                {renderCellContent(item?.match_invoice_amount)}
              </td>
              <td className="py-3 whitespace-nowrap text-center">
                <ChevronRight className="h-6 w-6 text-gray-600 rounded-full hover:bg-gray-200 transition-colors" />
              </td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
}

function MatchIcon() {
  return (
    <div className="inline-flex justify-center items-center">
      <span className="flex justify-center items-center text-white text-sm p-1 h-5 w-5 rounded-full bg-gray-600">
        M
      </span>
    </div>
  );
}
export default DuplicateInfoTable;

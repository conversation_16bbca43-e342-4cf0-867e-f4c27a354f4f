import React, { useCallback, useMemo } from 'react';
import <PERSON><PERSON><PERSON> from './components/AiField';
import { getFieldAlertObject } from '../../../../components/utils/aiUtils';

import ModalDropdown from './components/ModalDropdown';
import { resturls } from '../../../utils/apiurls';
import { useParams } from 'react-router-dom';
import useRealTimeValidation from './hook/useRealTimeValidation';
import { NumericFormat } from 'react-number-format';
import { formatIndianCurrency } from '../../../utils/dateUtils';

const SECTION = 'invoice_summary';

function InvoiceSummarySection({ formData, isReadOnly, invoiceType, formAction, setFormData }) {
  const data = formData[SECTION] || {};
  const { businessId } = useParams();
  const purchaseLedgerUrl = `${resturls.getPurchaseLedger}/search?business_id=${businessId}&ledger_type=expense`;
  const isZoho = useMemo(() => formData?.accounting_platform?.toLowerCase() === 'zoho', []);
  const [handleValidate, suggestions] = useRealTimeValidation({
    data,
    formAction,
    defaultSuggestion: data?.recommended_fields?.[0],
    isInventoryEnabled: formData?.is_inventory_enabled,
  });

  const handleOnPaste = useCallback((copyText, field) => {
    setFormData((prevData) => {
      const prevSection = prevData[SECTION] || {};
      const updatedSection = {
        ...prevSection,
        [field]: copyText?.replace(/,/g, ''),
      };
      const mergedData = {
        sales_of_product_services: prevData?.sales_of_product_services,
        gst_ledgers: prevData?.gst_ledgers,
        [SECTION]: updatedSection,
      };
      handleValidate(SECTION, invoiceType, mergedData, true);
      return {
        ...prevData,
        [SECTION]: updatedSection,
      };
    });
  }, []);

  const handleBlur = useCallback(() => {
    const mergedData = {
      sales_of_product_services: formData?.sales_of_product_services,
      gst_ledgers: formData?.gst_ledgers,
      invoice_summary: data,
    };
    handleValidate(SECTION, invoiceType, mergedData, true);
  }, [formData]);

  return (
    <div className="form-grid">
      {/* Invoice Amount */}
      <AiField
        label="Invoice Amount"
        isExactMatch={data?.exact_match?.invoice_amount}
        alertObject={getFieldAlertObject(data, 'invoice_amount')}
        required
        name="invoice_amount"
        id="invoice_amount"
        className="only-1-column"
        copyText={formatIndianCurrency(suggestions?.invoice_amount, true)}
        onPaste={handleOnPaste}
        disabled={isReadOnly}
        renderCustomField={() => (
          <NumericFormat
            value={data?.invoice_amount ?? ''}
            thousandSeparator={true}
            thousandsGroupStyle="lakh"
            decimalScale={2}
            fixedDecimalScale
            allowNegative={true}
            className="input-field text-right"
            disabled={isReadOnly}
            onBlur={handleBlur}
            id="invoice_amount"
            onValueChange={(values) => {
              formAction('FIELD_CHANGE', SECTION, 'invoice_amount', values.value);
            }}
          />
        )}
        alignSuggestionRight={true}
      />

      {/* Freight Charges + ledger, Insurance Charges + ledger */}
      <div className="only-4-column">
        {/* Freight Charges */}
        <AiField
          label="Freight Charges"
          isExactMatch={data?.exact_match?.freight_charges}
          alertObject={getFieldAlertObject(data, 'freight_charges')}
          name="freight_charges"
          id="freight_charges"
          disabled={isReadOnly}
          renderCustomField={() => (
            <NumericFormat
              value={data?.freight_charges ?? ''}
              thousandSeparator={true}
              thousandsGroupStyle="lakh"
              decimalScale={2}
              fixedDecimalScale
              allowNegative={true}
              className="input-field text-right"
              disabled={isReadOnly}
              onBlur={handleBlur}
              id="freight_charges"
              onValueChange={(values) => {
                formAction('FIELD_CHANGE', SECTION, 'freight_charges', values.value);
              }}
            />
          )}
        />

        {/* Freight Charges Ledger */}
        <div>
          <AiField
            label="Ledger"
            isExactMatch={data?.exact_match?.frieght_charges_ledger_name}
            alertObject={getFieldAlertObject(data, 'frieght_charges_ledger_name')}
            type="text"
            name="frieght_charges_ledger_name"
            id="frieght_charges_ledger_name"
            value={data?.frieght_charges_ledger_name ?? ''}
            onChange={(e) => formAction('FIELD_CHANGE', SECTION, 'frieght_charges_ledger_name', e.target.value)}
            readOnly
            disabled={isReadOnly}
            required={Number(data?.freight_charges) !== 0}
          />
          <ModalDropdown
            label="Select Ledger"
            url={purchaseLedgerUrl}
            searchParamName="ledger_name"
            onSelect={(option) =>
              formAction('UPDATE_SECTION', SECTION, null, {
                frieght_charges_ledger_name: option.label,
                frieght_charges_ledger_id: String(option.key),
              })
            }
            transformOptionsObj={{
              key: 'master_id',
              value: 'master_id',
              label: 'ledger_name',
            }}
            disabled={isReadOnly}
            isShowRefreshButton={isZoho}
          />
        </div>

        {/* Insurance Charges */}
        <AiField
          label="Insurance Charges"
          isExactMatch={data?.exact_match?.insurance_charges}
          alertObject={getFieldAlertObject(data, 'insurance_charges')}
          name="insurance_charges"
          id="insurance_charges"
          disabled={isReadOnly}
          renderCustomField={() => (
            <NumericFormat
              value={data?.insurance_charges ?? ''}
              thousandSeparator={true}
              thousandsGroupStyle="lakh"
              decimalScale={2}
              fixedDecimalScale
              allowNegative={true}
              className="input-field text-right"
              disabled={isReadOnly}
              onBlur={handleBlur}
              id="insurance_charges"
              onValueChange={(values) => {
                formAction('FIELD_CHANGE', SECTION, 'insurance_charges', values.value);
              }}
            />
          )}
        />

        {/* Insurance Charges Ledger */}
        <div>
          <AiField
            label="Ledger"
            isExactMatch={data?.exact_match?.insurance_charges_ledger_name}
            alertObject={getFieldAlertObject(data, 'insurance_charges_ledger_name')}
            type="text"
            name="insurance_charges_ledger_name"
            id="insurance_charges_ledger_name"
            value={data?.insurance_charges_ledger_name ?? ''}
            onChange={(e) => formAction('FIELD_CHANGE', SECTION, 'insurance_charges_ledger_name', e.target.value)}
            readOnly
            disabled={isReadOnly}
            required={Number(data?.insurance_charges) !== 0}
          />
          <ModalDropdown
            label="Select Ledger"
            url={purchaseLedgerUrl}
            searchParamName="ledger_name"
            onSelect={(option) =>
              formAction('UPDATE_SECTION', SECTION, null, {
                insurance_charges_ledger_name: option.label,
                insurance_charges_ledger_id: String(option.key),
              })
            }
            transformOptionsObj={{
              key: 'master_id',
              value: 'master_id',
              label: 'ledger_name',
            }}
            disabled={isReadOnly}
            isShowRefreshButton={isZoho}
          />
        </div>
      </div>

      {/* TCS + ledger, Other Charges + ledger */}
      <div className="only-4-column">
        {/* TCS */}
        <AiField
          label="TCS"
          isExactMatch={data?.exact_match?.tcs}
          alertObject={getFieldAlertObject(data, 'tcs')}
          name="tcs"
          id="tcs"
          disabled={isReadOnly}
          renderCustomField={() => (
            <NumericFormat
              value={data?.tcs ?? ''}
              thousandSeparator={true}
              thousandsGroupStyle="lakh"
              decimalScale={2}
              fixedDecimalScale
              allowNegative={true}
              className="input-field text-right"
              disabled={isReadOnly}
              onBlur={handleBlur}
              id="tcs"
              onValueChange={(values) => {
                formAction('FIELD_CHANGE', SECTION, 'tcs', values.value);
              }}
            />
          )}
        />

        {/* TCS Ledger */}
        <div>
          <AiField
            label="Ledger"
            isExactMatch={data?.exact_match?.tcs_ledger_name}
            alertObject={getFieldAlertObject(data, 'tcs_ledger_name')}
            type="text"
            name="tcs_ledger_name"
            id="tcs_ledger_name"
            value={data?.tcs_ledger_name ?? ''}
            onChange={(e) => formAction('FIELD_CHANGE', SECTION, 'tcs_ledger_name', e.target.value)}
            readOnly
            disabled={isReadOnly}
            required={Number(data?.tcs) !== 0}
          />
          <ModalDropdown
            label="Select Ledger"
            url={purchaseLedgerUrl}
            searchParamName="ledger_name"
            onSelect={(option) =>
              formAction('UPDATE_SECTION', SECTION, null, {
                tcs_ledger_name: option.label,
                tcs_ledger_id: String(option.key),
              })
            }
            transformOptionsObj={{
              key: 'master_id',
              value: 'master_id',
              label: 'ledger_name',
            }}
            disabled={isReadOnly}
            isShowRefreshButton={isZoho}
          />
        </div>

        {/* Other Charges */}
        <AiField
          label="Other Charges"
          isExactMatch={data?.exact_match?.other_charges}
          alertObject={getFieldAlertObject(data, 'other_charges')}
          name="other_charges"
          id="other_charges"
          disabled={isReadOnly}
          renderCustomField={() => (
            <NumericFormat
              value={data?.other_charges ?? ''}
              thousandSeparator={true}
              thousandsGroupStyle="lakh"
              decimalScale={2}
              fixedDecimalScale
              allowNegative={true}
              className="input-field text-right"
              disabled={isReadOnly}
              onBlur={handleBlur}
              id="other_charges"
              onValueChange={(values) => {
                formAction('FIELD_CHANGE', SECTION, 'other_charges', values.value);
              }}
            />
          )}
        />

        {/* Other Charges Ledger */}
        <div>
          <AiField
            label="Ledger"
            isExactMatch={data?.exact_match?.other_charges_ledger_name}
            alertObject={getFieldAlertObject(data, 'other_charges_ledger_name')}
            type="text"
            name="other_charges_ledger_name"
            id="other_charges_ledger_name"
            value={data?.other_charges_ledger_name ?? ''}
            onChange={(e) => formAction('FIELD_CHANGE', SECTION, 'other_charges_ledger_name', e.target.value)}
            readOnly
            disabled={isReadOnly}
            required={Number(data?.other_charges) !== 0}
          />
          <ModalDropdown
            label="Select Ledger"
            url={purchaseLedgerUrl}
            searchParamName="ledger_name"
            onSelect={(option) =>
              formAction('UPDATE_SECTION', SECTION, null, {
                other_charges_ledger_name: option.label,
                other_charges_ledger_id: String(option.key),
              })
            }
            transformOptionsObj={{
              key: 'master_id',
              value: 'master_id',
              label: 'ledger_name',
            }}
            disabled={isReadOnly}
            isShowRefreshButton={isZoho}
          />
        </div>
      </div>

      {/* Discount + ledger, Round Off + ledger */}
      <div className="only-4-column">
        {/* Discount */}
        <AiField
          label="Discount"
          isExactMatch={data?.exact_match?.discount}
          alertObject={getFieldAlertObject(data, 'discount')}
          name="discount"
          id="discount"
          disabled={isReadOnly}
          renderCustomField={() => (
            <NumericFormat
              value={data?.discount ?? ''}
              thousandSeparator={true}
              thousandsGroupStyle="lakh"
              decimalScale={2}
              fixedDecimalScale
              allowNegative={true}
              className="input-field text-right"
              disabled={isReadOnly}
              onBlur={handleBlur}
              id="discount"
              onValueChange={(values) => {
                formAction('FIELD_CHANGE', SECTION, 'discount', values.value);
              }}
            />
          )}
        />

        {/* Discount Ledger */}
        <div>
          <AiField
            label="Ledger"
            isExactMatch={data?.exact_match?.discount_ledger_name}
            alertObject={getFieldAlertObject(data, 'discount_ledger_name')}
            type="text"
            name="discount_ledger_name"
            id="discount_ledger_name"
            value={data?.discount_ledger_name ?? ''}
            onChange={(e) => formAction('FIELD_CHANGE', SECTION, 'discount_ledger_name', e.target.value)}
            readOnly
            disabled={isReadOnly}
            required={Number(data?.discount) !== 0}
          />
          <ModalDropdown
            label="Select Ledger"
            url={purchaseLedgerUrl}
            searchParamName="ledger_name"
            onSelect={(option) =>
              formAction('UPDATE_SECTION', SECTION, null, {
                discount_ledger_name: option.label,
                discount_ledger_id: String(option.key),
              })
            }
            transformOptionsObj={{
              key: 'master_id',
              value: 'master_id',
              label: 'ledger_name',
            }}
            disabled={isReadOnly}
            isShowRefreshButton={isZoho}
          />
        </div>

        {/* Round Off */}
        <AiField
          label="Round Off"
          isExactMatch={data?.exact_match?.round_off}
          alertObject={getFieldAlertObject(data, 'round_off')}
          name="round_off"
          id="round_off"
          disabled={isReadOnly}
          renderCustomField={() => (
            <NumericFormat
              value={data?.round_off ?? ''}
              thousandSeparator={true}
              thousandsGroupStyle="lakh"
              decimalScale={2}
              fixedDecimalScale
              allowNegative={true}
              className="input-field text-right"
              disabled={isReadOnly}
              onBlur={handleBlur}
              id="round_off"
              onValueChange={(values) => {
                formAction('FIELD_CHANGE', SECTION, 'round_off', values.value);
              }}
            />
          )}
        />

        {/* Round Off Ledger */}
        <div>
          <AiField
            label="Ledger"
            isExactMatch={data?.exact_match?.round_off_ledger_name}
            alertObject={getFieldAlertObject(data, 'round_off_ledger_name')}
            type="text"
            name="round_off_ledger_name"
            id="round_off_ledger_name"
            value={data?.round_off_ledger_name ?? ''}
            onChange={(e) => formAction('FIELD_CHANGE', SECTION, 'round_off_ledger_name', e.target.value)}
            readOnly
            disabled={isReadOnly}
            required={Number(data?.round_off) !== 0}
          />
          <ModalDropdown
            label="Select Ledger"
            url={purchaseLedgerUrl}
            searchParamName="ledger_name"
            onSelect={(option) =>
              formAction('UPDATE_SECTION', SECTION, null, {
                round_off_ledger_name: option.label,
                round_off_ledger_id: String(option.key),
              })
            }
            transformOptionsObj={{
              key: 'master_id',
              value: 'master_id',
              label: 'ledger_name',
            }}
            disabled={isReadOnly}
            isShowRefreshButton={isZoho}
          />
        </div>
      </div>
    </div>
  );
}

export default InvoiceSummarySection;

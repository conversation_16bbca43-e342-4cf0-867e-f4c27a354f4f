import React from 'react';
import useServiceFetch from '../../../../global/hooks/useServiceFetch';
import ticketServices from '../../../../services/ticketServices';
import LoadingWrapper from '../../../../global/components/LoadingWrapper';
import style from './scss/ticketPendingLists.module.scss';
import TicketsListDualDetailTable from '../../../../custom-components/TicketsListDualDetailTable';

function TicketPendingLists() {
  const { data, loading, error } = useServiceFetch(() => ticketServices.getTicktsByStatus('Pending'));

  return (
    <div className={style.ticketListContainer}>
      <div className={style.headerPart}>
        <div>
          <h4 className={style.separate}>Pending List</h4>
        </div>
      </div>
      <LoadingWrapper
        loading={loading}
        error={error}
        isRenderEmpty={data?.results?.length < 1}
        renderEmpty={{
          title: 'No Pending List',
          description:
            'Your accountant hasn’t flagged any tasks for you at the moment. When something needs your attention, It’ll show up here',
        }}
      >
        <TicketsListDualDetailTable tickets={data?.results} />
      </LoadingWrapper>
    </div>
  );
}

export default TicketPendingLists;

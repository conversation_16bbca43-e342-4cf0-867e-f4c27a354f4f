@import '../../../../../assets/scss/main.scss';

.attachmentsContainer {
  width: 97%;
  display: flex;
  flex-direction: column;
  gap: 0.7em;
  margin-top: 0.2em;
  overflow: hidden;
}

.noneImageContainer {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 95%;
  border: 1px solid #d7d7d7;
  gap: 2em;
  padding: 0.5em;
  border-radius: 10px;
  margin: 0.5em 0;
  cursor: pointer;
  position: relative;
  @include for_media(mobileScreen) {
    width: 100%;
    gap: 0;
  }
  img {
    width: 35px !important;
    height: 35px !important;
  }
  p {
    font-size: 1.1em !important;
    margin: 0;
    width: 60%;
  }
  .fileNameWrapper {
    display: flex;
    align-items: center;
    gap: 1em;
    width: 100%;
  }
  .btnWrapper {
    display: flex;
    align-items: center;
    gap: 1em;
    margin: 0 0.5em;
  }
  .noneImageWrapper {
    padding: 0.3em;
    border-radius: 7px;
    height: 100%;
    background-color: #f7f7f7 !important;
    border: 1px solid #d7d7d7;
    flex-shrink: 0;
  }
}

.imageWrapper {
  display: flex;
  align-items: center;
  width: 100%;
  gap: 1em;
  img {
    border-radius: 10px;
    cursor: pointer;
    max-width: 100px !important;
    max-height: 100px !important;
  }
}

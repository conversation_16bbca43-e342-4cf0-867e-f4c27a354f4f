import React from 'react';
import { Route } from 'react-router-dom';
import ProtectedRoute from './ProtectedRoute';
import LayoutWrapper from '../layout/LayoutWrapper';
import { Suspense } from 'react';
import HomeScreen from '../components/screens/home/<USER>';
import { CircularProgress } from '@mui/material';

// Lazy loaded components
const BusinessCreation = React.lazy(() => import('../components/screens/BusinessCreation'));
const BusinessDetailed = React.lazy(() => import('../components/screens/BusinessDetailed'));
const TicketDetails = React.lazy(() => import('../components/screens/TicketDetails'));
const SystemActivityPage = React.lazy(() => import('../components/screens/SystemActivityPage'));
const EmailServerConfPage = React.lazy(() => import('../components/screens/EmailServerConf'));
const FileManagementAdmin = React.lazy(() => import('../components/screens/FileManagementConf'));
const OrganisationUserList = React.lazy(() => import('../components/screens/OrganisationUsersList'));
const ArthTattvaUserManagement = React.lazy(() => import('../components/screens/ArthTattvaUserManagement'));
const TicketManagement = React.lazy(() => import('../components/screens/TicketManagement'));
const BusinessList = React.lazy(() => import('../components/screens/BusinessList'));

const SuspenseContent = ({ children }) => (
  <Suspense
    fallback={
      <div className="absolute top-0 left-0 h-full w-full flex items-center justify-center">
        <CircularProgress size={50} color="primary" />
      </div>
    }
  >
    {children}
  </Suspense>
);

const SuperuserRoutes = () => {
  return [
    <Route
      key="home"
      path="/"
      element={
        <ProtectedRoute requiredRole="superuser">
          <SuspenseContent>
            <HomeScreen />
          </SuspenseContent>
        </ProtectedRoute>
      }
    />,
    <Route
      key="businessList"
      path="/businessList/"
      element={
        <ProtectedRoute requiredRole="superuser">
          <SuspenseContent>
            <BusinessList />
          </SuspenseContent>
        </ProtectedRoute>
      }
    />,
    <Route
      key="businessCreation"
      path="/businessCreation/"
      element={
        <ProtectedRoute requiredRole="superuser">
          <LayoutWrapper>
            <SuspenseContent>
              <BusinessCreation />
            </SuspenseContent>
          </LayoutWrapper>
        </ProtectedRoute>
      }
    />,
    <Route
      key="businessDetail"
      path="/businessDetail/:BuisnessId?"
      element={
        <ProtectedRoute requiredRole="superuser">
          <SuspenseContent>
            <BusinessDetailed />
          </SuspenseContent>
        </ProtectedRoute>
      }
    />,
    <Route
      key="ticketDetails"
      path="/ticketDetails/:ticketId?"
      element={
        <ProtectedRoute requiredRole="superuser">
          <SuspenseContent>
            <TicketDetails />
          </SuspenseContent>
        </ProtectedRoute>
      }
    />,
    <Route
      key="systemActivityPage"
      path="/SystemActivityPage"
      element={
        <ProtectedRoute requiredRole="superuser">
          <SuspenseContent>
            <SystemActivityPage />
          </SuspenseContent>
        </ProtectedRoute>
      }
    />,
    <Route
      key="emailServerPage"
      path="/EmailServerPage"
      element={
        <ProtectedRoute requiredRole="superuser">
          <SuspenseContent>
            <EmailServerConfPage />
          </SuspenseContent>
        </ProtectedRoute>
      }
    />,
    <Route
      key="fileManagement"
      path="/FileManagement"
      element={
        <ProtectedRoute requiredRole="superuser">
          <SuspenseContent>
            <FileManagementAdmin />
          </SuspenseContent>
        </ProtectedRoute>
      }
    />,
    <Route
      key="organisationUsers"
      path="/organisationUsers"
      element={
        <ProtectedRoute requiredRole="superuser">
          <SuspenseContent>
            <OrganisationUserList />
          </SuspenseContent>
        </ProtectedRoute>
      }
    />,
    <Route
      key="arthtatavaUsers"
      path="/arthtatavaUsers"
      element={
        <ProtectedRoute requiredRole="superuser">
          <SuspenseContent>
            <ArthTattvaUserManagement />
          </SuspenseContent>
        </ProtectedRoute>
      }
    />,
    <Route
      key="ticketManagement"
      path="/ticketManagement"
      element={
        <ProtectedRoute requiredRole="superuser">
          <SuspenseContent>
            <TicketManagement />
          </SuspenseContent>
        </ProtectedRoute>
      }
    />,
  ];
};

export default SuperuserRoutes;

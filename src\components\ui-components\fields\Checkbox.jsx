import React, { useState } from 'react';
import { Check, Minus } from 'lucide-react';

const LoadingSpinner = ({ className }) => (
  <div className={`animate-spin ${className}`}>
    <div className="h-full w-full rounded-full border-2 border-t-primary border-r-primary border-b-transparent border-l-transparent" />
  </div>
);

const getSizes = (size) => {
  switch (size) {
    case 'small':
      return {
        box: 'w-4 h-4',
        text: 'text-sm',
        checkmark: 'w-3 h-3',
        spinner: 'w-3 h-3',
      };
    case 'large':
      return {
        box: 'w-6 h-6',
        text: 'text-lg',
        checkmark: 'w-5 h-5',
        spinner: 'w-5 h-5',
      };
    default: // medium
      return {
        box: 'w-5 h-5',
        text: 'text-md',
        checkmark: 'w-4 h-4',
        spinner: 'w-4 h-4',
      };
  }
};

const colorVariants = {
  primary: {
    bg: 'bg-[#2186D0]',
    border: 'border-[#2186D0]',
    text: 'text-[#2186D0]',
  },
  secondary: {
    bg: 'bg-gray-600',
    border: 'border-gray-600',
    text: 'text-gray-600',
  },
  success: {
    bg: 'bg-green-600',
    border: 'border-green-600',
    text: 'text-green-600',
  },
  danger: {
    bg: 'bg-red-600',
    border: 'border-red-600',
    text: 'text-red-600',
  },
};

const Checkbox = ({
  label = '',
  defaultChecked = false,
  checked,
  indeterminate = false,
  onChange = null,
  className = '',
  size = 'medium',
  disabled = false,
  loading = false,
  color = 'primary',
  id,
  name,
  labelPosition = 'right',
}) => {
  const [internalChecked, setInternalChecked] = useState(defaultChecked);
  const safeSize = ['small', 'medium', 'large'].includes(size) ? size : 'medium';
  const sizes = getSizes(safeSize);

  const isChecked = checked !== undefined ? checked : internalChecked;

  const safeColor = Object.keys(colorVariants).includes(color) ? color : 'primary';
  const colorClasses = colorVariants[safeColor];

  const handleChange = () => {
    if (disabled || loading) return;
    const newValue = !isChecked;
    // Only update internal state if in uncontrolled mode
    if (checked === undefined) {
      setInternalChecked(newValue);
    }
    onChange && onChange(newValue);
  };

  return (
    <div
      className={`flex items-center select-none ${disabled || loading ? 'cursor-not-allowed' : 'cursor-pointer'} ${
        labelPosition === 'left' ? 'flex-row-reverse' : 'flex-row'
      } ${className}`}
      onClick={handleChange}
    >
      <div
        className={`${sizes?.box || 'w-5 h-5'} flex items-center justify-center rounded-md ${
          isChecked || indeterminate
            ? `${disabled ? 'bg-gray-500' : colorClasses?.bg || 'bg-[#4e5ba6]'}`
            : `border-2 ${disabled ? 'border-gray-500' : colorClasses?.border || 'border-[#4e5ba6]'}`
        } ${disabled || loading ? 'opacity-60' : ''}`}
      >
        {loading ? (
          <LoadingSpinner className={sizes?.spinner || 'w-4 h-4'} />
        ) : indeterminate ? (
          <Minus className={`stroke-white ${sizes?.checkmark || 'w-4 h-4'}`} />
        ) : (
          isChecked && <Check className={`stroke-white ${sizes?.checkmark || 'w-4 h-4'}`} />
        )}
      </div>

      {label && (
        <span
          className={`text-nowrap ${labelPosition === 'left' ? 'mr-2' : 'ml-2'} ${sizes?.text || 'text-md'} ${
            disabled || loading ? 'text-gray-400' : colorClasses?.text || 'text-[#4e5ba6]'
          }`}
        >
          {label}
        </span>
      )}

      {/* Hidden actual checkbox for form submission */}
      <input
        type="checkbox"
        className="hidden"
        checked={isChecked}
        onChange={() => {}}
        disabled={disabled || loading}
        id={id}
        name={name}
      />
    </div>
  );
};

export default Checkbox;

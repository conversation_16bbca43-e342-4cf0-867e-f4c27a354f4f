import React, { useEffect, useState } from 'react';
import style from './scss/organisationUserList.module.scss';
import { useNavigate } from 'react-router-dom';

import { Image, Table, Card, Checkbox, Input, Popup, Dropdown, Modal, Form, Button } from 'semantic-ui-react';
import GlobalService from '../services/GlobalServices';
import { resturls } from '../utils/apiurls';
import { mediaBreakpoint } from '../global/MediaBreakPointes';
import {
  deleteIcon,
  downloadIcon,
  dropdownIcon,
  DropdownIcon,
  editIcon,
  lockIcon,
  mailIcon,
  orLineSvg,
  phoneIcon,
  superUserIcon,
  threeDotIcon,
} from '../global/Icons';
import { useFormik } from 'formik';
import * as Yup from 'yup';
import NavigationBar from './NavigationBar';
import Header from '../global/Header';
import infoIcon from '../../assets/Images/infoIcon.png';

const OrganisationUserList = () => {
  // const [ businessList, setBusinessList ] = useState();
  const [activeModal, setActiveModal] = useState();
  const [activePopup, setActivePopup] = useState();
  const [selctedInfo, setSelectedInfo] = useState();
  const [usersList, setUserList] = useState([]);
  const [selectUserID, setSelectedUserID] = useState();
  const [userActive, setUserActive] = useState();
  const [UserRoleCheck, setUserRoleCheck] = useState();
  const [initialValues, setInitialValues] = useState({
    server: '',
    email: '',
    password: '',
  });
  const navigate = useNavigate();

  const statusOptions = [
    { key: 'open', value: 'Open', text: 'Open', className: style.openStatus },
    { key: 'pending', value: 'Pending', text: 'Pending', className: style.pendingStatus },
    { key: 'closed', value: 'Closed', text: 'Closed', className: style.closedStatus },
    { key: 'verified', value: 'Verified', text: 'Verified', className: style.verifiedStatus },
    { key: 'deleted', value: 'Deleted', text: 'Deleted', className: style.deletedStatus },
  ];
  const roleOptions = [
    { key: 'Business user', value: 'Business user', text: 'Business user' },
    { key: 'Business super user', value: 'Business superuser', text: 'Business super user' },
    { key: 'Accountant', value: 'Accountant', text: 'Accountant' },
    { key: 'Manager', value: 'Manager', text: 'Manager' },
  ];

  const fetchOrgUsers = () => {
    GlobalService.generalSelect(
      (respdata) => {
        const { data } = respdata;
        const formattedUsers = data.map((user) => ({
          key: user.user_id,
          value: user.user_id,
          text: user.full_name?.length > 0 ? user.full_name : user?.email,
        }));
        setUserList(data);
      },
      `${resturls.obtainCategoryWiseUser}?user_type=business_users&user_type=business_superusers`,
      {},
      'GET'
    );
  };

  // useEffect
  useEffect(() => {
    fetchOrgUsers();
  }, []);

  console.log('user-list:- ', usersList);

  const handleUserDeactive = (id) => {
    console.log(id, 'Sdsdd');
    const payload = {
      is_active: false,
    };
    GlobalService.generalSelect(
      (respdata) => {
        console.log(respdata, 'deactivated_user');
        setUserList([]);
        fetchOrgUsers();
        setActiveModal(false);
      },
      `${resturls.users}${id}/`,
      payload,
      'PATCH'
    );
  };

  const handleUsereactive = (id) => {
    console.log(id, 'Sdsdd');
    const payload = {
      is_active: true,
    };
    GlobalService.generalSelect(
      (respdata) => {
        console.log(respdata, 'activated_user');
        setUserList([]);
        fetchOrgUsers();
        setActiveModal(false);
      },
      `${resturls.users}${id}/`,
      payload,
      'PATCH'
    );
  };

  const handleUSerRole = (id) => {
    console.log(id, '');
    const payload = {
      role: UserRoleCheck === 'Super User' ? 'buisness_user' : 'buisness_superuser',
    };
    GlobalService.generalSelect(
      (respdata) => {
        console.log(respdata, 'activated_user');
        setUserList([]);
        fetchOrgUsers();
        setActiveModal(false);
      },
      `${resturls.users}${id}/`,
      payload,
      'PATCH'
    );
  };

  const organisationList = usersList.map((user) => ({
    name: user.full_name,
    logo: null,
    userId: user.user_id,
    role: user.role === 'business_superuser' ? 'Super User' : 'User', // Assuming role is "Super User" for all users, you can change this logic
    status: user.is_active ? 'active' : 'inactive',
    desigination: user.designation || null,
  }));

  const formik = useFormik({
    initialValues: initialValues,
    // validationSchema,
    onSubmit: (values) => {
      GlobalService.generalSelect(
        (respdata) => {
          // navigate('/')
        },
        `${resturls.createBusiness}`,
        values,
        'POST'
      );
    },
  });

  const handleActiveModal = (key, info) => {
    if (key === 'edit') {
      setSelectedInfo(info);
      // setInitialValues(info)
    }
    setActiveModal(key);
    setActivePopup(false);
  };

  console.log('selected info:- ', userActive);
  const renderPopupContent = (info) => {
    const list = [
      { name: 'Reset password', icon: lockIcon(), key: 'reset' },
      {
        name: UserRoleCheck === 'Super User' ? 'Make Normal User' : 'Make Super User',
        icon: superUserIcon(),
        key: 'superuser',
      },
      {
        name: userActive === 'active' ? 'Deactivate User' : 'Activate User',
        icon: userActive === 'active' ? deleteIcon() : superUserIcon(),
        key: userActive === 'active' ? 'delete' : 'activate',
      },
      { name: 'Edit User Info', icon: editIcon(), key: 'edit' },
    ];
    return (
      <div className={style.popupList}>
        {list?.map((item) => {
          return (
            <div className={style.popupItem} onClickCapture={() => handleActiveModal(item?.key, info)}>
              {item?.icon} <p>{item?.name}</p>
            </div>
          );
        })}
      </div>
    );
  };

  const renderList = () => {
    return (
      <Table basic="very">
        <Table.Header>
          <Table.Row>
            <Table.HeaderCell>
              <div className="customCheckBox ">
                <Checkbox indeterminate className={`${style.checkbox}`} />
              </div>
            </Table.HeaderCell>
            <Table.HeaderCell className={style.subjectheaderRow}>User Name</Table.HeaderCell>
            <Table.HeaderCell>User ID</Table.HeaderCell>
            <Table.HeaderCell>Designation</Table.HeaderCell>
            <Table.HeaderCell>Role</Table.HeaderCell>
            <Table.HeaderCell></Table.HeaderCell>
          </Table.Row>
        </Table.Header>
        <Table.Body>
          {organisationList?.map((data) => (
            <Table.Row>
              <Table.Cell>
                <div className="customCheckBox">
                  <Checkbox className={`${style.checkbox}`} />
                </div>
              </Table.Cell>
              <Table.Cell className={style.userInfo}>
                <p className={style.userName}>{data?.name}</p>
                <span className={data?.status === 'active' ? style.activeStatus : style.inActive}>{data?.status}</span>
              </Table.Cell>
              <Table.Cell>{data?.userId}</Table.Cell>
              <Table.Cell>{data?.desigination}</Table.Cell>
              <Table.Cell>{data?.role}</Table.Cell>
              <Table.Cell>
                <div className={style.dotContainer}>
                  <Popup
                    content={renderPopupContent(data)}
                    on="click"
                    pinned
                    position="bottom right"
                    trigger={threeDotIcon()}
                    onOpen={() => {
                      setActivePopup(data?.userId);
                      setSelectedUserID(data?.userId);
                      setUserRoleCheck(data?.role);
                      setUserActive(data?.status);
                    }}
                    onClose={() => setActivePopup(false)}
                    className={style.popup}
                    open={activePopup === data?.userId}
                  />
                </div>
              </Table.Cell>
            </Table.Row>
          ))}
        </Table.Body>
      </Table>
    );
  };

  const renderButtons = (content, className) => {
    console.log(className, 'classname');

    return (
      <div className={style.buttonWrapper}>
        <Button type="button" className={style.cancel} onClick={() => setActiveModal(false)}>
          Cancel
        </Button>
        <Button type="submit" className={`${className} ${style.nextBtn}`}>
          {content || 'Save Details'}
        </Button>
      </div>
    );
  };

  //TODO: Add user modal-fix User Modal
  const addUserModal = () => (
    <div className={style.formContainer}>
      <Form.Field className={style.formField}>
        <label>User Name</label>
        <Form.Input
          id="user_name"
          name="userName"
          placeholder="Type here"
          onChange={formik.handleChange}
          onBlur={formik.handleBlur}
          value={formik.values.userName}
          error={
            formik.touched.userName && formik.errors.userName
              ? { content: formik.errors.userName, pointing: 'below' }
              : null
          }
        />
      </Form.Field>

      <Form.Field className={style.formField}>
        <label>Email</label>
        <Form.Input
          id="email"
          name="email"
          placeholder="Type here"
          onChange={formik.handleChange}
          onBlur={formik.handleBlur}
          value={formik.values.email}
          error={
            formik.touched.email && formik.errors.email ? { content: formik.errors.email, pointing: 'below' } : null
          }
        />
      </Form.Field>

      <Form.Field className={style.formField}>
        <label>Phone Number</label>
        <Form.Input
          id="phoneNumber"
          name="phoneNumber"
          placeholder="Type here"
          onChange={formik.handleChange}
          onBlur={formik.handleBlur}
          value={formik.values.phoneNumber}
          type="phoneNumber"
          error={
            formik.touched.phoneNumber && formik.errors.phoneNumber
              ? { content: formik.errors.phoneNumber, pointing: 'below' }
              : null
          }
        />
      </Form.Field>
      <Form.Field className={style.formField}>
        <label>Role</label>
        <Dropdown
          placeholder="Role"
          className={`customDropdown3 ${style.statusDropdown} ${style.roleDropdown}`}
          icon={<DropdownIcon />}
          options={roleOptions}
        />
      </Form.Field>
      <Form.Field className={style.formField}>
        <label>Designation</label>
        <Form.Input
          id="designation"
          name="designation"
          placeholder="Type here"
          onChange={formik.handleChange}
          onBlur={formik.handleBlur}
          value={formik.values.designation}
          type="designation"
          error={
            formik.touched.designation && formik.errors.designation
              ? { content: formik.errors.designation, pointing: 'below' }
              : null
          }
        />
      </Form.Field>
    </div>
  );

  console.log('isactive', userActive);
  const resetPasswordModal = () => (
    <div className={style.formContainer}>
      <Form.Field className={style.formField}>
        <label>New Password</label>
        <Form.Input
          id="password"
          name="password"
          placeholder="Type here"
          onChange={formik.handleChange}
          onBlur={formik.handleBlur}
          value={formik.values.password}
          error={
            formik.touched.password && formik.errors.password
              ? { content: formik.errors.password, pointing: 'below' }
              : null
          }
        />
      </Form.Field>
      <Form.Field className={style.formField}>
        <label>Confirm Password</label>
        <Form.Input
          id="confirmPassword"
          name="confirmPassword"
          placeholder="Type here"
          onChange={formik.handleChange}
          onBlur={formik.handleBlur}
          value={formik.values.confirmPassword}
          error={
            formik.touched.confirmPassword && formik.errors.confirmPassword
              ? { content: formik.errors.confirmPassword, pointing: 'below' }
              : null
          }
        />
      </Form.Field>
    </div>
  );

  const renderModalContent = () => {
    if (activeModal === 'add') {
      return (
        <div className={style.modalContent}>
          <h5>Add User</h5>
          <Form className={style.formWrapper} onSubmit={formik.handleSubmit}>
            {addUserModal()}
            {renderButtons()}
          </Form>
        </div>
      );
    }

    if (activeModal === 'edit') {
      return (
        <div className={style.modalContent}>
          <h5>Edit User</h5>
          <Form className={style.formWrapper} onSubmit={formik.handleSubmit}>
            {addUserModal()}
            {renderButtons()}
          </Form>
        </div>
      );
    }

    if (activeModal === 'reset') {
      return (
        <div className={`${style.autoHeight} ${style.modalContent}`}>
          <h5>Reset Password</h5>
          <div className={style.sendPasswordBtn}>
            <p>Send Password Reset Link</p>
          </div>
          <div className={style.orline}>{orLineSvg()}</div>
          <Form className={style.formWrapper} onSubmit={formik.handleSubmit}>
            {resetPasswordModal()}
            {renderButtons('Reset password')}
          </Form>
        </div>
      );
    }
    if (activeModal === 'activate') {
      return (
        <div className={`${style.autoHeight} ${style.modalContent}`}>
          <div className={style.imgWrapper}>
            <Image src={infoIcon} />
          </div>
          <div className={style.descContent}>
            <h5>Are you sure you want to Activate?</h5>
            <p>
              Activating this user will grant them access to the platform. You can deactivate their account at any time
              to revoke access.
            </p>
          </div>
          {/* {renderButtons('Activate', style.grteenBtn)} */}
          <div className={style.buttonWrapper}>
            <Button type="button" className={style.cancel} onClick={() => setActiveModal(false)}>
              Cancel
            </Button>
            <Button type="submit" className={style.grteenBtn} onClick={() => handleUsereactive(selectUserID)}>
              {' '}
              Activate{' '}
            </Button>
          </div>
        </div>
      );
    }

    if (activeModal === 'superuser') {
      return (
        <div className={`${style.autoHeight} ${style.modalContent}`}>
          <div className={style.imgWrapper}>
            <Image src={infoIcon} />
          </div>
          <div className={style.descContent}>
            <h5>
              Are you sure you want to Make this user to{' '}
              {UserRoleCheck === 'Super User' ? 'Make Normal User' : 'Make Super User'}?
            </h5>
            <p>
              Activating this user will grant them access to the platform. You can deactivate their account at any time
              to revoke access.
            </p>
          </div>
          {/* {renderButtons('Activate', style.grteenBtn)} */}
          <div className={style.buttonWrapper}>
            <Button type="button" className={style.cancel} onClick={() => setActiveModal(false)}>
              Cancel
            </Button>
            <Button type="submit" className={style.grteenBtn} onClick={() => handleUSerRole(selectUserID)}>
              {' '}
              Activate{' '}
            </Button>
          </div>
        </div>
      );
    }

    if (activeModal === 'delete') {
      return (
        <div className={`${style.autoHeight} ${style.modalContent}`}>
          <div className={style.imgWrapper}>
            <Image src={infoIcon} />
          </div>
          <div className={style.descContent}>
            <h5>Are you sure you want to deactivate</h5>
            <p>
              Deactivating this user will revoke their access to the platform. You can reactivate their account to
              restore access at any time.
            </p>
          </div>
          {/* {renderButtons('Deactivate', style.redBtn)}a */}
          <div className={style.buttonWrapper}>
            <Button type="button" className={style.cancel} onClick={() => setActiveModal(false)}>
              Cancel
            </Button>
            <Button type="submit" className={style.redBtn} onClick={() => handleUserDeactive(selectUserID)}>
              Deactivate{' '}
            </Button>
          </div>
        </div>
      );
    }
  };

  return (
    <div>
      <Header />
      <div className={style.bussinessListScreen}>
        <div className={style.navigationWrapper}>
          <NavigationBar disable />
        </div>
        <div className={style.rightContentWrapper}>
          <div className={style.headerPart}>
            <div>
              <h4>Organization Users</h4>
              <p className={style.desc}>Assign roles, update permissions, and manage organization users</p>
            </div>
            <div className={style.btnWrapper}>
              {/* <div className={style.downloadBtn}>{downloadIcon()} Download</div> */}
              {/* <div className={style.addBtn} onClick={() => setActiveModal('add')}>Add {dropdownIcon()}</div> */}
              <div className={style.addBtn} onClick={() => setActiveModal('add')}>
                Add{' '}
              </div>
            </div>
          </div>
          <div className={style.searchWrapper}>
            <Input className={style.searchInput} icon="search" placeholder="Search" iconPosition="left" />
            {/* <Dropdown placeholder='Role' className={`customDropdown3 ${style.statusDropdown}`} icon={<DropdownIcon />} options={roleOptions} />
                <Dropdown placeholder='Status' className={`customDropdown3 ${style.statusDropdown}`} icon={<DropdownIcon />} options={statusOptions} /> */}
          </div>
          <div className={style.tableWrapper}>{renderList()}</div>
        </div>
      </div>
      <Modal basic size="small" open={activeModal} onClose={() => setActiveModal(false)}>
        {renderModalContent()}
      </Modal>
    </div>
  );
};

export default OrganisationUserList;

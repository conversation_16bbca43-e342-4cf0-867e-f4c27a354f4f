@import '../../../../../assets/scss/main.scss';

.detailWrapper {
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}

.detailsContainer {
  padding: 1.5em;
  background-color: $white;
  box-shadow: 0 0 1px 2px $borderColor;
  margin: 2em;
  border-radius: 15px;
  width: 30%;
  min-height: 300px;
  position: relative;
  @include for_media(mobileScreen) {
    width: 100%;
    margin: 1em;
  }
}

.profileWrapper {
  padding: 1em;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.avatarProfile {
  border-radius: 100%;
  height: 5.5em !important;
  width: 5.5em !important;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: $white;
  cursor: pointer;
  span {
    background-color: $accentColor1;
    font-size: 1.2em;
    color: $primaryBgColor !important;
    font-weight: 900;
    border: 2px solid $accentBorder1;
    border-radius: 100%;
  }
}

.details {
  margin: 1.5em 0;
  .label {
    color: #535862;
    font-size: 1em;
  }
  p {
    margin: 0.5em 0;
  }
}

.resetBtn {
  width: 100%;
  display: flex;
  height: 2.5em;
  align-items: center;
  justify-content: center;
  gap: 1em;
  font-size: 1.4em;
  font-weight: 600;
  border-radius: 3rem;
  background-color: $accentBgColor1 !important;
  border: 1px solid $accentBorder1;
  color: $primaryColor !important;

  margin-bottom: 1em;
  cursor: pointer;
  svg {
    width: 20px;
    height: 20px;
  }

  &:hover {
    background-color: $accentHover1 !important;
  }
}

.logoutBtn {
  width: 100%;
  display: flex;
  height: 2.5em;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  font-size: 1.4em;
  border: 2px solid #d5d7da;
  border-radius: 35px;
  color: #b42318;
  font-weight: 600;
  cursor: pointer;

  svg {
    width: 20px;
    height: 20px;
  }

  &:hover {
    background-color: #fafafa;
  }
}

@import '../../../../assets/scss/main.scss';

:global(.ui.dimmer) {
  -webkit-backdrop-filter: blur(1px) !important;
  backdrop-filter: blur(1px) !important;
  background-color: rgba(0, 0, 0, 0.65) !important;
}

.extraction-form {
  background-color: $white;
  max-width: 1200px;
  margin: 0 auto;
  position: relative;

  .form-header {
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;
    align-items: center;
    gap: 1em;
    margin-bottom: 0.2em;
    padding: 0.5em 1em 0.7em 1em;
    position: sticky;
    top: 0;
    background-color: $white;
    z-index: 10;
    box-shadow: 0 2px 8px 0 rgba(0, 0, 0, 0.1);
  }

  .sections-container {
    padding: 0 1em;
  }

  .view-original-banner {
    position: sticky;
    top: 6em;
    left: 0;
    width: calc(100% - 2em);
    background-color: $white;
    color: $primaryColor;
    border: 1px solid $accentBorder1;
    padding: 0.5em 1em;
    margin: 0 1em;
    display: flex;
    align-items: center;
    justify-content: space-between;
    z-index: 10;
    border-radius: 8px;
    box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.1);
    font-weight: 600;
  }

  .form-title {
    font-weight: 600;
    font-size: 24px;
    color: #181d27;
    margin: 0;
  }

  .btn {
    display: flex;
    align-items: center;
    gap: 0.5em;
    padding: 0.5em 0.6em;
    border-radius: 2em;
    border: 1px solid $accentBorder1;
    background-color: $accentBgColor1;
    color: $primaryColor;
    font-size: 1em;
    transition: all 0.2s ease;
    white-space: nowrap;

    &:hover {
      background-color: $accentHover1;
    }

    &.active {
      background-color: rgba($accentColor1, 0.3);
    }

    svg {
      color: $primaryColor;
      width: 1.1em;
      height: 1.1em;
    }
  }

  .form-footer {
    display: flex;
    flex-direction: column;
    align-self: stretch;
    margin-top: 24px;
    padding: 0.7em 1em 0.5em 1em;
    background-color: $white;
    position: sticky;
    bottom: 0;
    z-index: 10;
    box-shadow: 0 -2px 8px 0 rgba(0, 0, 0, 0.1);
    .buttons-wrapper {
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      align-items: center;
      gap: 0.2em;
      width: 100%;

      .btn-draft {
        display: flex;
        justify-content: center;
        align-items: center;
        padding: 0.5em 1em;
        background-color: $accentBgColor1;
        border: 1px solid $accentBorder1;
        border-radius: 24px;
        cursor: pointer;

        span {
          font-size: 1.1em;
          line-height: 1.375em;
          color: $primaryColor;
          text-align: center;
        }

        &:hover {
          background-color: $accentHover1;
        }

        &:active {
          background-color: rgba($accentColor1, 0.3);
        }
      }

      .btn-submit {
        display: flex;
        justify-content: center;
        align-items: center;
        gap: 8px;
        padding: 0.5em 1em;
        background-color: $accentColor2;
        border: none;
        border-radius: 2em;
        cursor: pointer;

        svg {
          color: #ffffff;
        }

        span {
          font-family: 'DM Sans', sans-serif;
          font-weight: 600;
          font-size: 1.1em;
          line-height: 1.375em;
          color: #ffffff;
          text-align: center;
        }

        &:hover {
          background-color: $accentHover2;
        }

        &:active {
          background-color: #2d3666;
        }

        &:disabled {
          background-color: #d5d7da;
          cursor: not-allowed;

          svg,
          span {
            color: #535862;
          }
        }
      }
    }
  }
}

.form-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 0.7em;

  @media (max-width: 768px) {
    grid-template-columns: 1fr;
  }

  .only-1-column {
    grid-column: 1 / -1;
  }

  .only-2-column {
    grid-column: 1 / -1;
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: $field-gap;
  }

  .only-3-column {
    grid-column: 1 / -1;
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: $field-gap;
  }

  .only-4-column {
    grid-column: 1 / -1;
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: $field-gap;
    @media only screen and (max-width: 1164px) {
      grid-template-columns: repeat(2, 1fr);
    }
  }

  .only-5-column {
    grid-column: 1 / -1;
    display: grid;
    grid-template-columns: repeat(5, 1fr);
    gap: $field-gap;
  }

  .only-7-column {
    grid-column: 1 / -1;
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    gap: $field-gap;
    @media only screen and (max-width: 1378px) {
      grid-template-columns: repeat(4, 1fr);
    }
    @media only screen and (max-width: 1164px) {
      grid-template-columns: repeat(2, 1fr);
    }
  }
  .only-8-column {
    grid-column: 1 / -1;
    display: grid;
    grid-template-columns: repeat(8, 1fr);
    gap: $field-gap;
    @media only screen and (max-width: 1378px) {
      grid-template-columns: repeat(4, 1fr);
    }
    @media only screen and (max-width: 1164px) {
      grid-template-columns: repeat(2, 1fr);
    }
  }
}

.input-field {
  width: 100%;
  padding: 0.7em;
  border-radius: 12px;
  border: 1.5px solid #e9eaeb;
  background-color: $grayColor5 !important;
  font-size: 14px;
  font-family: $secondaryFont;
  outline: none;
  -moz-appearance: textfield;
  -webkit-appearance: textfield;
  appearance: textfield;
  flex: 1;
  line-height: 1.25;
  resize: none;
  &:read-only {
    background-color: #f8f9fa !important;
    border-color: #e0e2e7;
    cursor: not-allowed;
    box-shadow: inset 0 0 0 1px rgba(0, 0, 0, 0.05);
  }
}

.MuiInputBase-root.input-field {
  background-color: $grayColor5 !important;
  border-radius: 12px !important;
  font-size: 14px !important;
  font-family: 'DM Sans', sans-serif !important;
  line-height: 1.25 !important;
  padding: 0 !important;

  .MuiOutlinedInput-notchedOutline {
    border: 1.5px solid #e9eaeb !important;
    border-radius: 12px !important;
  }

  .MuiSelect-select {
    padding-top: 0.5em !important;
    padding-bottom: 0.5em !important;
  }

  &.Mui-focused .MuiOutlinedInput-notchedOutline {
    border-color: $accentColor1 !important;
    box-shadow: none !important;
  }
}

.MuiSelect-nativeInput {
  opacity: 0 !important;
}

//add some padding for input-field when there is alert like error/warning/exact match found.
#extraction-field-alert:has(#extraction-alert) .input-field {
  padding-right: 1.9em !important;
}

//If #errorIcon exists, border is red
#extraction-field-alert:has(#errorIcon) .input-field {
  border-color: #f04438 !important;
}

//If #warningIcon exists, border is yellow
#extraction-field-alert:has(#warningIcon) .input-field {
  border-color: #f79009 !important;
}

input:disabled,
textarea:disabled,
button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.dropdown-field.disabled {
  opacity: 0.4;
  cursor: not-allowed;
}

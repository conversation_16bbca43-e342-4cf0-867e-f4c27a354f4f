@import '../../../../../../assets//scss/main.scss';

.statsContainer {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  gap: 1em;
  width: 100%;
  height: 100%;
}

.progressBar {
  display: flex;
  height: 1.3em;
  width: 100%;
  border-radius: 5px;
  gap: 0 !important;
  overflow: hidden;
  background-color: #eeeeee; /* Background color for empty sections */
  border-radius: 1em;

  .progressSegment {
    height: 100%;

    &.open {
      background-color: #2186d0;
    }
    &.pending {
      background-color: #4ac2ff;
    }
    &.verified {
      background-color: #eaecf5;
    }
    &.closed {
      background-color: #293056;
    }
    &.deleted {
      background-color: #9ca3af;
    }
  }
}

.statusList {
  display: flex;
  flex-wrap: wrap;
  gap: 1em;
  font-size: 1.1em;

  .statusItem {
    display: flex;
    align-items: center;
    gap: 0.5em;

    .dot {
      width: 0.9em;
      height: 0.9em;
      border-radius: 50%;

      &.open {
        background-color: #2186d0;
      }
      &.pending {
        background-color: #4ac2ff;
      }
      &.verified {
        background-color: #eaecf5;
      }
      &.closed {
        background-color: #293056;
      }
      &.deleted {
        background-color: #9ca3af;
      }
    }

    .count {
      color: #6b7280;
    }
  }
}

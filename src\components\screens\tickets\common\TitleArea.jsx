import React from 'react';
import style from './scss/titleArea.module.scss';
import { PlusIcon, SearchIcon } from '../../../../assets/svgs';
import { useNavigate, useParams } from 'react-router-dom';
import FiltersAccountant from '../accountant/FiltersAccountant';
import { useAuth } from '../../../../contexts/AuthContext';
import FiltersUser from '../user/FiltersUser';
import InputSearch from '../../../ui-components/InputSearch';

function TitleArea({ query, setQuery, setExtraParams }) {
  const navigate = useNavigate();
  const { id } = useParams();
  const { role } = useAuth();

  return (
    <div className={style.titlePart}>
      <div className={style.filterContainer}>
        {['business_user', 'business_superuser'].includes(role) && <FiltersUser setExtraParams={setExtraParams} />}

        {['manager', 'accountant'].includes(role) && (
          <FiltersAccountant setQuery={setQuery} setExtraParams={setExtraParams} />
        )}
      </div>

      <div className={style.actionWrapper}>
        <div className={style.logSeachWrapper}>
          <InputSearch
            inputClasses="min-h-[3em]"
            placeholder="Search by ticket ID or subject"
            value={query}
            onChange={(e) => setQuery(e.target.value)}
          />
        </div>
        <p onClickCapture={() => navigate(`/createTicket/${id || ''}`)} className={style.plusIcon}>
          <PlusIcon />
        </p>
      </div>
    </div>
  );
}

export default React.memo(TitleArea);

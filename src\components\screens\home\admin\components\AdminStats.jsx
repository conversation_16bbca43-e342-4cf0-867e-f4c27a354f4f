import React from 'react';
import style from './scss/adminStats.module.scss';
import { BuildingIcon, ListDetailsIcon, UsersIcon } from '../../../../../assets/svgs';
import adminServices from '../../../../services/adminServices';
import useServiceFetch from '../../../../global/hooks/useServiceFetch';
import { Loader } from 'semantic-ui-react';
import { jsonKeyFormatLabel } from '../../../../utils';
import { LoadingWrapper } from '../../../../global/components';
import Card from '../../../../global/components/Card';

const statusList = ['Open', 'Pending', 'Verified', 'Closed', 'Deleted'];

const statsList = [
  { key: 'total_businesses', logo: <BuildingIcon /> },
  { key: 'arthtattva_users', logo: <UsersIcon /> },
  { key: 'client_users', logo: <UsersIcon /> },
  { key: 'total_tickets', logo: <ListDetailsIcon /> },
];

function AdminStats() {
  const { data: AdminStats, loading } = useServiceFetch(adminServices.getAdminStats);

  return (
    <div className={style.statsContainer}>
      <LoadingWrapper loading={loading}>
        {AdminStats &&
          statsList.map((stats) => (
            <Card logo={stats.logo} title={jsonKeyFormatLabel(stats.key)} count={AdminStats[stats.key]}>
              {stats.key === 'total_tickets' && (
                <>
                  <div className={style.progressBar}>
                    {AdminStats?.status_ratios &&
                      Object.entries(AdminStats?.status_ratios).map(([status, { ratio }]) => (
                        <div
                          key={status}
                          className={`${style.progressSegment} ${style[status.toLowerCase()]}`}
                          style={{ width: `${ratio}%` }}
                          title={`${status}: ${ratio}%`}
                        ></div>
                      ))}
                  </div>

                  <div className={style.statusList}>
                    {statusList?.map((status) => (
                      <div key={status} className={style.statusItem}>
                        <div className={`${style.dot} ${style[status.toLowerCase()]}`} />
                        <span>{status}</span>
                        <span className={style.count}>: {AdminStats?.status_ratios[status]?.count || '-'}</span>
                      </div>
                    ))}
                  </div>
                </>
              )}
            </Card>
          ))}
      </LoadingWrapper>
    </div>
  );
}

export default React.memo(AdminStats);

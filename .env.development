# Base CDN Domain (please replace this with you new url)
BCDN_DOMAIN=beta.omnisageai.com

# Application Domain
REACT_APP_DOMAIN=beta.omnisageai.com

# Protocol used (https for secure connections)
REACT_APP_PROTOCOL=https

# Ports (if no specific ports are needed, keep these blank)
REACT_APP_PORT=
REACT_APP_REACTPORT=443

# API Context (leave blank if not applicable)
REACT_APP_APICONTEXT=

# Base REST API URL(please replace this with you new url)
REACT_APP_RESTBASEURL=https://api-test.omnisageai.com

# WebSocket URL (please replace this with you new url)
REACT_APP_WS_URL=api-test.omnisageai.com

# CDN URL for accessing resources(please replace this with you new url)
REACT_APP_CDNURL=https://api-test.omnisageai.com

# Domain for cookies(please replace this with you new url)
REACT_APP_COOKIEDOMAIN=beta.omnisageai.com

# Application Secret Key (ensure this is securely managed)
REACT_APP_SECRET_KEY=G7eFz9b1WjP8TmQs3kX2Cvz5R!yB6oDp

REACT_APP_CREATION_BUSINESS_ID=8a2cbff6-2e58-45b8-a28d-8eb195d061a2
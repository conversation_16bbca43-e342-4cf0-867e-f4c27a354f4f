/**
 * function for debugging logs
 * @param {*} message
 * @param {*} data
 */

/**
 * Logs debugging information to the console and stores recent logs in localStorage.
 *
 * @param {string} message - The debug message to be logged.
 * @param {*} data - Optional data to be logged along with the message.
 * @param {string} ReportPage - The name of the report page where the log originates.
 */
export const debugLog = (message, data, ReportPage) => {
  console.group(`${ReportPage} Debug: ${message}`);
  if (data) console.log('Data:', data);
  console.groupEnd();

  console.log(`[${ReportPage}] ${message}`, data || '');

  // Store log in localStorage for debugging
  const timestamp = new Date().toISOString();
  const logEntry = {
    timestamp,
    ReportPage,
    message,
    data: data || {},
  };
  const logs = JSON.parse(localStorage.getItem('debug_logs') || '[]');
  logs.push(logEntry);
  localStorage.setItem(`${ReportPage}_logs`, JSON.stringify(logs.slice(-100))); // Keep only the latest 100 logs
};

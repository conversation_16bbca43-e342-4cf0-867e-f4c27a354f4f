import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import InvoicePreview from './components/InvoicePreview';
import AiForm from './components/AiForm';
import styles from './AiExtractionScreen.module.scss';
import { Link, useNavigate, useParams } from 'react-router-dom';
import { getExtractedInvoice } from '../../services/aiServices';
import useServiceFetch from '../../global/hooks/useServiceFetch';
import LoadingWrapper from '../../global/components/LoadingWrapper';
import { ArrowLeft } from 'lucide-react';
import ticketServices from '../../services/ticketServices';
import useAiExtractionNavigation from './useAiExtractionNavigation';
import useUpdateEffect from '../../global/hooks/useUpdateEffect';

function AiExtractionScreen() {
  const navigate = useNavigate();
  const { fileId, businessId } = useParams();
  const isFirstRender = useRef(true);

  const {
    handlePrevious,
    handleNext,
    reachedStatus,
    isFetching: fetchingInvoices,
    didNotFound,
  } = useAiExtractionNavigation();

  const { data, loading: categoryLoading } = useServiceFetch(ticketServices.getCategories);
  const { categoryOptions, subCategoryOptions } = useMemo(() => {
    return (
      data?.results?.reduce(
        (acc, item) => {
          if (item?.name?.toLowerCase() === 'purchase' || item?.name?.toLowerCase() === 'expense') {
            acc.categoryOptions.push({
              key: item?.id,
              value: item?.id,
              label: item?.name,
            });

            const invoiceSubCat = item?.subcaregories?.filter((item) => item?.name?.toLowerCase() === 'invoice');
            acc.subCategoryOptions.push({
              key: invoiceSubCat?.id,
              value: invoiceSubCat?.id,
              label: invoiceSubCat?.name,
            });
          }
          return acc;
        },
        {
          categoryOptions: [],
          subCategoryOptions: [],
        }
      ) || { categoryOptions: [], subCategoryOptions: [] }
    );
  }, [data]);
  const {
    data: extractedData,
    loading,
    error,
    refetch,
  } = useServiceFetch(() => getExtractedInvoice(fileId, businessId));

  // const [extractedData, setExtractedData] = useState(null);
  // const [loading, setLoading] = useState(true);
  // const [error, setError] = useState(null);
  // useEffect(() => {
  //   const staticJson = "/extracted-data.json";
  //   fetch(staticJson)
  //     .then((response) => response.json())
  //     .then((res) => {
  //       setExtractedData(res);
  //     })
  //     .catch((err) => {
  //       setError(err);
  //     })
  //     .finally(() => {
  //       setLoading(false);
  //     });
  // }, []);

  useEffect(() => {
    if (isFirstRender.current && extractedData?.ticket_id) {
      isFirstRender.current = false;
    }
  }, [extractedData?.ticket_id]);

  useUpdateEffect(() => {
    refetch(false);
  }, [fileId, businessId]);

  if (window.innerWidth < 1024) {
    return (
      <div className="flex flex-col text-center items-center justify-center h-screen">
        <p>For an optimal comparison experience, please use a large-screen device such as a laptop.</p>
        <p
          className="group flex items-center hover:text-blue-500 hover:underline cursor-pointer select-none"
          onClick={() => navigate(-1)}
        >
          <ArrowLeft className="group-hover:fill-blue-500 cursor-pointer" /> Go back{' '}
        </p>
      </div>
    );
  }
  return (
    <div className={styles.aiExtractionContainer}>
      <div className={styles.contentWrapper}>
        <div className={styles.leftContainer}>
          <div className="flex items-center gap-4 min-h-12">
            <Link
              to="/invoices"
              className="flex items-center gap-2 text-nowrap px-2 py-1.5 text-primary-color border border-accent1-border bg-accent1-bg no-underline font-semibold text-sm rounded-xl transition-all duration-200 hover:bg-accent1-bg-hover"
              aria-label="Go back to invoices list"
            >
              <ArrowLeft className="w-4 h-4" /> Go to invoices
            </Link>
            <div className="h-7 border-l border-gray-300 mx-1" />
            <LoadingWrapper loading={(loading || fetchingInvoices) && isFirstRender.current} useBlankSkeleton={true}>
              <div className="flex flex-col justify-center min-w-[180px]">
                <span className="font-semibold text-lg text-[#181d27] leading-tight">
                  {extractedData?.bill_to_details?.invoice_no}
                </span>
                <span className="text-sm text-gray-700 mt-0.5">{extractedData?.document_name}</span>
              </div>
            </LoadingWrapper>
          </div>
          <div className="bg-white rounded-lg p-4 shadow-lg max-h-inherit">
            <InvoicePreview
              loading={(loading || categoryLoading || fetchingInvoices) && isFirstRender.current}
              fileUrl={extractedData?.file_url}
            />
          </div>
        </div>
        <div id="ai-extraction-form-container" className={styles.rightContainer}>
          <LoadingWrapper
            useBlankSkeleton={true}
            loading={loading || categoryLoading || fetchingInvoices}
            error={error}
          >
            <AiForm
              extractedData={extractedData}
              businessId={businessId}
              refetch={refetch}
              categoryOptions={categoryOptions}
              subCategoryOptions={subCategoryOptions}
              handlePrevious={handlePrevious}
              handleNext={handleNext}
              reachedStatus={reachedStatus}
              didNotFound={didNotFound}
            />
          </LoadingWrapper>
        </div>
      </div>
    </div>
  );
}

export default AiExtractionScreen;

import React, { useState, useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import style from './scss/footer.module.scss';
import ls from 'local-storage';
import GlobalService from '../services/GlobalServices';
import { resturls } from '../utils/apiurls';
import { useAuth } from '../../contexts/AuthContext';
import { decryptData } from '../utils/cryptoUtils';

const Footer = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const [activeMenu, setActiveMenu] = useState('home');
  const [open, setOpen] = useState(false);
  const { headerLogo, userInfo, setBusiness } = useAuth();
  const [selectedBusiness, setSelectedBusiness] = useState();
  const [isLoading, setIsloading] = useState(false);

  const currentPathSegments = location.pathname.split('/');
  const isBussinessId = currentPathSegments[currentPathSegments.length - 2] === 'ticketList';
  const lastPathSegment = isBussinessId && currentPathSegments[currentPathSegments.length - 1];
  // const [selectedBusiness, setSelectedBusiness] = useState(() => {
  //   const stored = ls.get("selectedBusiness");
  //   console.log("Initial selectedBusiness from localStorage:", stored);
  //   return stored;
  // });

  const roleEncripted = ls.get('access_token')?.role;
  const role = roleEncripted && decryptData(roleEncripted);
  const isManager = ['superuser', 'accountant', 'manager']?.includes(role);
  const BusinessId = isManager ? '' : selectedBusiness?.business_id;
  const features = selectedBusiness?.features || [];
  const isReportsEnabled = features.some((feature) => feature.name === 'Reports' && feature.is_active);

  useEffect(() => {
    const checkLocalStorage = () => {
      const updatedBusiness = ls.get('selectedBusiness');
      if (JSON.stringify(updatedBusiness) !== JSON.stringify(selectedBusiness)) {
        setSelectedBusiness(updatedBusiness);
      }
    };

    const interval = setInterval(checkLocalStorage, 1000);

    return () => clearInterval(interval);
  }, [selectedBusiness]);

  // useEffect(() => {
  //   const handleStorageChange = () => {
  //     const updated = ls.get("selectedBusiness");
  //     console.log("Storage changed - selectedBusiness:", updated);
  //     setSelectedBusiness(updated);
  //   };

  //   window.addEventListener('storage', handleStorageChange);

  //   if (!selectedBusiness) {
  //     const stored = ls.get("selectedBusiness");
  //     console.log("Reloading selectedBusiness:", stored);
  //     setSelectedBusiness(stored);
  //   }

  //   return () => {
  //     window.removeEventListener('storage', handleStorageChange);
  //   };
  // }, []);

  const userId = userInfo?.user_id || userInfo?.userId;
  useEffect(() => {
    const admin = ['superuser', 'accountant', 'manager']?.includes(role);
    if (userId && !admin) {
      setIsloading(true);
      // const delayCall = setTimeout(() => {
      GlobalService.generalSelect(
        (respdata) => {
          const { results } = respdata;
          setBusiness(results);
          const selectedInLs = ls.get('selectedBusiness');
          if (results?.length > 0) {
            const matchingBusiness = results.find((business) => business?.business_image === headerLogo);

            if (selectedInLs) {
              setSelectedBusiness(selectedInLs);
            } else {
              if (matchingBusiness) {
                setSelectedBusiness(matchingBusiness);
              } else {
                setSelectedBusiness(results[0]);
              }
            }

            setIsloading(false);
          }
        },
        `${resturls.getBusinesses}`,
        {},
        'GET'
      );
      // }, 100);
      // return () => clearTimeout(delayCall);
    }
  }, [userId]);

  const plusIcon = () => (
    <svg width="14" height="14" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M14 7.99805H8V13.998H6V7.99805H0V5.99805H6V-0.00195312H8V5.99805H14V7.99805Z" fill="white" />
    </svg>
  );

  const homeIcon = () => (
    <svg width="13" height="14" viewBox="0 0 13 14" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        fill-rule="evenodd"
        clip-rule="evenodd"
        d="M6.14646 0.646447C6.34172 0.451184 6.65831 0.451184 6.85357 0.646447L12.8536 6.64645C12.9966 6.78945 13.0393 7.0045 12.962 7.19134C12.8846 7.37818 12.7022 7.5 12.5 7.5H11.6667V11.6667C11.6667 12.1529 11.4735 12.6192 11.1297 12.963C10.7859 13.3068 10.3196 13.5 9.83335 13.5H3.16668C2.68045 13.5 2.21414 13.3068 1.87032 12.963C1.5265 12.6192 1.33335 12.1529 1.33335 11.6667V7.5H0.500015C0.297783 7.5 0.115465 7.37818 0.0380748 7.19134C-0.0393157 7.0045 0.00346223 6.78945 0.146461 6.64645L6.14646 0.646447ZM5.00001 12.5H8.00001V9C8.00001 8.77899 7.91222 8.56703 7.75594 8.41074C7.59966 8.25446 7.38769 8.16667 7.16668 8.16667H5.83335C5.61233 8.16667 5.40037 8.25446 5.24409 8.41074C5.08781 8.56703 5.00001 8.77899 5.00001 9V12.5ZM9.00001 12.5V9C9.00001 8.51377 8.80686 8.04745 8.46304 7.70364C8.11923 7.35982 7.65291 7.16667 7.16668 7.16667H5.83335C5.34712 7.16667 4.8808 7.35982 4.53699 7.70364C4.19317 8.04745 4.00001 8.51377 4.00001 9V12.5H3.16668C2.94567 12.5 2.73371 12.4122 2.57743 12.2559C2.42115 12.0996 2.33335 11.8877 2.33335 11.6667V7C2.33335 6.72386 2.10949 6.5 1.83335 6.5H1.70712L6.50001 1.70711L11.2929 6.5H11.1667C10.8905 6.5 10.6667 6.72386 10.6667 7V11.6667C10.6667 11.8877 10.5789 12.0996 10.4226 12.2559C10.2663 12.4122 10.0544 12.5 9.83335 12.5H9.00001Z"
        fill="#0A0D12"
      />
    </svg>
  );
  const logIcon = () => (
    <svg width="11" height="14" viewBox="0 0 11 14" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        fill-rule="evenodd"
        clip-rule="evenodd"
        d="M4.83594 1.5C4.61492 1.5 4.40296 1.5878 4.24668 1.74408C4.0904 1.90036 4.0026 2.11232 4.0026 2.33333C4.0026 2.55435 4.0904 2.76631 4.24668 2.92259C4.40296 3.07887 4.61492 3.16667 4.83594 3.16667H6.16927C6.39029 3.16667 6.60225 3.07887 6.75853 2.92259C6.91481 2.76631 7.0026 2.55435 7.0026 2.33333C7.0026 2.11232 6.91481 1.90036 6.75853 1.74408C6.60225 1.5878 6.39029 1.5 6.16927 1.5H4.83594ZM3.53958 1.03697C3.88339 0.693154 4.34971 0.5 4.83594 0.5H6.16927C6.6555 0.5 7.12182 0.693154 7.46563 1.03697C7.68882 1.26016 7.84852 1.53497 7.93311 1.83333H8.83594C9.32217 1.83333 9.78848 2.02649 10.1323 2.3703C10.4761 2.71412 10.6693 3.18044 10.6693 3.66667V11.6667C10.6693 12.1529 10.4761 12.6192 10.1323 12.963C9.78848 13.3068 9.32217 13.5 8.83594 13.5H2.16927C1.68304 13.5 1.21672 13.3068 0.872908 12.963C0.529092 12.6192 0.335938 12.1529 0.335938 11.6667V3.66667C0.335938 3.18044 0.529092 2.71412 0.872908 2.3703C1.21673 2.02649 1.68304 1.83333 2.16927 1.83333H3.0721C3.15669 1.53497 3.31639 1.26016 3.53958 1.03697ZM3.0721 2.83333H2.16927C1.94826 2.83333 1.7363 2.92113 1.58002 3.07741C1.42373 3.23369 1.33594 3.44565 1.33594 3.66667V11.6667C1.33594 11.8877 1.42373 12.0996 1.58002 12.2559C1.7363 12.4122 1.94826 12.5 2.16927 12.5H8.83594C9.05695 12.5 9.26891 12.4122 9.42519 12.2559C9.58147 12.0996 9.66927 11.8877 9.66927 11.6667V3.66667C9.66927 3.44565 9.58147 3.23369 9.42519 3.07741C9.26891 2.92113 9.05695 2.83333 8.83594 2.83333H7.93311C7.84852 3.1317 7.68882 3.40651 7.46563 3.6297C7.12182 3.97351 6.6555 4.16667 6.16927 4.16667H4.83594C4.34971 4.16667 3.88339 3.97351 3.53958 3.6297C3.31639 3.40651 3.15669 3.1317 3.0721 2.83333ZM3.0026 7C3.0026 6.72386 3.22646 6.5 3.5026 6.5H3.50927C3.78541 6.5 4.00927 6.72386 4.00927 7C4.00927 7.27614 3.78541 7.5 3.50927 7.5H3.5026C3.22646 7.5 3.0026 7.27614 3.0026 7ZM5.66927 7C5.66927 6.72386 5.89313 6.5 6.16927 6.5H7.5026C7.77875 6.5 8.0026 6.72386 8.0026 7C8.0026 7.27614 7.77875 7.5 7.5026 7.5H6.16927C5.89313 7.5 5.66927 7.27614 5.66927 7ZM3.0026 9.66667C3.0026 9.39052 3.22646 9.16667 3.5026 9.16667H3.50927C3.78541 9.16667 4.00927 9.39052 4.00927 9.66667C4.00927 9.94281 3.78541 10.1667 3.50927 10.1667H3.5026C3.22646 10.1667 3.0026 9.94281 3.0026 9.66667ZM5.66927 9.66667C5.66927 9.39052 5.89313 9.16667 6.16927 9.16667H7.5026C7.77875 9.16667 8.0026 9.39052 8.0026 9.66667C8.0026 9.94281 7.77875 10.1667 7.5026 10.1667H6.16927C5.89313 10.1667 5.66927 9.94281 5.66927 9.66667Z"
        fill="#535862"
      />
    </svg>
  );
  const reportIcon = () => (
    <svg width="13" height="12" viewBox="0 0 13 12" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        fill-rule="evenodd"
        clip-rule="evenodd"
        d="M2.4974 1.16602C2.27638 1.16602 2.06442 1.25381 1.90814 1.41009C1.75186 1.56637 1.66406 1.77833 1.66406 1.99935V9.99935C1.66406 10.2204 1.75186 10.4323 1.90814 10.5886C2.06442 10.7449 2.27638 10.8327 2.4974 10.8327H10.4974C10.7184 10.8327 10.9304 10.7449 11.0867 10.5886C11.2429 10.4323 11.3307 10.2204 11.3307 9.99935V1.99935C11.3307 1.77834 11.2429 1.56637 11.0867 1.41009C10.9304 1.25381 10.7184 1.16602 10.4974 1.16602H2.4974ZM1.20103 0.702986C1.54485 0.35917 2.01117 0.166016 2.4974 0.166016H10.4974C10.9836 0.166016 11.4499 0.35917 11.7938 0.702986C12.1376 1.0468 12.3307 1.51312 12.3307 1.99935V9.99935C12.3307 10.4856 12.1376 10.9519 11.7938 11.2957C11.4499 11.6395 10.9836 11.8327 10.4974 11.8327H2.4974C2.01117 11.8327 1.54485 11.6395 1.20103 11.2957C0.857217 10.9519 0.664062 10.4856 0.664062 9.99935V1.99935C0.664062 1.51312 0.857217 1.0468 1.20103 0.702986ZM8.14384 4.31246C8.3391 4.1172 8.65569 4.1172 8.85095 4.31246L10.1843 5.64579C10.3795 5.84106 10.3795 6.15764 10.1843 6.3529C9.98902 6.54816 9.67244 6.54816 9.47718 6.3529L8.4974 5.37312L6.85095 7.01957C6.65569 7.21483 6.3391 7.21483 6.14384 7.01957L5.16406 6.03979L3.51762 7.68623C3.32235 7.8815 3.00577 7.8815 2.81051 7.68623C2.61525 7.49097 2.61525 7.17439 2.81051 6.97913L4.81051 4.97913C5.00577 4.78387 5.32235 4.78387 5.51762 4.97913L6.4974 5.95891L8.14384 4.31246Z"
        fill="#535862"
      />
    </svg>
  );
  const menuIcon = () => (
    <svg width="13" height="10" viewBox="0 0 13 10" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        fill-rule="evenodd"
        clip-rule="evenodd"
        d="M0.664062 1C0.664062 0.723858 0.88792 0.5 1.16406 0.5H11.8307C12.1069 0.5 12.3307 0.723858 12.3307 1C12.3307 1.27614 12.1069 1.5 11.8307 1.5H1.16406C0.88792 1.5 0.664062 1.27614 0.664062 1ZM0.664062 5C0.664062 4.72386 0.88792 4.5 1.16406 4.5H11.8307C12.1069 4.5 12.3307 4.72386 12.3307 5C12.3307 5.27614 12.1069 5.5 11.8307 5.5H1.16406C0.88792 5.5 0.664062 5.27614 0.664062 5ZM0.664062 9C0.664062 8.72386 0.88792 8.5 1.16406 8.5H11.8307C12.1069 8.5 12.3307 8.72386 12.3307 9C12.3307 9.27614 12.1069 9.5 11.8307 9.5H1.16406C0.88792 9.5 0.664062 9.27614 0.664062 9Z"
        fill="#535862"
      />
    </svg>
  );

  const list = [
    {
      name: 'Home',
      url: '/',
      key: 'home',
      icon: homeIcon(),
    },
    {
      name: 'Tickets',
      key: 'ticketList',
      icon: logIcon(),
      url: `/ticketList/${selectedBusiness?.business_id || ''}`,
    },
    {
      icon: plusIcon(),
      className: style.middleBtn,
      key: 'plus',
      url: `/createTicket/${lastPathSegment ? lastPathSegment : BusinessId}`,
    },
    {
      name: 'Reports',
      key: 'reports',
      icon: reportIcon(),
      url: isReportsEnabled ? '/reportsMenu' : '',
    },
    {
      name: 'Menu',
      key: 'menu',
      icon: menuIcon(),
    },
  ];

  useEffect(() => {
    const currentPath = location.pathname;
    if (currentPath === '/') {
      setActiveMenu('home');
    } else if (currentPath.includes('/createTicket')) {
      setActiveMenu(null);
    } else {
      const activeItem = list.find((item) => item.url && currentPath.startsWith(item.url) && item.url !== '/');
      if (activeItem) {
        setActiveMenu(activeItem.key);
      } else {
        setActiveMenu(null);
      }
    }
  }, [location.pathname, list]);

  const handleActiveMenu = (item) => {
    if (item?.url && !isLoading) {
      setActiveMenu(item.key);
      navigate(item.url);
      setOpen(false);
    } else if (item?.name === 'Menu') {
      setOpen(!open);
    }
  };

  const isAdmin = role === 'superuser';
  // const isManager = ["superuser", "accountant", "manager"]?.includes(role);

  const subMenu = [
    {
      name: 'Business List',
      url: '/businessList',
      isEnable: isManager,
    },
    {
      name: 'Users List',
      url: '/usersList',
      isEnable: isManager,
    },
    {
      name: 'Create Business',
      url: '/businessCreation',
      isEnable: isAdmin,
    },
    {
      name: 'Create User',
      url: '/createUser',
      isEnable: isAdmin,
    },
  ];

  const handleSubMenu = (data) => {
    navigate(data?.url);
    setOpen(false);
  };

  return (
    <div className={style.footerContainer}>
      {open && isAdmin && (
        <div className={style.popupContainer}>
          <ol>
            {subMenu?.map((data) =>
              data?.isEnable ? (
                <li key={data?.id} onClickCapture={() => handleSubMenu(data)}>
                  {data?.name}
                </li>
              ) : null
            )}
          </ol>
        </div>
      )}
      <ol>
        {list?.map((item) => (
          <li
            key={item.key}
            className={`${item?.className || ''} ${activeMenu === item.key ? style.activeMenu : ''} `}
            onClick={() => handleActiveMenu(item)}
          >
            {item?.name && item?.icon}
            {item?.name || item.icon}
          </li>
        ))}
      </ol>
    </div>
  );
};

export default Footer;

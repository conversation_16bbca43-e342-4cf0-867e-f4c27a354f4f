import { useState, useEffect, useCallback, useRef, useMemo } from 'react';
import debounce from 'lodash/debounce';
import apiClient from '../../services/apiClient';
import LoadMoreButton from '../../ui-components/LoadMoreButton';

/**
 * A custom hook for handling search functionality with load more capability
 * @param {string|null} fetchUrl - The API URL to fetch results from
 * @param {string} queryParam - The query parameter name to use for API requests
 * @param {number} debounceDelay - Delay in ms for debouncing search requests
 * @param {boolean} initialFetch - Whether to perform an initial fetch without query parameters (optional)
 * @returns {Object} Search state, functions, and LoadMoreButton component
 */
const useLoadMoreWithSearch = (fetchUrl, queryParam = 'search', debounceDelay = 600, initialFetch = false) => {
  const [query, setQuery] = useState('');
  const [data, setData] = useState([]);
  const [nextPageUrl, setNextPageUrl] = useState(null);
  const [error, setError] = useState(null);
  const [isLoading, setIsLoading] = useState(!!fetchUrl && initialFetch);
  const [isLoadMore, setIsLoadMore] = useState(false);
  const abortControllerRef = useRef(null);
  const [totalCount, setTotalCount] = useState(0);

  useEffect(() => {
    if (fetchUrl && initialFetch) {
      setIsLoading(true);
    }
  }, [fetchUrl, initialFetch]);

  const fetchData = useCallback(
    async (url, searchTerm, signal, isLoadMoreBtnClicked = false, skipQueryParam = false) => {
      if (!url) return;
      const loadingState = isLoadMoreBtnClicked ? setIsLoadMore : setIsLoading;
      loadingState(true);
      setError(null);
      try {
        const requestParams = skipQueryParam ? {} : { [queryParam]: searchTerm };
        const response = await apiClient.get(url, {
          params: requestParams,
          signal,
        });

        const { next, results, count } = response;
        count && totalCount !== count && setTotalCount(count);
        setData((prev) => {
          let safeResult = results || response;
          if (isLoadMoreBtnClicked) {
            return [...prev, ...safeResult];
          }
          return safeResult;
        });
        setNextPageUrl(next ?? null);
      } catch (err) {
        if (!signal.aborted) {
          setError(err.message || 'Failed to load data');
        }
      } finally {
        loadingState(false);
      }
    },
    []
  );

  const debouncedFetchData = useMemo(
    () =>
      debounce((url, searchTerm, signal, isLoadMoreBtnClicked = false, skipQueryParam = false) => {
        fetchData(url, searchTerm, signal, isLoadMoreBtnClicked, skipQueryParam);
      }, debounceDelay),
    [fetchData, debounceDelay]
  );

  useEffect(() => {
    if (!fetchUrl) return;
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }
    const controller = new AbortController();
    abortControllerRef.current = controller;
    if (initialFetch && !query) {
      debouncedFetchData(fetchUrl, '', controller.signal, false, true);
    } else {
      debouncedFetchData(fetchUrl, query, controller.signal);
    }

    return () => controller.abort();
  }, [query, initialFetch, fetchUrl, debouncedFetchData]);

  const fetchMore = useCallback(() => {
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }
    const controller = new AbortController();
    abortControllerRef.current = controller;
    fetchData(nextPageUrl, query, controller.signal, true);
  }, [fetchData, nextPageUrl, query]);

  const refetch = useCallback(() => {
    setQuery('');
    debouncedFetchData(fetchUrl, '', abortControllerRef.current.signal, false, true);
  }, [debouncedFetchData, fetchUrl]);

  return {
    data,
    nextPageUrl,
    isLoading,
    isLoadMore,
    error,
    fetchMore,
    query,
    setQuery,
    refetch,
    totalCount,
    LoadMoreButton: nextPageUrl ? (
      <LoadMoreButton onClick={fetchMore} loading={isLoadMore} disabled={isLoadMore} />
    ) : null,
  };
};

export default useLoadMoreWithSearch;

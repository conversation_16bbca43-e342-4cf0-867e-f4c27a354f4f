/**
 * This function will remove object from given keys
 * @param {json} data - The form data
 * @param {array} keys - The keys to remove from the json
 * @returns {json} - The parsed data
 */
export function removeObjectsFromJson(data, keys = []) {
  if (!Array.isArray(keys) || keys.length < 1) return data;
  if (Array.isArray(data)) {
    return data.map((item) => removeObjectsFromJson(item, keys));
  } else if (typeof data === 'object' && data !== null) {
    return Object.fromEntries(
      Object.entries(data)
        .filter(([key]) => !keys.includes(key))
        .map(([key, value]) => [key, removeObjectsFromJson(value, keys)])
    );
  }
  return data;
}

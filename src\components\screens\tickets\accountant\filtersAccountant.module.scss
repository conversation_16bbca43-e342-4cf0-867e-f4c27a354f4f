@import '../../../../assets/scss/main.scss';

.priorityBtn {
  height: 2.5em;
  padding: 0 1em;
  background-color: $white;
  border-radius: 35px;
  box-shadow: 0 0 2px 2px $borderColor;
  display: flex;
  align-items: center;
  justify-content: center;
  text-wrap: nowrap;
  @include clickable;
  p {
    font-size: 1em !important;
  }
}

.activePriorityBtn {
  background-color: $accentColor2;
  color: $white;
  box-shadow: 0 0 2px 2px $accentBorder2;
}

.filtersMenuContainer {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100vh;
  height: 100dvh;
  overflow: hidden;
  z-index: 1000;
  background-color: #00000072;
  backdrop-filter: blur(0.6px);
}

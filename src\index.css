@tailwind base;
@tailwind components;
@tailwind utilities;

body {
  margin: 0;
  font-family:
    -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Robot<PERSON>', 'Oxygen', 'Ubuntu', 'Can<PERSON>ell', 'Fira Sans', 'Droid Sans',
    'Helvetica Neue', sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

code {
  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New', monospace;
}

.loaderWrapper {
  width: 100vw;
  height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}

.main_loader {
  height: 15px;
  aspect-ratio: 5;
  display: flex;
  justify-content: space-between;
  --_g: no-repeat radial-gradient(farthest-side, #585858 95%, #0000);
  background: var(--_g), var(--_g);
  background-size: 20% 100%;
  animation: l42-0 1s infinite;
}

.main_loader:before,
.main_loader:after {
  content: '';
  height: inherit;
  aspect-ratio: 1;
  border-radius: 50%;
  background: #2e90ff;
  animation: l42-1 1s infinite;
}

.main_loader:after {
  --s: -1, -1;
}

@keyframes l42-0 {
  0%,
  60% {
    background-position:
      calc(1 * 100% / 3) 0,
      calc(2 * 100% / 3) 0;
  }

  100% {
    background-position:
      calc(0 * 100% / 3) 0,
      calc(3 * 100% / 3) 0;
  }
}

@keyframes l42-1 {
  0% {
    transform: scale(var(--s, 1)) translate(0, 0);
  }

  33% {
    transform: scale(var(--s, 1)) translate(0, 130%);
  }

  66% {
    transform: scale(var(--s, 1)) translate(calc(400% / 3), 130%);
  }

  100% {
    transform: scale(var(--s, 1)) translate(calc(400% / 3), 0);
  }
}

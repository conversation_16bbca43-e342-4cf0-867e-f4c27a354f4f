---
description: This tells how React components should be defined
globs: *.js, *.jsx
alwaysApply: false
---
- Use functional components  
- Define components using the `function` keyword  
- Extract reusable logic into custom hooks or components  
- Implement proper component composition  
- Implement cleanup in `useEffect` hooks only if necessary  
- Use `useCallback` to memoize callback functions  
- Use `useMemo` for expensive computations  
- Avoid inline function definitions in JSX  
- Implement code splitting using dynamic imports  
- Provide proper `key` props in lists (avoid using index as key)  
- Use inline functions only for one-liner callbacks with props (e.g., `onClick={() => setOpenModal(true)}`)  
- Use `apiClient` for API calls  
- Follow proper data fetching patterns (e.g., `React Query`)
- Use `LoadingWrapper` for loading or error states

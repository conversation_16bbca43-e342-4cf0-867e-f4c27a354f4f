import { Check, Loader2, X } from 'lucide-react';
import React, { useState } from 'react';
import { connectToZoho, disconnectFromZoho, zohoCallback } from '../../../../../services/syncSettingsServices';
import { useAuth } from '../../../../../../contexts/AuthContext';
import { toast } from 'react-toastify';
import { getErrorMessage } from '../../../../../utils/apiUtils';
import { authPopup } from '../../../../../utils/authUtils';

function ZohoAuthBtn({ setIsConnected, isConnected, organizationId, region }) {
  const [isConnecting, setIsConnecting] = useState(false);
  const [isDisconnecting, setIsDisconnecting] = useState(false);
  const { globSelectedBusiness } = useAuth();
  const handleConnectDisconnect = () => {
    if (isConnected) {
      setIsDisconnecting(true);
      disconnectFromZoho(globSelectedBusiness?.business_id)
        .then(() => {
          setIsConnected(false);
          setIsDisconnecting(false);
          toast.success('Zoho disconnected successfully!');
        })
        .catch((err) => {
          const error = getErrorMessage(err);
          toast.error(error);
          setIsDisconnecting(false);
        });
    } else {
      setIsConnecting(true);
      const redirectUrl = window.location.href;
      connectToZoho(globSelectedBusiness?.business_id, organizationId, region)
        .then((res) => {
          const authUrl = res?.auth_url;
          if (!authUrl) throw new Error('No auth URL provided');
          authPopup(authUrl, redirectUrl, setIsConnecting)
            .then((res) => {
              const restAuthParams = res?.split('?')[1];
              if (!restAuthParams) throw new Error('No auth params provided');
              zohoCallback(globSelectedBusiness?.business_id, restAuthParams)
                .then(() => {
                  setIsConnected(true);
                  setIsConnecting(false);
                  toast.success('Zoho connected successfully!');
                })
                .catch((err) => {
                  const error = getErrorMessage(err);
                  toast.error(error);
                  setIsConnecting(false);
                });
            })
            .catch((err) => {
              const error = getErrorMessage(err);
              toast.error(error);
            });
        })
        .catch((err) => {
          const error = getErrorMessage(err);
          toast.error(error);
          setIsConnecting(false);
        });
    }
  };

  return (
    <div className="flex flex-wrap items-center gap-3 ml-3">
      <button
        onClick={handleConnectDisconnect}
        disabled={isConnecting || isDisconnecting || (!organizationId && !isConnected)}
        className={`flex items-center justify-center py-2.5 px-6 rounded-xl transition-colors duration-200 whitespace-nowrap select-none gap-2 ${
          isConnecting || isDisconnecting || (!organizationId && !isConnected)
            ? 'opacity-50 cursor-not-allowed'
            : 'hover:opacity-90'
        } ${isConnected ? 'bg-red-500 text-white hover:bg-red-600' : 'common-btn-schema'}`}
        data-tooltip-id="tooltip"
        data-tooltip-content={!organizationId && !isConnected ? 'Please enter organization ID' : ''}
      >
        {isConnecting || isDisconnecting ? <Loader2 className="w-5 h-5 animate-spin" /> : null}
        {isConnecting ? 'Connecting...' : isDisconnecting ? 'Disconnecting...' : isConnected ? 'Disconnect' : 'Connect'}
      </button>

      <div className={`flex items-center gap-3 p-1.5 rounded-lg ${isConnected ? 'bg-success-bg' : 'bg-error-bg'}`}>
        <span className="text-lg font-medium text-primary-color">Status:</span>
        <div className="flex items-center gap-2">
          {isConnected ? <Check className="w-5 h-5 text-success" /> : <X className="w-5 h-5 text-error" />}
          <span className={`font-semibold ${isConnected ? 'text-success' : 'text-error'}`}>
            {isConnected ? 'Connected' : 'Not Connected'}
          </span>
        </div>
      </div>
    </div>
  );
}

export default ZohoAuthBtn;

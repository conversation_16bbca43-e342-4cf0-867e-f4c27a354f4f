import React, { useMemo, useCallback, useState } from 'react';
import { Form, Button, Input, TextArea, Checkbox } from 'semantic-ui-react';
import { Formik } from 'formik';
import * as Yup from 'yup';
import { useAuth } from '../../contexts/AuthContext';
import SearchableFormSelect from './SearchableFormSelect';
import { resturls } from '../utils/apiurls';
import style from './scss/ticketForm2.module.scss';
import ticketServices from '../services/ticketServices';
import { transformToDropdownOptions } from '../utils';
import { toast } from 'react-toastify';
import useServiceFetch from '../global/hooks/useServiceFetch';

const defaultInitialValues = {
  organization: '',
  subject: '',
  priority: 'normal',
  category: '',
  sub_category: '',
  assign_to: '',
  requested_by: '',
  description: '',
};

const validationSchema = Yup.object({
  organization: Yup.string().required('Organization is required'),
  subject: Yup.string().required('Subject is required'),
  category: Yup.string().required('Category is required'),
  assign_to: Yup.string().required('Assign to is required'),
});

const TicketForm2 = ({
  initialValues = {},
  hideFields = [],
  disableFields = [],
  isEdit = false,
  initialDropdownValues = {},
  onSubmitIfSucess = null,
}) => {
  const { userInfo, role } = useAuth();
  const [subCategory, setSubCategory] = useState(initialDropdownValues.sub_category ?? []);
  const [realTimeInitialValues, setRealTimeInitialValues] = useState(initialValues);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { data, loading } = useServiceFetch(ticketServices.getAllAccountantUsers);
  const finalInitialValues = useMemo(
    () => ({
      ...defaultInitialValues,
      ...realTimeInitialValues,
      assign_to_me: realTimeInitialValues?.assign_to === userInfo?.userId || false,
    }),
    [realTimeInitialValues, userInfo]
  );

  const onSubmit = useCallback(
    (values) => {
      setIsSubmitting(true);
      const changedValues = {};
      Object.keys(finalInitialValues).forEach((key) => {
        if (finalInitialValues[key] !== values[key] && key !== 'assign_to_me') {
          changedValues[key] = values[key];
        }
      });
      if (isEdit) {
        ticketServices
          .updateTicket(initialValues.id, changedValues)
          .then(() => {
            toast.success('Ticket updated successfully');
            onSubmitIfSucess && onSubmitIfSucess();
            setRealTimeInitialValues(values);
          })
          .catch((err) => toast.error(err?.message || 'Something went wrong'))
          .finally(() => setIsSubmitting(false));
      }
    },
    [finalInitialValues]
  );

  return (
    <div className={style.formWrapper}>
      <Formik
        initialValues={finalInitialValues}
        validationSchema={validationSchema}
        onSubmit={onSubmit}
        validateOnChange={true}
        validateOnBlur={true}
        enableReinitialize={true}
      >
        {({
          values,
          handleChange,
          handleBlur,
          setFieldValue,
          setFieldTouched,
          handleSubmit,
          dirty,
          errors,
          touched,
          isValid,
          resetForm,
        }) => (
          <Form onSubmit={handleSubmit}>
            {!hideFields.includes('organization') && (
              <FormField label="Organization" error={errors.organization} touched={touched.organization}>
                <SearchableFormSelect
                  url={isEdit ? resturls.ticketList + initialValues.id + '/businesses/' : resturls.getBusinesses}
                  queryParams="business_name"
                  placeholder="Select Organization"
                  name="organization"
                  initialValue={initialDropdownValues.organization}
                  value={values.organization}
                  onChange={(e, { value }) => {
                    setFieldValue('organization', value);
                    setFieldTouched('organization', true);
                  }}
                  className="customDropdown customDropdown4withLoadMore"
                />
              </FormField>
            )}

            {!hideFields.includes('subject') && (
              <FormField label="Subject" error={errors.subject} touched={touched.subject}>
                <Input
                  name="subject"
                  placeholder="Enter subject"
                  value={values.subject}
                  onChange={handleChange}
                  onBlur={handleBlur}
                />
              </FormField>
            )}

            {!hideFields.includes('priority') && (
              <FormField label="Priority">
                <div className={style.priorityBtnContainer}>
                  {['Normal', 'High priority'].map((priority) => (
                    <span
                      key={priority}
                      className={values.priority === priority ? style.active : ''}
                      onClick={() => setFieldValue('priority', priority)}
                    >
                      {priority.split(' ')[0]}
                    </span>
                  ))}
                </div>
              </FormField>
            )}

            {!hideFields.includes('category') && (
              <FormField label="Category" error={errors.category} touched={touched.category}>
                <SearchableFormSelect
                  url={resturls.obtainCategortList}
                  queryParams="category"
                  placeholder="Select Category"
                  name="category"
                  transformOptions={{ key: 'id', value: 'id', text: 'name' }}
                  value={values.category}
                  initialValue={initialDropdownValues.category}
                  subOptions="subcategories"
                  className="customDropdown customDropdown4withLoadMore"
                  onChange={(e, { value, options }) => {
                    setSubCategory((prev) => {
                      const selectedOption = options.find((option) => option.value === value);
                      return selectedOption?.subOptions ? transformToDropdownOptions(selectedOption.subOptions) : prev;
                    });
                    setFieldValue('category', value);
                    setFieldTouched('category', true);
                  }}
                />
              </FormField>
            )}

            {!hideFields.includes('sub_category') && (
              <FormField label="Sub Category" error={errors.sub_category} touched={touched.sub_category}>
                <Form.Select
                  name="sub_category"
                  placeholder="Enter Sub Category"
                  options={subCategory}
                  value={values.sub_category}
                  className="customDropdown customDropdown4withLoadMore"
                  onChange={(e, { value }) => {
                    setFieldValue('sub_category', value);
                    setFieldTouched('sub_category', true);
                  }}
                />
              </FormField>
            )}

            {!hideFields.includes('assign_to') && (
              <FormField label="Assign to" error={errors.assign_to} touched={touched.assign_to}>
                <Form.Select
                  options={transformToDropdownOptions(data?.data, {
                    keyProp: 'user_id',
                    valueProp: 'user_id',
                    textProp: 'full_name',
                  })}
                  className="customDropdown customDropdown4withLoadMore"
                  name="assign_to"
                  value={String(values.assign_to)}
                  disabled={values.assign_to_me}
                  onChange={(e, { value }) => {
                    setFieldValue('assign_to', value);
                    setFieldTouched('assign_to', true);
                  }}
                  selectOnBlur={false}
                  loading={loading}
                  placeholder="Select Assignee"
                />
                <Checkbox
                  label="Assign to me"
                  name="assign_to_me"
                  checked={values.assign_to_me}
                  onChange={(e, { checked }) => {
                    setFieldValue('assign_to_me', checked);
                    setFieldValue('assign_to', checked ? userInfo?.userId : initialValues.assign_to);
                  }}
                />
              </FormField>
            )}

            {!hideFields.includes('requested_by') && (
              <FormField label="Requested By (optional)" error={errors.requested_by} touched={touched.requested_by}>
                <Form.Select
                  placeholder="Select Requested By"
                  fluid
                  selection
                  options={initialDropdownValues.requested_by}
                  name="requested_by"
                  value={values.requested_by}
                  disabled={disableFields.includes('requested_by')}
                  className="customDropdown customDropdown4withLoadMore"
                />
              </FormField>
            )}

            {!hideFields.includes('account_manager') && (
              <FormField label="Account Manager" error={errors.account_manager} touched={touched.account_manager}>
                <Form.Select
                  placeholder="Select Account Manager"
                  fluid
                  selection
                  options={initialDropdownValues.account_manager}
                  name="account_manager"
                  value={values.account_manager}
                  disabled={disableFields.includes('account_manager')}
                  className="customDropdown customDropdown4withLoadMore"
                />
              </FormField>
            )}

            {!hideFields.includes('description') && (
              <FormField label="Description (optional)" error={errors.description} touched={touched.description}>
                <TextArea
                  name="description"
                  placeholder="Enter description"
                  value={values.description}
                  onChange={handleChange}
                  onBlur={handleBlur}
                />
              </FormField>
            )}

            <div className={style.formBtnContainer}>
              <Button type="button" secondary disabled={!dirty} onClick={() => resetForm()} className={style.resetBtn}>
                Reset
              </Button>
              <Button type="submit" primary disabled={!dirty || !isValid || isSubmitting} className={style.submitBtn}>
                {isEdit ? 'Update' : 'Submit'}
              </Button>
            </div>
          </Form>
        )}
      </Formik>
    </div>
  );
};

const FormField = ({ label, error, touched, children }) => (
  <Form.Field error={!!error && touched} className={style.formField}>
    <label>{label}</label>
    {children}
    {error && touched && <div className="ui pointing red basic label">{error}</div>}
  </Form.Field>
);

export default React.memo(TicketForm2);

import React, { useMemo, useState } from 'react';
import style from './ticketView.module.scss';
import Header from '../../global/Header';
import NavigationBar from '../NavigationBar';
import { useParams } from 'react-router-dom';
import TicketInfo from './common/TicketInfo';
import useServiceFetch from '../../global/hooks/useServiceFetch';
import ticketServices from '../../services/ticketServices';
import CommentInput from './common/CommentInput';
import Comments from './common/Comments';
import LoadingWrapper from '../../global/components/LoadingWrapper';
import TicketForm2 from '../../custom-components/TicketForm2';
import { transformToDropdownOptions } from '../../utils';
import { useAuth } from '../../../contexts/AuthContext';
import BackBtn from '../../ui-components/BackBtn';
import LayoutWrapper from '../../generic-components/LayoutWrapper';

function TicketView() {
  const { id } = useParams();
  const { isMobileScreen, roleType } = useAuth();
  const { data, error, loading, refetch } = useServiceFetch(() => ticketServices.getTicketById(id));

  const initialValues = useMemo(() => {
    if (!data?.results[0]) return {};
    const results = data?.results[0];
    return {
      values: {
        id,
        organization: results?.business?.business_id,
        category: results?.category?.id,
        sub_category: results?.sub_category?.id,
        assign_to: results?.assign_to?.user_id,
        subject: results?.subject,
        priority: results?.priority,
        requested_by: results?.requested_by?.user_id,
        account_manager: results?.business?.account_manager?.id,
      },
      initialDropdownValues: {
        organization: {
          key: results?.business?.business_id,
          value: results?.business?.business_id,
          text: results?.business?.business_name,
        },
        category: {
          key: results?.category?.id,
          value: results?.category?.id,
          text: results?.category?.name,
        },
        assign_to: {
          key: results?.assign_to?.user_id,
          value: results?.assign_to?.user_id,
          text: results?.assign_to?.user_full_name,
        },
        sub_category: transformToDropdownOptions(results?.category?.subcategories),
        requested_by: [
          {
            key: results?.requested_by?.user_id,
            value: results?.requested_by?.user_id,
            text: results?.requested_by?.user_full_name,
          },
        ],
        account_manager: [
          {
            key: results?.business?.account_manager?.id,
            value: results?.business?.account_manager?.id,
            text: results?.business?.account_manager?.full_name,
          },
        ],
      },
    };
  }, [data]);

  return (
    <LayoutWrapper>
      <LoadingWrapper loading={loading} error={error}>
        <div className={style.ticketViewContent}>
          {isMobileScreen ? (
            <MobileView roleType={roleType} initialValues={initialValues} data={data} refetch={refetch} />
          ) : (
            <DesktopView roleType={roleType} initialValues={initialValues} data={data} refetch={refetch} />
          )}
        </div>
      </LoadingWrapper>
    </LayoutWrapper>
  );
}

/**
 * Separate components for desktop/mobile views are required due to
 * fundamentally different layouts and interactions
 * (This is much easier than handling it with completely CSS)
 */

const DesktopView = React.memo(({ roleType, initialValues, data, refetch }) => {
  return (
    <>
      <div className={style.infoAndFormContainer}>
        <BackBtn text="GO TO PREVIOUS PAGE" />

        <TicketInfo ticket={data?.results[0]} />

        {roleType !== 'user' && (
          <div className={style.formContainer}>
            <TicketForm2
              initialValues={initialValues.values}
              initialDropdownValues={initialValues.initialDropdownValues}
              hideFields={['description']}
              disableFields={['requested_by', 'account_manager']}
              isEdit={true}
              onSubmitIfSuccess={refetch}
            />
          </div>
        )}
      </div>

      <div className={style.commentsContainer}>
        {data?.results && <Comments ticket={data?.results[0]} />}
        <div className={style.commentInputContainer}>
          <CommentInput />
        </div>
      </div>
    </>
  );
});

const MobileView = React.memo(({ roleType, initialValues, data }) => {
  const [activeTab, setActiveTab] = useState(() => (roleType === 'user' ? 'activity' : 'info'));

  return (
    <>
      <TicketInfo ticket={data?.results[0]} />

      {roleType !== 'user' && (
        <>
          <div className={style.tabSwitchBtnsContainer}>
            <div className={style.tabSwitchBtns}>
              <p className={activeTab === 'info' && style.active} onClick={() => setActiveTab('info')}>
                Info
              </p>
              <p className={activeTab === 'activity' && style.active} onClick={() => setActiveTab('activity')}>
                Activities
              </p>
            </div>
          </div>
          <div className={`${activeTab === 'activity' ? 'hide' : ''}`}>
            <TicketForm2
              initialValues={initialValues.values}
              initialDropdownValues={initialValues.initialDropdownValues}
              hideFields={['description']}
              disableFields={['requested_by', 'account_manager']}
              isEdit={true}
            />
          </div>
        </>
      )}

      <div className={`${style.commentsContainer} ${activeTab === 'info' ? 'hide' : ''}`}>
        {data?.results && <Comments ticket={data?.results[0]} />}
        <div className={style.commentInputContainer}>
          <CommentInput />
        </div>
      </div>
    </>
  );
});

export default React.memo(TicketView);

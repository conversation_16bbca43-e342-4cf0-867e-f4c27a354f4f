import React, { useState } from 'react';
import PropTypes from 'prop-types';
import { Input, Popup } from 'semantic-ui-react';
import style from '../../screens/phase2/scss/DateRangeDropdown.module.scss';
import { calenderIcon, dropdownIcon, radioSelectedIcon } from '../../global/Icons';

DateRangeDropdown.propTypes = {
  dropdownOptions: PropTypes.arrayOf(
    PropTypes.shape({
      id: PropTypes.string.isRequired,
      label: PropTypes.string.isRequired,
    })
  ).isRequired,
  setDateData: PropTypes.string.isRequired,
  isResponsive: PropTypes.func.isRequired,
  dateData: PropTypes.object,
};

function getDateRange(value) {
  const currentDate = new Date();
  const endDate = new Date();
  let startDate = new Date();
  switch (value) {
    case 'last7days':
      startDate.setDate(currentDate.getDate() - 6);
      break;

    case 'monthToDate':
      startDate = new Date(currentDate.getFullYear(), currentDate.getMonth(), 1);
      break;

    case 'last30days':
      startDate.setDate(currentDate.getDate() - 29); // Last 30 days includes today
      break;

    case 'quarterToDate':
      const quarterStartMonth = Math.floor(currentDate.getMonth() / 3) * 3;
      startDate = new Date(currentDate.getFullYear(), quarterStartMonth, 1); // First day of the current quarter
      break;

    case 'yearToDate':
      startDate = new Date(currentDate.getFullYear(), 0, 1); // First day of the current year
      break;

    default:
      console.log('Invalid value provided');
  }

  // Helper function to format dates as YYYY-MM-DD
  function formatDate(date) {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
  }

  return { startDate: formatDate(startDate), endDate: formatDate(endDate) };
}

export default function DateRangeDropdown({ dropdownOptions = [], setDateData, isResponsive, dateData }) {
  const [selectedTimeline, setTimeline] = useState(dropdownOptions[0]);
  const [openDropdown, setOpenDropdown] = useState(false);

  const handleSelectDropdown = (list) => {
    setTimeline(list);
    if (list?.value !== 'customDate') {
      setDateData(getDateRange(list?.value));
    }
    if (list?.value !== 'customDate' && !isResponsive) {
      setOpenDropdown(false);
    }
  };
  const renderPopupContent = () => {
    return (
      <div className={style.popupContainer}>
        {dropdownOptions?.map((list) => (
          <div
            className={`${selectedTimeline?.value === list?.value ? style.selectedItem : ''} ${style.dropDownItem}`}
            onClickCapture={() => handleSelectDropdown(list)}
          >
            <div className={style.option}>
              <span className={`${style.customRadio}`}>{radioSelectedIcon()}</span>
              <p>{list?.text}</p>
            </div>
            {selectedTimeline?.value === 'customDate' && selectedTimeline?.value === list?.value && (
              <div className={style.dateContainer}>
                <Input
                  className={style.dateInput}
                  type="date"
                  placeholder="Select a date"
                  onChange={(e) => {
                    setDateData({ ...dateData, startDate: e.target.value });
                  }}
                />
                <Input
                  className={style.dateInput}
                  type="date"
                  placeholder="Select a date"
                  onChange={(e) => setDateData({ ...dateData, endDate: e.target.value })}
                />
              </div>
            )}
          </div>
        ))}
      </div>
    );
  };

  const handleDropdownList = () => {
    setOpenDropdown(true);
  };

  return (
    <>
      {!isResponsive && (
        <div className={style.dropdownWrapper}>
          <Popup
            className={style.popup}
            trigger={
              <div className={style.dropDown} onClick={() => handleDropdownList()}>
                <p>
                  {calenderIcon()}
                  {selectedTimeline?.text}
                </p>
                {dropdownIcon()}
              </div>
            }
            content={renderPopupContent()}
            position="bottom left"
            on="click"
            hoverable
            basic
            open={!isResponsive && openDropdown}
          />
        </div>
      )}
      {isResponsive && openDropdown && renderPopupContent()}
    </>
  );
}

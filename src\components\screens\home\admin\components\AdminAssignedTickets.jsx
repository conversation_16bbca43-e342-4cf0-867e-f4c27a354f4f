import React from 'react';
import style from './scss/adminAssignedTickets.module.scss';
import usePaginationWithSearch from '../../../../global/hooks/usePaginationWithSearch';
import { useAuth } from '../../../../../contexts/AuthContext';
import { resturls } from '../../../../utils/apiurls';
import { PlusIcon, RigthArrowIcon } from '../../../../../assets/svgs';
import { useNavigate } from 'react-router-dom';
import { Dropdown, Input } from 'semantic-ui-react';
import { DropdownIcon } from '../../../../global/Icons';
import { LoadingWrapper } from '../../../../global/components';
import TicketsListDualDetailTable from '../../../../custom-components/TicketsListDualDetailTable';
import SearchableFormSelect from '../../../../custom-components/SearchableFormSelect';

const TicketStatus = [
  { key: 'All', value: '', text: 'All' },
  { key: 'open', value: 'Open', text: 'Open', className: style.openStatus },
  {
    key: 'pending',
    value: 'Pending',
    text: 'Pending',
    className: style.pendingStatus,
  },
  {
    key: 'closed',
    value: 'Closed',
    text: 'Closed',
    className: style.closedStatus,
  },
  {
    key: 'verified',
    value: 'Verified',
    text: 'Verified',
    className: style.verifiedStatus,
  },
  {
    key: 'deleted',
    value: 'Deleted',
    text: 'Deleted',
    className: style.deletedStatus,
  },
];

function AdminAssignedTickets() {
  const { userInfo } = useAuth();
  const navigate = useNavigate();
  const {
    data: assignedTickets,
    totalItemsCount,
    loading,
    query,
    setQuery,
    PaginationComponent,
    setExtraParams,
  } = usePaginationWithSearch({
    url: `${resturls.ticketList}?assign_to=${userInfo?.userId}`,
    queryParam: 'subject',
  });

  return (
    <>
      <div className={style.ticketListContainer}>
        <div className={style.ticketListHeader}>
          <h4 className={style.headerLeft}>
            Your Tickets
            <span className={style.orgCount}>{totalItemsCount}</span>
          </h4>

          <div className={style.headerRight}>
            <span className={style.viewAllBtn} onClickCapture={() => navigate('/ticketManagement')}>
              View all <RigthArrowIcon />
            </span>
          </div>
        </div>

        <div className={style.searchWrapper}>
          <div className={style.leftFilter}>
            <Input
              className={style.searchInput}
              icon="search"
              placeholder="Search"
              iconPosition="left"
              value={query}
              onChange={(e, { value }) => setQuery(value)}
            />
            <Dropdown
              placeholder="All"
              className={`customDropdown3 ${style.statusDropdown}`}
              icon={<DropdownIcon />}
              options={TicketStatus}
              onChange={(e, { value }) => setExtraParams((prev) => ({ ...prev, status: value }))}
            />
            <SearchableFormSelect
              url={resturls.getBusinesses}
              queryParams="business_name"
              placeholder="Organization"
              className="customDropdown5"
              icon={<DropdownIcon />}
              transformOptions={{
                key: 'business_id',
                value: 'business_id',
                text: 'business_name',
              }}
              clearable
              onChange={(e, { value }) => setExtraParams((prev) => ({ ...prev, business_id: value }))}
            />
          </div>
          <div className={style.rightFilter}>
            <span className={style.createTicketBtn} onClickCapture={() => navigate('/createTicket')}>
              <PlusIcon />
            </span>
          </div>
        </div>

        <div className={style.ticketListMainContainer}>
          <LoadingWrapper
            loading={loading}
            isRenderEmpty={assignedTickets.length < 1}
            renderEmpty={{
              title: 'No New Tickets',
              description: 'There are no tickets at the moment. Have a tea 🍵',
            }}
            wrapperClass={style.loaderWrapper}
          >
            <TicketsListDualDetailTable tickets={assignedTickets} />
            {PaginationComponent}
          </LoadingWrapper>
        </div>
      </div>
    </>
  );
}

export default AdminAssignedTickets;

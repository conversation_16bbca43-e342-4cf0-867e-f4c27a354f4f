import React, { useCallback, useState } from 'react';
import ticketServices from '../services/ticketServices';
import { toast } from 'react-toastify';
import style from './scss/ticketForm2.module.scss';

function TicketPriorityChangeBtn({ ticketId, initialPriority }) {
  const [currentPriority, setCurrentPriority] = useState(initialPriority);

  const handlePriorityChange = useCallback(
    (priority) => {
      if (currentPriority === priority) return;
      setCurrentPriority(priority);
      ticketServices
        .updateTicket(ticketId, { priority })
        .then(() => toast.success('Priority updated successfully'))
        .catch((err) => toast.error(err.message || 'Something went wrong'));
    },
    [ticketId]
  );

  return (
    <div className={style.priorityBtnContainer}>
      {['Normal', 'High priority'].map((priority) => (
        <span
          key={priority}
          className={currentPriority === priority ? style.active : ''}
          onClick={() => handlePriorityChange(priority)}
        >
          {priority.split(' ')[0]}
        </span>
      ))}
    </div>
  );
}

export default TicketPriorityChangeBtn;

import React from 'react';
import { useNavigate } from 'react-router-dom';
import style from './scss/notification.module.scss';
import GlobalService from '../services/GlobalServices';
import { resturls } from '../utils/apiurls';
import { ticketLogIcon } from '../global/Icons';
import { convertDateTimeFormat } from '../utils';
import { RigthArrowIcon } from '../../assets/svgs';

const NotificationItem = ({ info, setNotifications, admin, setNoticationActive }) => {
  const navigate = useNavigate();

  const notificationRead = () => {
    if (info?.is_read === true) {
      redirectComment(info);
      return;
    }

    GlobalService.generalSelect(
      () => {
        setNotifications((prevNotifications) =>
          prevNotifications.map((notification) =>
            notification.id === info.id ? { ...notification, is_read: true } : notification
          )
        );
        redirectComment(info);
      },
      `${resturls.notificationRead}${info?.id}`,
      {},
      'GET'
    );
  };

  const redirectComment = (info) => {
    GlobalService.generalSelect(
      (respdata) => {
        const businessId = respdata.business?.business_id ?? respdata.business_id;
        const ticketId = respdata.business?.business_id ? respdata.id : respdata.ticket;

        console.log(businessId, ticketId);

        if (businessId && ticketId) {
          if (setNoticationActive) {
            setNoticationActive(false);
          }

          navigate(`/ticketList/${businessId}/${ticketId}`);
        }
      },
      `${info?.link}`,
      {},
      'GET'
    );
  };

  return (
    <div className={`${!info?.is_read && style.isReaded} ${style.notificationItem}`}>
      {!info?.is_read && <span className={style.redIcon} />}
      <div className={style.contentWrapper}>
        {!admin && <div className={style.ticketLogIcon}>{ticketLogIcon()}</div>}
        <div>
          <p className={style.message}>{info.message}</p>
          <p className={style.time}>{convertDateTimeFormat(info.created_at, true)}</p>
        </div>
      </div>
      <div className={style.viewIcon} onClickCapture={notificationRead}>
        <RigthArrowIcon />
      </div>
    </div>
  );
};

export default NotificationItem;

@import '../../../../../assets/scss/main.scss';

.commentsWrapper {
  display: flex;
  flex-direction: column-reverse;
  overflow-y: auto;
  height: 66vh;
  padding-right: 2em;
  background-color: $white;
  padding: 1em 1.5em 2em 1.5em;
  @include for_media(mobileScreen) {
    flex-direction: column;
    height: unset;
    overflow-y: unset;
    padding-right: 0.5em;
    margin-top: 0;
  }
}

.commentContainer {
  display: flex;
  align-items: center;
  gap: 1em;
  @include for_media(mobileScreen) {
    height: 100%;
  }
  .timelineContainer {
    width: 2em;
    height: 100%;
    position: relative;
    z-index: 100;
    .avatar {
      position: relative;
      width: 2em !important;
      height: 2em !important;
      border-radius: 50%;
      z-index: 2;
      background-color: $accentColor2 !important;
      span {
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.5em !important;
        font-weight: 600;
        color: $white;
        @include for_media(mobileScreen) {
          font-size: 1.3em !important;
        }
      }
    }
    &::after {
      content: '';
      position: absolute;
      top: 0;
      left: 45%;
      width: 3px;
      height: 100%;
      background-color: darken($accentBgColor1, 20%);
    }
  }
  .contentContainer {
    width: 100%;
    display: flex;
    flex-direction: column;
    justify-content: left;
    align-items: center;
    margin-bottom: 1.5em;
    @include for_media(mobileScreen) {
      width: 82%;
    }
    .infoContainer {
      display: flex;
      width: 100%;
      justify-content: space-between;
      padding-bottom: 0.5em;
      .nameContainer {
        display: flex;
        align-items: center;
        gap: 0.5em;
        .nameWrapper {
          text-transform: capitalize;
          font-size: 1.2em;
          display: flex;
          align-items: center;
          @include for_media(mobileScreen) {
            font-size: 1.1em;
          }
          .ticketCreatedBy {
            font-weight: 900;
            margin-right: 10px;
          }
          .private {
            svg {
              width: 20px;
              height: 20px;
              margin-left: 0.2em;
            }
          }
        }
      }
      .timeContainer {
        flex-shrink: 0;
        color: gray;
        font-size: 0.9em;
      }
    }
    .messageContainer {
      display: flex;
      flex-direction: column;
      justify-content: left;
      align-items: flex-start;
      width: 100%;
      margin-bottom: 2em;
      .message {
        padding: 0;
        min-width: 5em;
        font-size: 1.1em !important;
        font-weight: 500;
        color: #4d4d4d;
        width: 100%;
        margin: 0;
      }
    }
  }
}

.emptyProfile {
  width: 2em !important;
  height: 2em !important;
  border-radius: 50%;
  position: relative;
  z-index: 2;
  border-radius: 100%;
  background-color: $accentBgColor1;
  &::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 1.2em !important;
    height: 1.2em !important;
    background-color: $accentColor2;
    border-radius: 100%;
  }
}

@import '../../../assets/scss/main.scss';

.dateFilterBtn {
  display: flex !important;
  align-items: center !important;
  background-color: #eaecf5 !important;
  gap: 0.5em;
  border: 1px solid #9ea5d1 !important;
  border-radius: 0.5em !important;
  color: #333 !important;
  padding: 0.8em 1.2em !important;
  font-weight: normal !important;
  transition: all 0.2s ease !important;
  box-shadow: none !important;
  width: fit-content !important;
  cursor: pointer;
}

.dateInput {
  width: 100%;

  input {
    border-radius: 0.25em !important;
    border: 1px solid #e1e4e8 !important;
    padding: 0.75em 1em !important;
    font-size: 0.875em !important;
    background-color: #f8f9fa !important;
    color: #333 !important;
    width: 100% !important;

    &:focus {
      border-color: #3b82f6 !important;
      box-shadow: 0 0 0 0.125em rgba(59, 130, 246, 0.2) !important;
    }

    &::-webkit-calendar-picker-indicator {
      cursor: pointer;
      color: #666 !important;
    }
  }
}

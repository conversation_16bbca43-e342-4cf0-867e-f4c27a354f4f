@import '../../../assets//scss/main.scss';

.defaultHeader {
  background-color: $primaryBgColor !important;
  position: relative !important;
}
.mainContainer {
  height: 50vh;
  top: 1em;
  background: linear-gradient(to bottom, #f6f8fa 50%, #f6f8fa 50%);
  @include for_media(mobileScreen) {
    background: unset;
    height: auto;
  }
}
.heading {
  font-size: 2em !important;
  @include for_media(mobileScreen) {
    font-size: 1.5em !important;
    text-align: left;
    width: 100%;
    padding: 0.8em 1em;
    display: flex;
    justify-content: space-between;
  }
  @include for_media(bigTabletScreen) {
    visibility: hidden;
  }

  @include for_media(tabletScreen) {
    display: none;
  }
}

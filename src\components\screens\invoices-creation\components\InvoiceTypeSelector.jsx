import { TaskAlt } from '@mui/icons-material';

export default function InvoiceTypeSelector({ invoiceType, setInvoiceType }) {
  return (
    <>
      <h3 className="text-lg font-semibold text-primary-color mb-2 select-none">
        Invoice Type
        <span className="text-error text-sm ml-1">*</span>
      </h3>

      <div className="grid grid-cols-2 gap-3 select-none">
        <label
          className={`relative flex items-center justify-center p-2 border-2 rounded-lg cursor-pointer transition-all duration-200 ${
            invoiceType === 'purchase'
              ? 'border-accent2 bg-accent1-bg text-accent2'
              : 'border-gray-200 hover:border-gray-300 text-gray-700'
          }`}
        >
          <input
            type="radio"
            name="invoiceType"
            value="purchase"
            checked={invoiceType === 'purchase'}
            onChange={() => setInvoiceType('purchase')}
            className="sr-only"
          />
          <div className="text-center">
            <div className="font-medium">Purchase</div>
            <div className="text-xs text-gray-500 mt-1">Purchase invoices</div>
          </div>
          {invoiceType === 'purchase' && (
            <div className="absolute top-2 right-2">
              <TaskAlt className="!h-4 !w-4 text-accent2" />
            </div>
          )}
        </label>

        <label
          className={`relative flex items-center justify-center p-2 border-2 rounded-lg cursor-pointer transition-all duration-200 ${
            invoiceType === 'expense'
              ? 'border-accent2 bg-accent1-bg text-accent2'
              : 'border-gray-200 hover:border-gray-300 text-gray-700'
          }`}
        >
          <input
            type="radio"
            name="invoiceType"
            value="expense"
            checked={invoiceType === 'expense'}
            onChange={() => setInvoiceType('expense')}
            className="sr-only"
          />
          <div className="text-center">
            <div className="font-medium">Expense</div>
            <div className="text-xs text-gray-500 mt-1">Expense invoices</div>
          </div>
          {invoiceType === 'expense' && (
            <div className="absolute top-2 right-2">
              <TaskAlt className="!h-4 !w-4 text-accent2" />
            </div>
          )}
        </label>
      </div>
    </>
  );
}

@import '../../../assets/scss/main.scss';

.notificationContainer {
  height: 100vh;

  @include for_media(mobileScreen) {
    .header {
      padding: 1.25rem;
      background-color: $white;
      position: fixed;
      top: 0;
      width: 100%;
      display: flex;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.08);
      z-index: 100;

      p {
        width: 100%;
        display: flex;
        justify-content: center;
        font-weight: 500;
      }
    }
  }

  .notificationList {
    padding: 2rem 1.25rem 7rem;
    height: 85%;
    overflow-y: auto;
    overflow-x: hidden;

    @include for_media(mobileScreen) {
      padding: 4.5rem 1.25rem 7rem;
    }

    &::-webkit-scrollbar {
      width: 6px;
    }

    &::-webkit-scrollbar-track {
      background: #f1f1f1;
    }

    &::-webkit-scrollbar-thumb {
      background: #888;
      border-radius: 3px;
    }
  }
}

.isReaded {
  background-color: #eef3ff !important;
  border: none !important;
  box-shadow: 0 1px 3px rgba(158, 165, 209, 0.2) !important;
}

.notificationItem {
  display: flex;
  gap: 1.2rem;
  padding: 1.5rem 2rem;
  position: relative;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  margin: 1rem 0;
  border-radius: 12px;
  background-color: $white;
  justify-content: space-between;
  align-items: center;
  transition: transform 0.2s ease;

  &:hover {
    transform: translateY(-2px);
  }

  .redIcon {
    height: 8px;
    width: 8px;
    border-radius: 50%;
    background-color: #ff4d4f;
    position: absolute;
    right: 12px;
    top: 12px;
  }

  .contentWrapper {
    display: flex;
    align-items: center;
    gap: 1.25rem;
    flex: 1;

    .message {
      margin: 0;
      font-weight: 400;
      line-height: 1.5;
      width: 100%;
      word-wrap: break-word;
      word-break: break-word;
      overflow-wrap: break-word;
      overflow: hidden;
      white-space: normal;
    }

    .time {
      margin-top: 0.625rem;
      color: #8f919a;
      font-size: 0.875rem;
    }
  }

  .viewIcon {
    height: 40px;
    width: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 1px solid #e8e9eb;
    cursor: pointer;
    transition: all 0.2s ease;

    &:hover {
      background-color: #f8f9fa;
      border-color: #d0d2d6;
    }

    svg {
      height: 22px;
      width: 22px;

      path {
        fill: #8c7008;
      }
    }
  }

  .notificationIcon {
    padding: 1rem;
    border-radius: 16px;
    border: 2px solid #e8e9eb;
    height: 50px;
    width: 50px;
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: $white;

    svg {
      width: 32px;
      height: 32px;
    }
  }
}

.noNotifications {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 60vh;
  gap: 1.25rem;

  p {
    margin: 0;
  }

  .desc {
    color: #8f919a;
    text-align: center;
    font-size: 0.9375rem;
  }

  @include for_media(mobileScreen) {
    height: 78vh;
  }
}

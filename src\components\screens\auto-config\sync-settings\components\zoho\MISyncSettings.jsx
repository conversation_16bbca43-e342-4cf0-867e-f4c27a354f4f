import { Ch<PERSON>ronDown, <PERSON>f<PERSON><PERSON><PERSON>, Zap } from 'lucide-react';
import React, { useState } from 'react';
import { toast } from 'react-toastify';
import { getErrorMessage } from '../../../../../utils/apiUtils';
import { useAuth } from '../../../../../../contexts/AuthContext';
import { updateBusinessPreferences } from '../../../../../services/syncSettingsServices';

const syncTimes = [
  '00:00:00',
  '01:00:00',
  '02:00:00',
  '03:00:00',
  '04:00:00',
  '05:00:00',
  '06:00:00',
  '07:00:00',
  '08:00:00',
  '09:00:00',
  '10:00:00',
  '11:00:00',
  '12:00:00',
  '13:00:00',
  '14:00:00',
  '15:00:00',
  '16:00:00',
  '17:00:00',
  '18:00:00',
  '19:00:00',
  '20:00:00',
  '21:00:00',
  '22:00:00',
  '23:00:00',
];

const getSettingsJsonKey = (key) => {
  switch (key) {
    case 'autoSyncMasters':
      return 'enable_auto_sync_master';
    case 'autoSyncValidation':
      return 'enable_auto_sync_invoice';
    case 'dailySyncTime':
      return 'sync_time';
    default:
      return key;
  }
};

function MISyncSettings({ isConnected, businessPreferences }) {
  const { globSelectedBusiness } = useAuth();

  const [MISettings, setMISettings] = useState({
    autoSyncMasters: businessPreferences?.enable_auto_sync_master,
    autoSyncValidation: businessPreferences?.enable_auto_sync_invoice,
    dailySyncTime: businessPreferences?.sync_time,
  });

  const handleChangeMISettings = (e, key) => {
    const jsonKey = getSettingsJsonKey(key);
    const value = key === 'dailySyncTime' ? e.target.value : e.target.checked;
    updateBusinessPreferences(globSelectedBusiness?.business_id, {
      [jsonKey]: value,
    })
      .then(() => {
        setMISettings((prev) => ({
          ...prev,
          [key]: value,
        }));
      })
      .catch((err) => {
        const errorMessage = getErrorMessage(err);
        toast.error(`Failed to update settings: ${errorMessage}`);
      });
  };

  return (
    <div className="mb-8 p-6 rounded-2xl shadow-lg bg-white border border-accent1-border">
      <div className="flex flex-wrap gap-12">
        <div className="flex-1 min-w-96">
          <div className="flex items-center gap-3 mb-4">
            <div className="flex items-center justify-center rounded w-6 h-6">
              <RefreshCw className={`w-6 h-6 ${!isConnected ? 'text-gray-400' : 'text-accent1'}`} />
            </div>
            <label
              className={`text-xl font-bold leading-none ${!isConnected ? 'text-gray-400' : 'text-primary-color'}`}
            >
              Master Sync Settings
            </label>
          </div>

          <div className="space-y-6 ml-3">
            <div
              className={`flex items-center justify-between p-4 rounded-lg ${
                !isConnected ? 'bg-gray-100' : 'bg-accent1-bg'
              }`}
            >
              <label className={`text-lg font-medium ${!isConnected ? 'text-gray-400' : ''}`}>Auto Sync Masters</label>
              <label
                className={`relative inline-flex items-center ${
                  !isConnected ? 'cursor-not-allowed opacity-50' : 'cursor-pointer'
                }`}
              >
                <input
                  type="checkbox"
                  checked={MISettings.autoSyncMasters}
                  onChange={(e) => handleChangeMISettings(e, 'autoSyncMasters')}
                  disabled={!isConnected}
                  className="sr-only peer"
                />
                <div
                  className={`w-12 h-7 rounded-full peer peer-checked:after:translate-x-full after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:rounded-full after:h-5 after:w-5 after:transition-all duration-200 ${
                    !isConnected
                      ? 'bg-gray-300 border-gray-300'
                      : MISettings?.autoSyncMasters
                        ? 'bg-accent1 border-accent1'
                        : 'bg-[#ccc] border-[#ccc]'
                  }`}
                >
                  <div
                    className={`absolute top-[2px] left-[2px] rounded-full h-5 w-5 transition-all duration-200 bg-white ${
                      MISettings?.autoSyncMasters ? 'translate-x-6' : 'translate-x-0'
                    }`}
                  ></div>
                </div>
              </label>
            </div>

            <div>
              <label
                className={`block text-lg font-semibold mb-3 ${!isConnected ? 'text-gray-400' : 'text-primary-color'}`}
              >
                Daily Sync Time
              </label>
              <div className="relative w-64 max-w-[200px]">
                <select
                  value={MISettings.dailySyncTime || ''}
                  onChange={(e) => handleChangeMISettings(e, 'dailySyncTime')}
                  disabled={!isConnected}
                  className={`w-full text-lg font-medium px-3 py-2 pr-12 rounded-xl border-2 focus:outline-none appearance-none ${
                    !isConnected
                      ? 'bg-gray-100 border-gray-300 text-gray-400 cursor-not-allowed'
                      : 'hover:shadow-md text-gray-900 bg-accent1-bg border-accent1-border'
                  }`}
                >
                  <option value="">Select Time</option>
                  {syncTimes.map((time) => (
                    <option key={time} value={time}>
                      {time}
                    </option>
                  ))}
                </select>
                <ChevronDown
                  className={`absolute right-4 top-1/2 transform -translate-y-1/2 w-3 h-3 pointer-events-none ${
                    !isConnected ? 'text-gray-400' : 'text-accent1'
                  }`}
                />
              </div>
            </div>
          </div>
        </div>

        <div className="flex-1 min-w-96">
          <div className="flex items-center gap-3 mb-4">
            <div className="flex items-center justify-center w-6 h-6">
              <Zap className={`w-8 h-8 ${!isConnected ? 'text-gray-400' : 'text-accent1'}`} />
            </div>
            <label
              className={`text-xl font-bold leading-none ${!isConnected ? 'text-gray-400' : 'text-primary-color'}`}
            >
              Invoice Sync Settings
            </label>
          </div>

          <div
            className={`flex items-center justify-between p-4 rounded-lg ${
              !isConnected ? 'bg-gray-100' : 'bg-accent1-bg'
            }`}
          >
            <label className={`text-lg font-medium ${!isConnected ? 'text-gray-400' : ''}`}>
              Auto Sync On Validation
            </label>
            <label
              className={`relative inline-flex items-center ${
                !isConnected ? 'cursor-not-allowed opacity-50' : 'cursor-pointer'
              }`}
            >
              <input
                type="checkbox"
                checked={MISettings?.autoSyncValidation}
                onChange={(e) => handleChangeMISettings(e, 'autoSyncValidation')}
                disabled={!isConnected}
                className="sr-only peer"
              />
              <div
                className={`w-12 h-7 rounded-full peer peer-checked:after:translate-x-full after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:rounded-full after:h-5 after:w-5 after:transition-all duration-200 ${
                  !isConnected
                    ? 'bg-gray-300 border-gray-300'
                    : MISettings?.autoSyncValidation
                      ? 'bg-accent1 border-accent1'
                      : 'bg-[#ccc] border-[#ccc]'
                }`}
              >
                <div
                  className={`absolute top-[2px] left-[2px] rounded-full h-5 w-5 transition-all duration-200 bg-white ${
                    MISettings?.autoSyncValidation ? 'translate-x-6' : 'translate-x-0'
                  }`}
                ></div>
              </div>
            </label>
          </div>
        </div>
      </div>
    </div>
  );
}

export default MISyncSettings;

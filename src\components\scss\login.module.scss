@import '../../assets/scss/main.scss';

.mainContainer {
  height: 100vh;
  height: 100dvh;
  .logoWrapper {
    display: flex;
    justify-content: flex-start;
    // padding: 1em 2em;
    img {
      width: 50%;
    }

    @include for_media(mobileScreen) {
      img {
        width: 65%;
        display: block !important;
      }
    }
  }
  .contentWrapper {
    display: flex;
    justify-content: space-evenly;
    gap: 2em;
    background-color: $white;
    width: 100%;
    max-height: 100%;
    padding: 2em;
    border-radius: 35px;
    overflow-y: auto;
    @include for_media(mobileScreen) {
      padding: 1em;
      gap: 0;
      height: 100%;
      flex-direction: column;
      align-items: center;
    }
    @include for_media(tabletScreen) {
      flex-direction: column !important;
      align-items: center;
    }
    @include for_media(bigTabletScreen) {
      flex-direction: column !important;
      align-items: center;
    }
    .leftContainer {
      max-width: 60%;
      img {
        max-width: 85% !important;
        max-height: 100%;
      }
      @include for_media(mobileScreen) {
        max-width: 100%;
      }
      @include for_media(bigTabletScreen) {
        width: 50% !important;
      }
    }
    .forotScreenImg {
      justify-content: center;
      display: flex;
      img {
        max-width: 88% !important;
      }
    }
    .rightContainer {
      display: flex;
      flex-direction: column;
      padding: 5%;
      box-shadow: 0 0 50px 2px $grayColor4;
      border-radius: 10px;
      width: 40%;
      min-height: 550px;

      .loginText {
        color: #2b5ba1;
        text-transform: uppercase;
        text-decoration: none;
        border-bottom: 1px solid transparent;
        transition: border-color 0.2s ease;
        cursor: pointer;

        &:hover {
          border-bottom-color: currentColor;
        }
      }
      @include for_media(mobileScreen) {
        width: 100%;
      }
      @include for_media(tabletScreen) {
        width: 100% !important;
      }
      @include for_media(bigTabletScreen) {
        width: 100% !important;
      }

      h4 {
        margin: 1em 0;
        font-size: 2.4em;
      }

      .formField {
        width: 100%;
        height: 3.2em;
        border-radius: 7px;
        border: 1px solid $grayColor1 !important;
        text-indent: 10px;
        background-color: $white !important;
        color: $black !important;
        &:focus {
          border-color: $accentColor1 !important;
        }
      }

      .formContainer {
        display: flex;
        justify-content: center;
        flex-direction: column;
        gap: 2em;
        .loginButton {
          width: 100% !important;
          background-color: $accentColor1 !important;
          color: $white !important;
          height: 2.5em;
          border-radius: 7px !important;
          font-family: $primaryFont !important;
          font-size: 1.3em !important;
        }
        .inputError {
          position: absolute;
          color: $errorColor;
          margin: 0.5em 0;
        }
      }
      .fieldWrapper {
        position: relative;
        width: 100%;
        margin: 0.5rem 0;
      }
      .userOptionsContainer {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-top: 1.5rem;
      }
      .forgetPassword {
        color: $black;
        display: flex;
        justify-content: flex-end;
        span {
          cursor: pointer;
        }
      }
      .forgetPassword span:hover {
        text-decoration: underline;
      }
      .errorMessage {
        text-align: center;
      }
      .messageContent {
        text-align: center;
        margin: 0;
      }
    }
  }
  .backToLogin {
    right: 0.6em;
    font-size: 1.3em;
    color: $primaryColor;
    position: relative;
    right: 0.7em;
    bottom: 2em;
    height: 0 !important;
    cursor: pointer;
    @include for_media(mobileScreen) {
      right: 0.6em;
      font-size: 1.3em;
      color: #1d3fa1;
      position: absolute;
      left: 0em;
      top: 7em;
      height: 0 !important;
      cursor: pointer;
    }
    @include for_media(bigTabletScreen) {
      bottom: 1em;
    }
  }
  .alignCenter {
    @include for_media(mobileScreen) {
      padding: 4em 1em !important;
    }
  }
  .backToLogin:hover {
    text-decoration: underline;
    color: $black;
  }
  .backToLogin i {
    text-decoration: none;
  }
  .pswdMsg {
    font-size: 0.9em;
    text-align: end;
  }
  .progressBar {
    margin: 0.5em 0 !important;
  }
  .formFieldWrapper {
    position: relative;
    .eyeIcon {
      position: absolute;
      right: 1em;
      top: 1em;
      cursor: pointer;
    }
  }
  .titleMsg {
    h4 {
      margin: 0.5em 0 !important;
    }
    p {
      margin-bottom: 1em;
      color: $primaryColor;
    }
    span {
      color: $primaryColor;
    }
  }
  .resendLink {
    margin: 0.6em 0;
    span {
      color: blue;
      margin-left: 5px;
      cursor: pointer;
      @include for_media(mobileScreen) {
        margin: 0 !important;
      }
    }
  }

  .otpContainer {
    input {
      text-indent: 0px !important;
      width: 3.2em !important;
      @include for_media(mobileScreen) {
        width: 2.7em !important;
      }
    }
  }
  .seperator {
    width: 1em;
  }
}

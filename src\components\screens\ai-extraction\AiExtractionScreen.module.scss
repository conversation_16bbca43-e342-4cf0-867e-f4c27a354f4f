@import '../../../assets/scss/main.scss';

.aiExtractionContainer {
  width: 100%;
  min-height: 100vh;
  min-height: 100dvh;
  overflow: hidden;
  background-color: #f8fafc;

  .contentWrapper {
    display: flex;
    justify-content: space-between;
    gap: 2em;
    margin: 0 auto;
    padding: 0 2em;
    max-width: 100%;
    height: inherit;
    max-height: 100vh;
    max-height: 100dvh;
    overflow: hidden;
    @include for_media(mobileScreen) {
      flex-direction: column;
      overflow-y: auto;
      height: 100%;
      max-height: 100vh;
      max-height: 100dvh;
    }
  }

  .leftContainer {
    display: flex;
    flex-direction: column;
    gap: 0.5em;
    width: 40%;
    max-width: 40%;
    min-width: 40%;
    margin: 1.5em 0;
    @include for_media(mobileScreen) {
      width: 100%;
      max-width: 100%;
      min-width: 100%;
    }
  }

  .rightContainer {
    width: 60%;
    max-width: 60%;
    min-width: 60%;
    max-height: inherit;
    overflow-y: auto;
    padding: 0;
    border-radius: 0.5em;
    position: relative;
    @include hide-scrollbar;
    @include for_media(mobileScreen) {
      width: 100%;
      max-width: 100%;
      min-width: 100%;
      height: 100%;
      max-height: none;
      overflow-y: visible;
    }
  }
}

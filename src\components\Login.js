import React, { useState, useEffect } from 'react';
import { Formik, Form, Field, ErrorMessage } from 'formik';
import * as Yup from 'yup';
import style from './scss/login.module.scss';
import loginImg from '../assets/Images/loginImg.png';
import { Button, Image, Message, Icon, Checkbox, Loader } from 'semantic-ui-react';
import { useNavigate } from 'react-router-dom';
import GlobalService from './services/GlobalServices';
import { resturls } from './utils/apiurls';
import { useAuth } from '../contexts/AuthContext';
import ls from 'local-storage';
import Header from './global/Header';

const Login = () => {
  const validationSchema = Yup.object({
    email: Yup.string()
      .matches(/^[\w.%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$/, 'Invalid email address')
      .required('Email is required'),
    password: Yup.string().required('Password is required'),
  });

  const [message, setMessage] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [rememberMe, setRememberMe] = useState(false);
  const [initialEmail, setInitialEmail] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const navigate = useNavigate();
  const { setIsAuthenticated, setRole, setUserInfo } = useAuth();

  useEffect(() => {
    const savedEmail = ls.get('rememberedEmail');
    if (savedEmail) {
      setRememberMe(true);
      setInitialEmail(savedEmail);
    }
  }, []);

  const handleSubmit = (values) => {
    setIsLoading(true);
    setMessage(false);

    const obj = {
      email: values.email,
      password: values.password,
    };

    GlobalService.generalSelect(
      (respdata) => {
        setIsLoading(false);
        const { data, msg, is_temporary_password } = respdata;
        if (is_temporary_password === true) {
          navigate('/forgetPassword/reset');
          return;
        }
        if (msg === 'success' && data?.user_id) {
          setIsAuthenticated(true);
          const userInfoObj = {
            data: data?.access_token,
            expiry: new Date().getTime() + 1209600000,
            role: data?.role,
            userId: data?.user_id,
          };
          setUserInfo(userInfoObj);
          setRole(data?.role);
          ls.set('userName', values.email);
          if (rememberMe) {
            ls.set('rememberedEmail', values.email);
          } else {
            ls.remove('rememberedEmail');
          }

          navigate('/');
        } else {
          setMessage('Invalid username or password');
        }
      },
      resturls.login,
      obj,
      'POST',
      () => {
        // Error callback
        setIsLoading(false);
        setMessage('An error occurred during login');
      }
    );
  };

  const formikInitialValues = {
    email: initialEmail,
    password: '',
  };

  const renderForm = () => (
    <Formik
      key={initialEmail}
      initialValues={formikInitialValues}
      validationSchema={validationSchema}
      onSubmit={handleSubmit}
    >
      {({ isValid, dirty, values, setFieldValue }) => (
        <Form className={style.formContainer}>
          <div className={style.fieldWrapper}>
            <Field
              type="email"
              name="email"
              placeholder="Email"
              className={style.formField}
              onChange={(e) => {
                setFieldValue('email', e.target.value);
                setMessage(false);
              }}
            />
            <ErrorMessage name="email" component="div" className={style.inputError} />
          </div>

          <div className={style.fieldWrapper}>
            <div className={style.formFieldWrapper}>
              <Field
                type={showPassword ? 'text' : 'password'}
                name="password"
                placeholder="Password"
                className={style.formField}
                onChange={(e) => {
                  setFieldValue('password', e.target.value);
                  setMessage(false);
                }}
              />
              <Icon
                name={!showPassword ? 'eye slash' : 'eye'}
                onClick={() => setShowPassword(!showPassword)}
                className={style.eyeIcon}
              />
            </div>
            <ErrorMessage name="password" component="div" className={style.inputError} />
          </div>

          <div className={style.userOptionsContainer}>
            <div className={style.rememberMe}>
              <Checkbox
                label="Remember Me"
                checked={rememberMe}
                onChange={() => {
                  setRememberMe(!rememberMe);
                }}
              />
            </div>

            <div className={style.forgetPassword}>
              <span onClick={() => navigate('/forgetPassword')}>Forgot password?</span>
            </div>
          </div>

          <Button className={style.loginButton} type="submit" disabled={!(isValid && dirty) || isLoading}>
            {isLoading ? <Loader active inline="centered" size="tiny" /> : 'Login'}
          </Button>
        </Form>
      )}
    </Formik>
  );

  return (
    <div className={style.mainContainer}>
      <Header withOutUser={true} />
      <div className={style.contentWrapper}>
        <div className={style.leftContainer}>
          <Image src={loginImg} size="massive" />
        </div>
        <div className={style.rightContainer}>
          {message && (
            <Message className={style.errorMessage} negative>
              {message}
            </Message>
          )}

          <div>
            <div className={style.titleMsg}>
              <h4>Login</h4>
              <p>Welcome! Please provide your login credentials to continue</p>
            </div>
            {renderForm()}
          </div>
        </div>
      </div>
    </div>
  );
};

export default Login;

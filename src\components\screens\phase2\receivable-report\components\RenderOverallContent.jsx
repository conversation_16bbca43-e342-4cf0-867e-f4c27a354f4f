import React from 'react';
import RenderDownloadModal from '../../../../utils/reportUtils/RenderDownloadModal';
import DetailedReportContent from './DetailedReportContent';
import ReportHeader from '../../../../global/components/ReportHeader';
import ReportSortFilter from '../../../../global/components/ReportSortFilter';

const RenderOverallContent = ({
  handleRefresh,
  receivableDetails,
  setDownloadModal,
  activeTab,
  isLoading,
  handlePagination,
  activePage,
  setActiveTab,
  handleDropdownList,
  selectedTimeline,
  handleSelectDropdown,
  handleApplySort,
  handleClear,
  openDropdown,
  setOpenDropdown,
  invoiceInfo,
  detailedLoading,
  searchTerm,
  handleSearch,
  customerDetails,
  downloadModal,
  handleDownload,
}) => {
  return (
    <>
      <ReportHeader
        title="Total Receivable"
        data={receivableDetails}
        handleRefresh={handleRefresh}
        setDownloadModal={setDownloadModal}
      />
      <DetailedReportContent
        activeTab={activeTab}
        isLoading={isLoading}
        receivableDetails={receivableDetails}
        handlePagination={handlePagination}
        activePage={activePage}
        setActiveTab={setActiveTab}
        handleDropdownList={handleDropdownList}
        content={
          <ReportSortFilter
            selectedTimeline={selectedTimeline}
            handleSelectDropdown={handleSelectDropdown}
            handleApplySort={handleApplySort}
            handleClear={handleClear}
          />
        }
        openDropdown={openDropdown}
        setOpenDropdown={setOpenDropdown}
        invoiceInfo={invoiceInfo}
        detailedLoading={detailedLoading}
        searchTerm={searchTerm}
        handleSearch={handleSearch}
        customerDetails={customerDetails}
      />
      <RenderDownloadModal
        isOpen={downloadModal}
        onClose={() => setDownloadModal(false)}
        content={`Your Receivable Report is prepared and ready for download. You can save it for further review.`}
        downloadFunction={handleDownload}
        checkBoxDisable={true}
      />
    </>
  );
};

export default RenderOverallContent;

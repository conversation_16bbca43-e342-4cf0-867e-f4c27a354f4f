import React, { useCallback } from 'react';
import style from './scss/horizontalScrollControls.module.scss';
import { LeftArrowCircle, RigthArrowCircle } from '../../assets/svgs';

const SCROLL_AMOUNT = 600;

const HorizontalScrollControls = ({ containerId }) => {
  const handleScroll = useCallback(
    (direction) => {
      const container = document.getElementById(containerId);
      if (!container) return;

      const scrollAmount = direction === 'left' ? -SCROLL_AMOUNT : SCROLL_AMOUNT;
      const targetScroll = container.scrollLeft + scrollAmount;

      container.scrollTo({
        left: targetScroll,
        behavior: 'smooth',
      });
    },
    [containerId]
  );

  return (
    <div className={style.scrollControls}>
      <div className={style.scrollButton} aria-label="Scroll left" onClick={() => handleScroll('left')}>
        <LeftArrowCircle />
      </div>
      <div className={style.scrollButton} aria-label="Scroll right" onClick={() => handleScroll('right')}>
        <RigthArrowCircle />
      </div>
    </div>
  );
};

export default React.memo(HorizontalScrollControls);

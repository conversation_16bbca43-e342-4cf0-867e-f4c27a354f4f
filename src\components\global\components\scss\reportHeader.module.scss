@import '../../../../assets/scss/main.scss';

.headerContent {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 0 1.5em 0;

  .leftContent {
    h4 {
      margin: 0;
      color: #181d27;
    }

    p {
      color: #717680;
      font-size: 1.1em !important;
    }
  }

  .rightContent {
    display: flex;
    gap: 2em;

    button {
      display: flex;
      align-items: center;
      height: 3.1em;
      gap: 1em;
      border-radius: 35px !important;
      cursor: pointer;
      -webkit-text-stroke: 0.25px;
      font-size: 1.2em !important;
    }

    .refreshBtn {
      padding: 0 2em;
      background-color: $white;
      color: $black;
      border: 1px solid #e9e9e9 !important;

      svg {
        height: 20px;
        width: 20px;
      }
    }

    .downloadBtn {
      background-color: #f6d659;
      color: #7e6607;

      svg {
        path {
          fill: #7e6607;
        }
      }
    }
  }
}

@include for_media(mobileScreen) {
  .headerContent {
    .downloadBtn {
      display: none !important;
    }

    .refreshBtn {
      width: 35px !important;
      height: 35px !important;
      padding: 0 !important;
      display: flex;
      justify-content: center;
      align-items: center;
      border-radius: 10px !important;
      background-color: $white;

      span {
        display: none;
      }
    }

    .leftContent {
      p {
        font-size: 1em !important;
      }
    }
  }
}

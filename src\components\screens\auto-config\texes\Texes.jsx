import React from 'react';
import { Form, Formik, useFormikContext } from 'formik';
import { Button, Typography } from '@mui/material';
import AutoConfigDropdown from '../AutoConfigDropdown';
import { useOutletContext } from 'react-router-dom';

const initialValues = {
  gst_ledger_mode: 'consolidated',
  cess_ledger: '',
};

const gst_ledger_mode_options = [
  {
    label: 'Consolidated',
    value: 'consolidated',
  },
  {
    label: 'Rate wise',
    value: 'rate_wise',
  },
];

const GST_LEDGERS = [0, 5, 12, 18, 28];

export default function Texes() {
  const { isZoho } = useOutletContext();
  const handleSubmit = (values) => {
    console.log(values);
  };

  return (
    <Formik initialValues={initialValues} onSubmit={handleSubmit}>
      {({ values, setFieldValue }) => (
        <Form className="flex flex-col gap-4 px-5">
          <Typography variant="h6" className="mb-4">
            Tax Configuration
          </Typography>

          <AutoConfigDropdown
            label="GST Ledger Mode (for Purchases)"
            onSelect={(value) => setFieldValue('gst_ledger_mode', value?.value ?? '')}
            value={values.gst_ledger_mode}
            optionLabel="label"
            optionValue="value"
            options={gst_ledger_mode_options}
            size="small"
          />
          {!isZoho && (
            <AutoConfigDropdown
              label="Cess Ledger"
              onSelect={(value) => setFieldValue('cess_ledger', value?.uuid_id ?? '')}
              value={values.cess_ledger}
              size="small"
            />
          )}

          <div className={`grid gap-4 ${isZoho ? 'grid-cols-2' : 'grid-cols-3'}`}>
            {isZoho ? <ZohoTaxes /> : <TallyTaxes />}
          </div>

          <Button variant="contained" className="!bg-accent2" type="submit">
            Save
          </Button>
        </Form>
      )}
    </Formik>
  );
}

function TallyTaxes() {
  const { values, setFieldValue } = useFormikContext();
  
  const isConsolidated = values.gst_ledger_mode === 'consolidated';
  
  const handleConsolidatedChange = (gstType, value, gstRate) => {
    if (isConsolidated && gstRate === 0) {
      // When in consolidated mode and 0% rate is changed, update all other rates
      GST_LEDGERS.forEach(rate => {
        setFieldValue(`${gstType}_ledger_${rate}`, value?.uuid_id ?? '');
      });
    } else {
      // Normal field update
      setFieldValue(`${gstType}_ledger_${gstRate}`, value?.uuid_id ?? '');
    }
  };

  return (
    <>
      {/* IGST */}
      <div className="flex flex-col gap-2">
        {GST_LEDGERS.map((gst) => (
          <AutoConfigDropdown
            key={`igst_${gst}`}
            label={`IGST ${gst}%`}
            onSelect={(value) => handleConsolidatedChange('igst', value, gst)}
            value={values[`igst_ledger_${gst}`]}
            size="small"
            disabled={isConsolidated && gst !== 0}
          />
        ))}
      </div>

      {/* CGST */}
      <div className="flex flex-col gap-2">
        {GST_LEDGERS.map((gst) => (
          <AutoConfigDropdown
            key={`cgst_${gst}`}
            label={`CGST ${gst}%`}
            onSelect={(value) => handleConsolidatedChange('cgst', value, gst)}
            value={values[`cgst_ledger_${gst}`]}
            size="small"
            disabled={isConsolidated && gst !== 0}
          />
        ))}
      </div>

      <div className="flex flex-col gap-2">
        {/* SGST */}
        {GST_LEDGERS.map((gst) => (
          <AutoConfigDropdown
            key={`sgst_${gst}`}
            label={`SGST ${gst}%`}
            onSelect={(value) => handleConsolidatedChange('sgst', value, gst)}
            value={values[`sgst_ledger_${gst}`]}
            size="small"
            disabled={isConsolidated && gst !== 0}
          />
        ))}
      </div>
    </>
  );
}

function ZohoTaxes() {
  const { values, setFieldValue } = useFormikContext();
  return (
    <>
      {/* Inter */}
      <div className="flex flex-col gap-2">
        {GST_LEDGERS.map((gst) => (
          <AutoConfigDropdown
            label={`Inter State ${gst}%`}
            onSelect={(value) => setFieldValue(`inter_state_ledger_${gst}`, value?.uuid_id ?? '')}
            value={values[`inter_state_ledger_${gst}`]}
            size="small"
          />
        ))}
      </div>

      {/* Intra */}
      <div className="flex flex-col gap-2">
        {GST_LEDGERS.map((gst) => (
          <AutoConfigDropdown
            label={`Intra State ${gst}%`}
            onSelect={(value) => setFieldValue(`intra_state_ledger_${gst}`, value?.uuid_id ?? '')}
            value={values[`intra_state_ledger_${gst}`]}
            size="small"
          />
        ))}
      </div>
    </>
  );
}

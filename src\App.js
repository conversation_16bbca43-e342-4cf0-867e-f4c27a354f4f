import React from 'react';
import './assets/scss/main.scss';
import { BrowserRouter as Router, Route, Routes } from 'react-router-dom';
import { AuthProvider } from './contexts/AuthContext';
import { ToastContainer } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import { Tooltip } from 'react-tooltip';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import RoutesWithLayout from './routes/RoutesWithLayout';
import RoutesWithoutLayout from './routes/RoutesWithoutLayout';
import PublicRoutes from './routes/PublicRoutes';
import { SuspenseWrapper } from './components/utils/JSXUtils';
const Error404 = React.lazy(() => import('./components/error-pages/Error404'));

const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: false,
      refetchOnWindowFocus: false,
    },
  },
});

const App = () => {
  return (
    <>
      <QueryClientProvider client={queryClient}>
        <Router>
          <AuthProvider>
            <RoutesWrapper />
          </AuthProvider>
        </Router>
      </QueryClientProvider>
      <ToastContainer position="top-right" autoClose={2000} hideProgressBar={true} closeButton={true} />
      <Tooltip id="tooltip" />
      <div id="date-picker-portal" />
    </>
  );
};

const RoutesWrapper = () => {
  return (
    <Routes>
      {/* Public routes */}
      {PublicRoutes()}

      {/* Protected routes without layout */}
      {RoutesWithoutLayout()}

      {/* Protected routes with layout */}
      {RoutesWithLayout()}

      {/* Catch-all route */}
      <Route
        path="*"
        element={
          <SuspenseWrapper>
            <Error404 />
          </SuspenseWrapper>
        }
      />
    </Routes>
  );
};

export default App;

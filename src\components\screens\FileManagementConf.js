import React, { useEffect, useState, useCallback } from 'react';
import { useNavigate } from 'react-router-dom';
import style from './scss/businessList.module.scss';
import NavigationBar from './NavigationBar';
import Header from '../global/Header';
import { useAuth } from '../../contexts/AuthContext';
// import Avatar from 'react-avatar';
// import ls from "local-storage";
// import { processLogout } from '../utils';
import { Image, Table, Card, Pagination, Loader, Input, Dropdown, Popup, Button } from 'semantic-ui-react';
import GlobalService from '../services/GlobalServices';
import { resturls } from '../utils/apiurls';
import { mediaBreakpoint } from '../global/MediaBreakPointes';
import { downloadIcon, dropdownIcon, DropdownIcon, mailIcon, phoneIcon } from '../global/Icons';
import { useFormik } from 'formik';
import * as Yup from 'yup';
import debounce from 'lodash/debounce';
import staticImage from '../../assets/Images/sampleImg.png';
import pdfIcon from '../../assets/Images/pdfIcon.png';
import xls from '../../assets/Images/xls.png';
import doc from '../../assets/Images/doc.png';

const FileManagementAdmin = () => {
  // const navigate = useNavigate();
  // const { TicketList } = useAuth();
  const [TicketList, setTicketList] = useState();
  // const [BuisnessSuperUserList, setBuisnessSuperUserList] = useState([]);
  const [BuisnesCount, setBuisnessCount] = useState(0);
  const [filesList, setFilesList] = useState([]);
  const [paginationInfo, setPaginationInfo] = useState();
  const [activePage, setActivePage] = useState(1);
  const [isLoading, setIsLoading] = useState(1);
  const [businessList, setBusinessList] = useState(0);
  const [categoryList, setCategoryList] = useState([]);
  const [enableDate, setEnableDate] = useState(false);
  const [downloadLink, setDownloadLink] = useState('');
  const [logDowloadLoad, setlogDowloadLoad] = useState(false);
  const [nextPageOrg, setnextPageOrg] = useState(1);
  const [nextPage, setNextPage] = useState(null);

  const [filters, setFilters] = useState({
    searchText: '',
    selectedPeople: '',
    selectedOrganisation: '',
    selectedCategory: '',
    selectedDate: { start: '', end: '' },
  });
  const [userList, setUserList] = useState([]);

  const isResponsive = mediaBreakpoint?.mobile > window.innerWidth;
  // const navigate = useNavigate();

  // const OrganisationSearch = (isActiveStatus) => {
  //   GlobalService.generalSelect(
  //     (respdata) => {
  //       const { results } = respdata;
  //       setTicketList([]);
  //       setBuisnessCount(respdata.count); // Update the business count
  //       setTicketList(results); // Update the business list
  //     },
  //     `${resturls.getBusinesses}?business_name=${isActiveStatus}`,
  //     {},
  //     "GET"
  //   );
  // };

  const obtainUsersList = () => {
    GlobalService.generalSelect(
      (respdata) => {
        if (respdata && respdata.data) {
          const { data } = respdata;
          const list = data?.map((user) => ({
            key: user.user_id,
            value: user.user_id,
            text: user.full_name,
            role: user?.role,
          }));
          const updatedList = [{ key: 'all_users', value: '', text: 'All users' }, ...list];
          setUserList(updatedList);
        }
      },
      `${resturls.obtainCategoryWiseUser}?user_type=all`,
      {},
      'GET'
    );
  };

  const [isFetchingCategories, setIsFetchingCategories] = useState(false);
  const [isFetchingBusinesses, setIsFetchingBusinesses] = useState(false);

  const obtainCategoryList = (page = 1) => {
    setIsFetchingCategories(true);
    const url = `${resturls.obtainCategortList}?page=${page}`;

    GlobalService.generalSelect(
      (respdata) => {
        if (respdata && respdata.results) {
          const { results, next } = respdata;
          const all = { key: 'all-category', value: '', text: 'All category' };
          const list = results.map((category) => ({
            key: category.id,
            value: category.name,
            text: category.name,
          }));

          const updatedList = page === 1 ? [all, ...list] : [...categoryList, ...list];
          setCategoryList(updatedList);

          if (next) {
            setNextPage(page + 1);
          } else {
            setNextPage(null);
          }

          setIsFetchingCategories(false);
        }
      },
      url,
      {},
      'GET'
    );
  };

  const obtainBusinessList = (page = 1) => {
    setIsFetchingBusinesses(true);
    const url = `${resturls.getBusinesses}?page=${page}`;

    GlobalService.generalSelect(
      (respdata) => {
        if (respdata && respdata.results) {
          const { results, next } = respdata;
          const optionList = results.map((info) => ({
            key: info?.business_id,
            value: info?.business_id,
            text: info?.business_name,
          }));

          const updatedList =
            page === 1
              ? [{ key: 'all_organisation', value: '', text: 'All organization' }, ...optionList]
              : [...businessList, ...optionList];

          setBusinessList(updatedList);

          if (next) {
            setnextPageOrg(page + 1);
          } else {
            setnextPageOrg(null);
          }

          setIsFetchingBusinesses(false);
        }
      },
      url,
      {},
      'GET'
    );
  };

  useEffect(() => {
    obtainUsersList();
    FetchFilesSpecificBuisness(1);
    obtainBusinessList(1);
    obtainCategoryList(1);
  }, []);

  const organisationList = () => {
    if (businessList?.length > 0) {
      return businessList;
    }
    return [];
  };

  const FetchFilesSpecificBuisness = () => {
    setIsLoading(true);
    GlobalService.generalSelect(
      (respdata) => {
        setFilesList(respdata.results);
        setPaginationInfo(respdata);
        setIsLoading(false);
      },
      `${resturls.file}`,
      'GET'
    );
  };

  //   const { userInfo } = useAuth();
  useEffect(() => {
    GlobalService.generalSelect(
      (respdata) => {
        const { results } = respdata;
        setBuisnessCount(respdata.count);
        setTicketList(results);
      },
      `${resturls.ticketList}`,
      {},
      'GET'
    );
  }, []);

  console.log('Buisness_count', BuisnesCount);

  //  useEffect(() => {
  //   GlobalService.generalSelect(
  //     (respdata) => {
  //       const { results } = respdata;
  //       setBuisnessSuperUserList(respdata.data);
  //     },
  //     `${resturls.obtainCategoryWiseUser}?user_type=business_superusers`,
  //     {},
  //     "GET"
  //   );
  // }, []);

  const orgIcon = () => (
    <svg width="16" height="15" viewBox="0 0 16 15" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        fill-rule="evenodd"
        clip-rule="evenodd"
        d="M2.26126 1.26126C2.78871 0.733816 3.50408 0.4375 4.25 0.4375H7.25C7.99592 0.4375 8.71129 0.733816 9.23874 1.26126C9.76618 1.78871 10.0625 2.50408 10.0625 3.25V13.1875H12.6875V11.2343C12.3518 11.1392 12.0427 10.9595 11.7916 10.7084C11.4048 10.3216 11.1875 9.79701 11.1875 9.25V7.75C11.1875 7.20299 11.4048 6.67839 11.7916 6.29159C12.1784 5.9048 12.703 5.6875 13.25 5.6875C13.797 5.6875 14.3216 5.9048 14.7084 6.29159C15.0952 6.67839 15.3125 7.20299 15.3125 7.75V9.25C15.3125 9.79701 15.0952 10.3216 14.7084 10.7084C14.4573 10.9595 14.1482 11.1392 13.8125 11.2343V13.1875H14.75C15.0607 13.1875 15.3125 13.4393 15.3125 13.75C15.3125 14.0607 15.0607 14.3125 14.75 14.3125H1.25C0.93934 14.3125 0.6875 14.0607 0.6875 13.75C0.6875 13.4393 0.93934 13.1875 1.25 13.1875H1.4375V3.25C1.4375 2.50408 1.73382 1.78871 2.26126 1.26126ZM2.5625 13.1875H5.1875V10.75C5.1875 10.4393 5.43934 10.1875 5.75 10.1875C6.06066 10.1875 6.3125 10.4393 6.3125 10.75V13.1875H8.9375V3.25C8.9375 2.80245 8.75971 2.37322 8.44324 2.05676C8.12678 1.74029 7.69755 1.5625 7.25 1.5625H4.25C3.80245 1.5625 3.37322 1.74029 3.05676 2.05676C2.74029 2.37323 2.5625 2.80245 2.5625 3.25V13.1875ZM4.4375 4.75C4.4375 4.43934 4.68934 4.1875 5 4.1875H6.5C6.81066 4.1875 7.0625 4.43934 7.0625 4.75C7.0625 5.06066 6.81066 5.3125 6.5 5.3125H5C4.68934 5.3125 4.4375 5.06066 4.4375 4.75ZM13.25 6.8125C13.0014 6.8125 12.7629 6.91127 12.5871 7.08709C12.4113 7.2629 12.3125 7.50136 12.3125 7.75V9.25C12.3125 9.49864 12.4113 9.7371 12.5871 9.91291C12.7629 10.0887 13.0014 10.1875 13.25 10.1875C13.4986 10.1875 13.7371 10.0887 13.9129 9.91291C14.0887 9.7371 14.1875 9.49864 14.1875 9.25V7.75C14.1875 7.50136 14.0887 7.2629 13.9129 7.08709C13.7371 6.91127 13.4986 6.8125 13.25 6.8125ZM4.4375 7.75C4.4375 7.43934 4.68934 7.1875 5 7.1875H6.5C6.81066 7.1875 7.0625 7.43934 7.0625 7.75C7.0625 8.06066 6.81066 8.3125 6.5 8.3125H5C4.68934 8.3125 4.4375 8.06066 4.4375 7.75Z"
        fill="#717680"
      />
    </svg>
  );

  const renderLogo = (logo) => {
    if (logo) {
      return <Image src={logo} />;
    }
    return orgIcon();
  };

  const formatDate = (isoDateString) => {
    const date = new Date(isoDateString);
    return new Intl.DateTimeFormat('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    }).format(date);
  };

  const renderList = () => {
    const itemsPerPage = 10;
    const totalPages = Math.ceil(paginationInfo?.count / itemsPerPage);
    const handlePaginationChange = (e, { activePage }) => {
      setActivePage(activePage);
      const queryParams = new URLSearchParams();
      if (filters.searchText?.length > 0) queryParams.append('search_query', filters.searchText);
      if (filters.selectedPeople?.length > 0) queryParams.append('uploaded_by', filters.selectedPeople);
      if (filters.selectedOrganisation?.length > 0) queryParams.append('business_id', filters.selectedOrganisation);
      if (filters.selectedCategory?.length > 0) queryParams.append('category_name', filters.selectedCategory);

      // Adding the date range filter to query params
      const hasStartDate = filters.selectedDate.start;
      const hasEndDate = filters.selectedDate.end;

      if (hasStartDate && hasEndDate) {
        queryParams.append('from_date', formatDateToDMY(filters.selectedDate.start));
        queryParams.append('to_date', formatDateToDMY(filters.selectedDate.end));
      } else if (hasStartDate || hasEndDate) {
        // If only one date is selected, skip the API call
        return filters;
      }
      const selected = paginationInfo?.next || paginationInfo?.previous;
      const bisnsId = selected?.split('api/get-tickets/')[1]?.split('/')[0]?.split('?')[0];
      const endpoint = bisnsId
        ? `${resturls.getTickets}${bisnsId}?page=${activePage}`
        : `${resturls.file}?page=${activePage}`;
      setIsLoading(true);
      GlobalService.generalSelect(
        (respdata) => {
          console.log(respdata, 'handlePaginationChange');
          if (respdata && respdata.results) {
            setFilesList(respdata.results);
            setPaginationInfo(respdata);
            setIsLoading(false);
          } else {
            console.warn('No results found in response:', respdata);
            setFilesList([]);
          }
        },
        `${endpoint}&${queryParams}`,
        {},
        'GET'
      );
    };

    if (filesList?.length === 0) {
      return (
        <div className={style.noMatchMsg}>
          <p>No matches found</p>
        </div>
      );
    }
    return (
      <>
        <div className={style.tableWrapper}>
          <Table basic="very">
            <Table.Header>
              <Table.Row>
                {/* <Table.HeaderCell><div className='customCheckBox '><Checkbox indeterminate className={`${style.checkbox}`}/></div></Table.HeaderCell> */}
                <Table.HeaderCell className={style.subjectheaderRow}>File Name</Table.HeaderCell>
                <Table.HeaderCell className={style.subjectheaderRow}>Ticket ID</Table.HeaderCell>
                <Table.HeaderCell className={style.subjectheaderRow}>Category</Table.HeaderCell>
                <Table.HeaderCell className={style.subjectheaderRow}>Organization</Table.HeaderCell>
                <Table.HeaderCell className={style.subjectheaderRow}>Uploaded by</Table.HeaderCell>
                <Table.HeaderCell className={style.subjectheaderRow}>Uploaded on</Table.HeaderCell>
              </Table.Row>
            </Table.Header>
            <Table.Body>
              {filesList?.map((data) => (
                <Table.Row>
                  {/* <Table.Cell><div className='customCheckBox'><Checkbox className={`${style.checkbox}`} /></div></Table.Cell> */}
                  <Table.Cell>
                    <div className={style.fileInfo}>
                      <div className={style.leftImage}>
                        {data?.file &&
                        (data.file.endsWith('.pdf') ||
                          data.file.endsWith('.xls') ||
                          data.file.endsWith('.xlsx') ||
                          data.file.endsWith('.doc')) ? (
                          <a href={data.file}>
                            {data.file.endsWith('.pdf') ? (
                              <Image src={pdfIcon} alt="PDF Icon" />
                            ) : data.file.endsWith('.xls') || data.file.endsWith('.xlsx') ? (
                              <Image src={xls} alt="XLS Icon" />
                            ) : (
                              <Image src={doc} alt="DOC Icon" />
                            )}
                          </a>
                        ) : (
                          // Render static image if no recognized file type
                          <a href={data.file} target="_blank" rel="noreferrer">
                            <Image src={data?.file || staticImage} alt="File" />
                          </a>
                        )}
                      </div>
                      <div className={style.businessName}>
                        <a href={data.file} target="_blank" rel="noreferrer">
                          <p>{data?.file ? data.file.split('/').pop() : 'No File'}</p>
                        </a>
                      </div>
                    </div>
                  </Table.Cell>
                  <Table.Cell>{data?.ticket_id ? `${data?.ticket_id}` : '-'}</Table.Cell>
                  <Table.Cell>{data?.category?.name || '-'}</Table.Cell>
                  <Table.Cell>
                    <div className={style.businessInfo}>
                      <div className={style.logo}>{renderLogo(data?.business_image)}</div>
                      {data?.business?.business_name || data?.business_name || '-'}
                    </div>
                  </Table.Cell>
                  <Table.Cell>
                    {data?.uploaded_by ? (
                      <>
                        <p>{data?.uploaded_by?.full_name}</p>
                        {data?.uploaded_by?.role}
                      </>
                    ) : (
                      '-'
                    )}
                  </Table.Cell>
                  <Table.Cell>{formatDate(data?.updated_at) || '-'}</Table.Cell>
                </Table.Row>
              ))}
            </Table.Body>
          </Table>
        </div>
        <div className={style.paginationWrapper2}>
          {paginationInfo?.count > 10 && filesList?.length > 0 && (
            <Pagination activePage={activePage} totalPages={totalPages} onPageChange={handlePaginationChange} />
          )}
        </div>
      </>
    );
  };
  // const Superuseroptions = BuisnessSuperUserList.map(user => ({
  //   key: user.user_id,
  //   value: user.user_id,
  //   text: user.full_name
  // }));

  const renderCardList = () => {
    return (
      <div className={style.tableWrapper}>
        <div className={style.ticketList}>
          {TicketList?.map((data) => (
            <Card className={`${style.ticketCard}`}>
              <Card.Content className={style.ticketContent}>
                <div className={style.rightContent}>
                  <h5>{data?.business_name}</h5>
                  <p>{data?.business_id}</p>
                  {data?.accountant_name && (
                    <p>
                      {data?.accountant_name || '-'}
                      {`  (Accountant)`}
                    </p>
                  )}
                </div>
                <div className={style.leftContent}>{renderLogo(data?.business_image)}</div>
              </Card.Content>
            </Card>
          ))}
        </div>
      </div>
    );
  };

  const formatDateToDMY = (date) => {
    const d = new Date(date);
    const day = String(d.getDate()).padStart(2, '0');
    const month = String(d.getMonth() + 1).padStart(2, '0'); // Months are 0-based
    const year = String(d.getFullYear()).slice(-2);
    return `${day}-${month}-${year}`;
  };

  const obtainLogDownloadLink = (queryParams = '') => {
    // Parse queryParams if it's a string
    const urlParams = new URLSearchParams(queryParams);

    // Check for 'uploaded_by' and rename it to 'user_id'
    if (urlParams.has('uploaded_by')) {
      const value = urlParams.get('uploaded_by');
      urlParams.delete('uploaded_by'); // Remove the old key
      urlParams.set('user_id', value); // Add the new key with the same value
    }

    setlogDowloadLoad(true);

    GlobalService.generalSelect(
      (respdata) => {
        const { download_link } = respdata;
        console.log(respdata);
        setDownloadLink(download_link);
        setlogDowloadLoad(false);
      },
      `${resturls.filesDownload}?${urlParams.toString()}`,
      {},
      'GET'
    );
  };

  // useEffect(() => {
  //   obtainLogDownloadLink();
  // }, [])

  const handleFilterChange = (key, value) => {
    console.log(key, value, 'key, value');
    setFilters((prev) => {
      const updatedFilters = { ...prev, [key]: value };
      const queryParams = new URLSearchParams();

      if (updatedFilters.searchText) queryParams.append('search_query', updatedFilters.searchText);
      if (updatedFilters.selectedPeople) queryParams.append('uploaded_by', updatedFilters.selectedPeople);
      if (updatedFilters.selectedOrganisation) queryParams.append('business_id', updatedFilters.selectedOrganisation);
      if (updatedFilters.selectedCategory) queryParams.append('category_name', updatedFilters.selectedCategory);

      // Adding the date range filter to query params
      const hasStartDate = updatedFilters.selectedDate.start;
      const hasEndDate = updatedFilters.selectedDate.end;

      if (hasStartDate && hasEndDate) {
        queryParams.append('from_date', formatDateToDMY(updatedFilters.selectedDate.start));
        queryParams.append('to_date', formatDateToDMY(updatedFilters.selectedDate.end));
      } else if (hasStartDate || hasEndDate) {
        // If only one date is selected, skip the API call
        console.warn("Both 'from_date' and 'to_date' must be selected to apply the date filter.");
        return updatedFilters;
      }

      // Proceed with the API call if valid
      setIsLoading(true);
      GlobalService.generalSelect(
        (respdata) => {
          const { results } = respdata;
          setFilesList(results); // Update the file list
          setPaginationInfo(respdata);
          setIsLoading(false);
          obtainLogDownloadLink(queryParams);
        },
        `${resturls.fileFilter}/?${queryParams.toString()}`,
        {},
        'GET'
      );

      return updatedFilters;
    });
  };

  const handleSearch = useCallback(
    debounce((query) => {
      handleFilterChange('searchText', query);
    }, 0),
    []
  );

  const handleInputChange = (e) => {
    const query = e.target.value;
    handleSearch(query);
  };

  const handleClearDate = () => {
    setFilters({ ...filesList, selectedDate: { start: '', end: '' } });
    FetchFilesSpecificBuisness();
  };

  const renderDateField = () => {
    const startDate = filters?.selectedDate?.start?.length > 0;
    const endDate = filters?.selectedDate?.end?.length > 0;
    const enabled = startDate || endDate;
    return (
      <div className={style.dateInputWrapper}>
        <div className={style.inputWrapper}>
          <label>Start Date</label>
          <input
            type="date"
            placeholder="Start Date"
            value={filters.selectedDate.start}
            onChange={(e) => handleFilterChange('selectedDate', { ...filters.selectedDate, start: e.target.value })}
          />
        </div>
        <div className={style.inputWrapper}>
          <label>End Date</label>
          <input
            type="date"
            placeholder="End Date"
            value={filters.selectedDate.end}
            onChange={(e) => handleFilterChange('selectedDate', { ...filters.selectedDate, end: e.target.value })}
          />
        </div>
        {enabled && (
          <Button className={style.clearBtn} onClick={handleClearDate}>
            Clear
          </Button>
        )}
      </div>
    );
  };

  const renderSelectedDate = () => {
    const startDate = filters?.selectedDate?.start;
    const endDate = filters?.selectedDate?.end;
    if (startDate?.length !== 0 && endDate?.length !== 0) {
      return (
        <div className={style.selectedDateContent}>
          {formatDateToDMY(startDate)} <span>to</span> {formatDateToDMY(endDate)}
        </div>
      );
    }

    return 'Date';
  };

  return (
    <>
      <Header />
      <div className={style.bussinessListScreen}>
        <div className={style.navigationWrapper}>
          <NavigationBar disable />
        </div>
        <div className={style.rightContentWrapper}>
          <div className={style.headerPart}>
            <div>
              <h4 className={style.separate}>
                File Management <span className={style.span_orgCOunt}>{paginationInfo?.count || 0}</span>
              </h4>
            </div>
            {downloadLink && (
              <div className={style.btnWrapper}>
                <a href={downloadLink}>
                  <p className={style.downloadfileBtn}>
                    {logDowloadLoad ? (
                      <Loader inverted active inline="centered" size="small" />
                    ) : (
                      <>Dowload {downloadIcon()}</>
                    )}
                  </p>
                </a>
              </div>
            )}
          </div>
          <div className={style.searchWrapper}>
            <Input
              className={style.searchInput}
              icon="search"
              placeholder="Search by Ticket Id or Uploaded by or business name"
              iconPosition="left"
              value={filters?.searchText}
              onChange={handleInputChange}
            />
            <Dropdown
              placeholder="Organization"
              className={`customDropdown3 ${style.statusDropdown}`}
              icon={<DropdownIcon />}
              options={[
                ...organisationList(), // Include the organization list
                ...(nextPageOrg
                  ? [
                      {
                        key: 'load-more',
                        text: (
                          <Button
                            type="button"
                            onClick={() => obtainBusinessList(nextPageOrg)}
                            disabled={isFetchingBusinesses}
                            loading={isFetchingBusinesses}
                            className={style.loadMoreButton}
                          >
                            Load More
                          </Button>
                        ),
                        disabled: true, // Prevent actual selection
                      },
                    ]
                  : []),
              ]}
              onChange={(e, { value }) => handleFilterChange('selectedOrganisation', value)}
            />

            <Dropdown
              placeholder="People"
              className={`customDropdown3 ${style.statusDropdown}`}
              icon={<DropdownIcon />}
              options={userList}
              onChange={(e, { value }) => handleFilterChange('selectedPeople', value)}
            />
            <div
              onClickCapture={() => setEnableDate(true)}
              className={`customDropdown3 ${style.statusDropdown} ${style.dateDropdown}`}
            >
              <Popup
                className={style.datePopup}
                trigger={
                  <p className={style.dateInfo} onClickCapture={() => setEnableDate(true)}>
                    {renderSelectedDate()}
                  </p>
                }
                content={renderDateField()}
                position="top right"
                hoverable
                // open
                on="click"
              />
            </div>
            <Dropdown
              placeholder="Category"
              className={`customDropdown3 ${style.statusDropdown}`}
              icon={<DropdownIcon />}
              options={[
                ...categoryList,
                ...(nextPage
                  ? [
                      {
                        key: 'load-more',
                        text: (
                          <Button
                            type="button"
                            onClick={() => obtainCategoryList(nextPage)}
                            disabled={isFetchingCategories}
                            loading={isFetchingCategories}
                            className={style.loadMoreButton}
                          >
                            Load More
                          </Button>
                        ),
                        disabled: true, // Prevent actual selection
                      },
                    ]
                  : []),
              ]}
              onChange={(e, { value }) => handleFilterChange('selectedCategory', value)}
            />
          </div>
          <div>
            {isLoading ? (
              <div className={style.loaderContainer}>
                <Loader active inline="centered" size="medium" />
              </div>
            ) : isResponsive ? (
              renderCardList()
            ) : (
              renderList()
            )}
          </div>
        </div>
      </div>
    </>
  );
};

export default FileManagementAdmin;

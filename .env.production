# Base CDN Domain (please replace this with you new url)
BCDN_DOMAIN=app.omnisageai.com


# Application Domain
REACT_APP_DOMAIN=app.omnisageai.com


# Protocol used (https for secure connections)
REACT_APP_PROTOCOL=https


# Ports (if no specific ports are needed, keep these blank)
REACT_APP_PORT=
REACT_APP_REACTPORT=443


# API Context (leave blank if not applicable)
REACT_APP_APICONTEXT=


# Base REST API URL(please replace this with you new url)
REACT_APP_RESTBASEURL=https://api.omnisageai.com


# WebSocket URL (please replace this with you new url)
REACT_APP_WS_URL=api.omnisageai.com


# CDN URL for accessing resources(please replace this with you new url)
REACT_APP_CDNURL=https://api.omnisageai.com


# Domain for cookies(please replace this with you new url)
REACT_APP_COOKIEDOMAIN=api.omnisageai.com


# Application Secret Key (ensure this is securely managed)
REACT_APP_SECRET_KEY=G7eFz9b1WjP8TmQs3kX2Cvz5R!yB6oDp

REACT_APP_CREATION_BUSINESS_ID=ba239f70-df65-4532-8977-d00532443f3e
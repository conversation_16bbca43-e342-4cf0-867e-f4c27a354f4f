import React, { useCallback, useState } from 'react';
import { Dropdown } from 'semantic-ui-react';
import ticketServices from '../services/ticketServices';
import { getStatusClass } from '../utils';
import { toast } from 'react-toastify';
import style from './scss/ticketStatusChangeBtn.module.scss';
import { DropdownIcon } from '../../assets/svgs';
import { useAuth } from '../../contexts/AuthContext';

const statusOptions = [
  { key: 'open', value: 'Open', text: 'Open', className: 'openStatus' },
  {
    key: 'pending',
    value: 'Pending',
    text: 'Pending',
    className: 'pendingStatus',
  },
  {
    key: 'closed',
    value: 'Closed',
    text: 'Closed',
    className: 'closedStatus',
  },
  {
    key: 'verified',
    value: 'Verified',
    text: 'Verified',
    className: 'verifiedStatus',
  },
  {
    key: 'deleted',
    value: 'Deleted',
    text: 'Deleted',
    className: 'deletedStatus',
  },
];

function TicketStatusChangeBtn({ initialStatus, ticketId, onStatusChange = null }) {
  const { roleType } = useAuth();
  const [status, setStatus] = useState(initialStatus ?? null);
  const handleStatusChange = useCallback(
    (value) => {
      ticketServices
        .updateTicket(ticketId, { status: value })
        .then(() => {
          toast.success('Status updated successfully');
          onStatusChange && onStatusChange(ticketId, value);
        })
        .catch((err) => toast.error(err.message || 'Something went wrong'));
      setStatus(value);
    },
    [ticketId]
  );

  return roleType !== 'user' ? (
    <Dropdown
      placeholder="Status"
      options={statusOptions}
      onChange={(e, { value }) => handleStatusChange(value)}
      value={status}
      className={`${style.dropdown} customDropdown ${getStatusClass(status)}`}
      icon={<DropdownIcon />}
    />
  ) : (
    <p className={`${style.status} ${getStatusClass(status)}`}>{status}</p>
  );
}

export default React.memo(TicketStatusChangeBtn);

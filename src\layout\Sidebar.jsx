import React, { useState, useEffect, useRef } from 'react';
import {
  CloudUpload,
  Description,
  Build,
  Settings,
  Business,
  KeyboardArrowDown,
  KeyboardArrowUp,
} from '@mui/icons-material';
import { useNavigate, useLocation } from 'react-router-dom';
import SlicedText from '../components/generic-components/SlicedText';
import { useAuth } from '../contexts/AuthContext';

const menuItems = [
  {
    id: 'upload',
    label: 'Upload',
    icon: CloudUpload,
    description: 'Upload invoices',
    path: '/',
  },
  {
    id: 'invoices',
    label: 'Invoices',
    icon: Description,
    description: 'View and manage invoices',
    path: '/invoices',
  },
  {
    id: 'tools',
    label: 'Tools',
    icon: Build,
    description: 'Business tools and utilities',
    path: '/tools',
  },
  {
    id: 'config',
    label: 'Config',
    icon: Settings,
    description: 'Business Configurations',
    path: '/config',
  },
];

export default function Sidebar() {
  const navigate = useNavigate();
  const location = useLocation();
  const { globSelectedBusiness } = useAuth();
  const [expandedItem, setExpandedItem] = useState(null);
  const navRef = useRef(null);
  const expandedItemRef = useRef(null);

  // Auto expand parent item if a subOption path is directly accessed
  useEffect(() => {
    const currentPath = location.pathname;

    // Find if the current path matches any subOption
    menuItems.forEach((item) => {
      if (item.subOptions && item.subOptions.length > 0) {
        const matchingSubOption = item.subOptions.find((subOption) => currentPath === subOption.path);

        if (matchingSubOption) {
          setExpandedItem(item.id);
        }
      }
    });
  }, [location.pathname]);

  // Scroll expanded item into view
  useEffect(() => {
    if (expandedItem && expandedItemRef.current && navRef.current) {
      const navElement = navRef.current;
      const expandedElement = expandedItemRef.current;

      // Get positions
      const navRect = navElement.getBoundingClientRect();
      const expandedRect = expandedElement.getBoundingClientRect();

      // Check if the expanded element is not fully visible
      if (expandedRect.bottom > navRect.bottom) {
        // Calculate how much to scroll to make the expanded element visible with some extra padding
        const scrollOffset = expandedRect.bottom - navRect.bottom + 16;

        navElement.scrollBy({
          top: scrollOffset,
          behavior: 'smooth',
        });
      }
    }
  }, [expandedItem]);

  const handleItemClick = (item) => {
    if (item.subOptions && item.subOptions.length > 0) {
      setExpandedItem(expandedItem === item.id ? null : item.id);
    } else {
      navigate(item.path);
    }
  };

  const handleSubOptionClick = (path, e) => {
    e.stopPropagation();
    navigate(path);
  };

  return (
    <div className="w-[250px] max-w-[250px] min-w-[250px] bg-white border-r border-gray-200 flex flex-col h-full">
      {/* Selected Business Info */}
      <div className="p-4 mx-2 mt-2 mb-2 rounded-xl shadow-lg bg-white border border-gray-200 hover:shadow-xl transition-shadow flex-shrink-0">
        <SelectedBusinessInfo
          business_name={globSelectedBusiness?.business_name}
          business_address={globSelectedBusiness?.business_address}
          business_image={globSelectedBusiness?.business_image}
          IsBusinessSelected={!!globSelectedBusiness}
        />
      </div>

      {/* Navigation Menu */}
      <nav ref={navRef} className="flex-1 overflow-y-auto py-2 px-2 [scrollbar-gutter:stable] scroll-smooth">
        <ul className="space-y-1">
          {menuItems.map((item) => {
            const Icon = item.icon;
            const isActive =
              (location.pathname === '/' && item.path === '/') ||
              (item.path !== '/' && location.pathname.startsWith(item.path)) ||
              (item?.subOptions && item.subOptions.some((subOption) => location.pathname === subOption.path));

            const hasSubOptions = item.subOptions && item.subOptions.length > 0;
            const isExpanded = expandedItem === item.id;

            return (
              <li key={item.id} ref={isExpanded ? expandedItemRef : null}>
                <div className="flex flex-col">
                  <button
                    onClick={() => handleItemClick(item)}
                    className={`w-full flex items-center space-x-3 px-3 py-2 rounded-lg text-left transition-all duration-200 select-none border-l-3 ${
                      isActive
                        ? 'bg-accent2 text-white border-l-accent2 shadow-sm'
                        : 'text-gray-700 hover:bg-gray-100 hover:text-gray-900 border-l-transparent'
                    }`}
                  >
                    <Icon className={`w-5 h-5 ${isActive ? 'text-white' : 'text-gray-400'}`} />
                    <div className="flex-1">
                      <p className={`font-bold text-base mb-0 ${isActive ? 'text-white' : 'text-gray-800'}`}>
                        {item.label}
                      </p>
                      <p
                        className={`text-[0.9em] text-nowrap ${isActive ? 'text-white text-opacity-80' : 'text-gray-500'}`}
                      >
                        {item.description}
                      </p>
                    </div>
                    {hasSubOptions &&
                      (isExpanded ? (
                        <KeyboardArrowUp className={`w-5 h-5 ${isActive ? 'text-white' : 'text-gray-400'}`} />
                      ) : (
                        <KeyboardArrowDown className={`w-5 h-5 ${isActive ? 'text-white' : 'text-gray-400'}`} />
                      ))}
                  </button>

                  {/* SubOptions */}
                  {hasSubOptions && isExpanded && (
                    <div className="ml-8 mt-2 space-y-1">
                      {item.subOptions.map((subOption) => {
                        const isSubActive = location.pathname === subOption.path;

                        return (
                          <button
                            key={subOption.id}
                            onClick={(e) => handleSubOptionClick(subOption.path, e)}
                            className={`w-full flex items-center space-x-2 px-3 py-2 rounded-lg text-left transition-all duration-200 select-none border-l-2 ${
                              isSubActive
                                ? 'bg-gray-100 text-accent2 border-l-accent2'
                                : 'text-gray-700 hover:bg-gray-50 hover:text-gray-900 border-l-transparent'
                            }`}
                          >
                            <div className="flex-1">
                              <p
                                className={`font-medium text-sm mb-0 ${isSubActive ? 'text-accent2' : 'text-gray-800'}`}
                              >
                                {subOption.label}
                              </p>
                              <p
                                className={`text-xs ${isSubActive ? 'text-accent2 text-opacity-80' : 'text-gray-500'}`}
                              >
                                {subOption.description}
                              </p>
                            </div>
                          </button>
                        );
                      })}
                    </div>
                  )}
                </div>
              </li>
            );
          })}
        </ul>
      </nav>
    </div>
  );
}

function SelectedBusinessInfo({ IsBusinessSelected, business_name, business_address, business_image }) {
  if (!IsBusinessSelected) {
    return (
      <div className="text-center py-1">
        <div className="w-10 h-10 mx-auto mb-2 rounded bg-gray-200 flex items-center justify-center">
          <Business className="w-5 h-5 text-gray-400" />
        </div>
        <div className="text-sm font-medium text-gray-600 mb-1">No business selected</div>
        <div className="text-xs text-gray-500">Please select a business to continue</div>
      </div>
    );
  }

  return (
    <div className="flex items-center space-x-3">
      <div className="w-10 h-10 rounded-lg bg-gray-200 flex items-center justify-center flex-shrink-0">
        {business_image ? (
          <img src={business_image} alt="Business Logo" className="w-8 h-8 rounded object-cover" />
        ) : (
          <Business className="w-6 h-6 text-gray-500" />
        )}
      </div>
      <div className="flex-1 min-w-0">
        <div className="text-sm font-semibold text-gray-900 truncate">
          <SlicedText text={business_name} sliceTill={20} />
        </div>
        {business_address && <div className="text-xs text-gray-600 mt-0.5">{business_address}</div>}
      </div>
    </div>
  );
}

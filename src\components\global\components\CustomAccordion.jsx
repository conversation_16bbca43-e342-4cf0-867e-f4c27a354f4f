import React, { useState } from 'react';
import { Accordion } from 'semantic-ui-react';
import style from './scss/customAccordion.module.scss';
import { DropdownIcon } from '../../../assets/svgs';
import { formatAmount, formatDateDayTypes } from '../../utils/dateUtils';

const CustomAccordion = ({ data = [] }) => {
  const [activeIndex, setActiveIndex] = useState(0);

  const handleClick = (index) => {
    // const { index } = titleProps;
    const newIndex = activeIndex === index ? -1 : index;
    setActiveIndex(newIndex);
  };

  const getDateColor = (dateString) => {
    const formattedDate = formatDateDayTypes(dateString);

    if (formattedDate === 'Today') return '#F79009';
    if (formattedDate === 'Yesterday' || formattedDate.includes('days ago')) return '#F04438';
    const date = new Date(dateString);
    const today = new Date();
    // Reset time for an accurate day difference calculation
    today.setHours(0, 0, 0, 0);
    date.setHours(0, 0, 0, 0);

    const diffTime = today - date;
    const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24));
    if (diffDays > 30) return '#F04438';
    return '#4E5BA6';
  };

  return (
    <Accordion className={style.customAccordion}>
      {data?.map((item, idx) => (
        <div key={`item${item?.ledger_name}invoice-${idx}`}>
          <Accordion.Title active={activeIndex === idx} index={idx} className={`${style.accordionTitle}`}>
            <p className={`${style.accordionName}`}>{item.ledger_name}</p> <p>{formatAmount(item.amount)}</p>
          </Accordion.Title>
          <div className={`${activeIndex === idx ? style.activeAccordion : ''} ${style.accordContentWrapper}`}>
            {item.invoices.data.length > 0 && (
              <p className={style.invoice} onClick={() => handleClick(idx)}>
                <span>{item.invoices.data?.length} Invoice </span>{' '}
                <span>
                  <DropdownIcon />
                </span>
              </p>
            )}
            <Accordion.Content className={style.accordContent}>
              {item.invoices.data.length > 0 ? (
                <div className={style.listContainer}>
                  {item.invoices.data.map((invoice, i) => (
                    <div key={`selected${i}-${invoice.invoice_numbe}`} className={style.listItem}>
                      <p>{invoice.invoice_number}</p>
                      <p style={{ color: getDateColor(invoice.due_date) }}>
                        {invoice.due_date && formatDateDayTypes(invoice.due_date)}
                      </p>
                      <p className={style.amount}>{formatAmount(invoice.amount)}</p>
                    </div>
                  ))}
                </div>
              ) : (
                <p>No invoices available</p>
              )}
            </Accordion.Content>
          </div>
        </div>
      ))}
    </Accordion>
  );
};

export default CustomAccordion;

@import '../../../../../assets/scss/main.scss';

.commentInputWrapper {
  width: 100%;
  padding-top: 0.1em !important;
  padding-bottom: 2em;
  background-color: $white !important;
  position: relative;

  @include for_media(mobileScreen) {
    padding: 0.2em 1.5em 2em 1.5em;
  }
  .previewContainer {
    max-height: 200px;
    overflow-y: auto;
    border: 2px solid $borderColor;
    border-radius: 15px;
    padding: 0.5em;
    padding: 0 1em;
  }

  .commentInputContainer {
    display: flex;
    width: 100%;
    justify-content: space-between;
    align-items: center;
    padding: 0.5em 1em;
    background-color: #f5f5f5;
    border-radius: 15px;
    box-shadow: 0 0 1px 2px $borderColor;
    @include for_media(mobileScreen) {
      padding: 0 1em;
    }
    svg {
      width: 25px;
      height: 25px;
      flex-shrink: 0 !important;
    }
    textarea {
      flex-grow: 1;
      border: none;
      background-color: transparent;
      font-size: 1.1rem;
      width: 100%;
      resize: none;
      overflow: hidden;
      max-height: 150px;
      overflow-y: auto;
      padding: 1em 1em;
      @include for_media(mobileScreen) {
        min-height: 70px;
      }
      &:focus {
        outline: none;
        border: none;
      }
    }
    .sendIconSvg {
      cursor: pointer;
      width: 40px !important;
      height: 40px !important;
      flex-shrink: 0 !important;
    }
  }
}

.commentTypeContainer {
  display: flex;
  align-items: center;
  gap: 0.5em;
  margin: 1em;
  -webkit-user-select: none;
  user-select: none;
  span {
    white-space: nowrap;
    font-size: 1.1em;
    color: $grayColor1;
    @include for_media(mobileScreen) {
      font-size: 0.7em;
    }
  }
  p {
    height: 2.5em;
    padding: 0 1em;
    border: 3px solid $accentBorder1;
    background-color: $accentBgColor1;
    border-radius: 35px;
    font-size: 1.1em;
    margin: 0 !important;
    @include flex-center;
    @include clickable;
    @include for_media(mobileScreen) {
      height: 2em;
      font-size: 1em;
    }
  }
  .activeType {
    background-color: $accentColor2;
    border-radius: 35px;
    color: $white;
  }
}

import { INDIAN_STATES } from './constants';

export const getIndianStateFromGstNumber = (gstNumber) => {
  if (!gstNumber || typeof gstNumber !== 'string') return '';
  const cleanGst = gstNumber.trim().toUpperCase();
  if (cleanGst.length < 2) return '';
  const stateCode = cleanGst.substring(0, 2);
  const indianState = INDIAN_STATES.find((state) => state.code_num === stateCode);
  return indianState?.code || '';
};

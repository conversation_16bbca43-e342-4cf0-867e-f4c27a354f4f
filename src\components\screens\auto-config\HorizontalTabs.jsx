import React from 'react';
import { NavLink } from 'react-router-dom';
import { Sync, Settings, AccountBalance, Business, Inventory, Compare, Receipt } from '@mui/icons-material';

const TABS = [
  {
    id: 'sync',
    label: 'Sync',
    icon: Sync,
    path: '/config',
  },
  {
    id: 'general',
    label: 'General',
    icon: Settings,
    path: '/config/general',
  },
  {
    id: 'ledgers',
    label: 'Ledgers',
    icon: AccountBalance,
    path: '/config/ledgers',
  },
  {
    id: 'supplier',
    label: 'Supplier',
    icon: Business,
    path: '/config/supplier',
  },
  {
    id: 'item',
    label: 'Item',
    icon: Inventory,
    path: '/config/item',
  },
  {
    id: 'item-maps',
    label: 'Item Maps',
    icon: Compare,
    path: '/config/item-maps',
  },
  {
    id: 'taxes',
    label: 'Taxes',
    icon: Receipt,
    path: '/config/taxes',
  },
];

function HorizontalTabs() {
  return (
    <div className="flex overflow-x-auto mb-1 border-b border-gray-200">
      {TABS.map((item) => {
        const Icon = item.icon;
        return (
          <NavLink
            key={item.id}
            to={item.path}
            className={({ isActive }) =>
              `px-6 py-3 text-center whitespace-nowrap font-medium transition-colors flex items-center gap-2 ${
                isActive ? 'border-b-2 border-accent2 text-accent2' : 'text-gray-600 hover:text-accent2'
              }`
            }
            end={item.path === '/config'}
          >
            <Icon fontSize="small" />
            {item.label}
          </NavLink>
        );
      })}
    </div>
  );
}

export default HorizontalTabs;

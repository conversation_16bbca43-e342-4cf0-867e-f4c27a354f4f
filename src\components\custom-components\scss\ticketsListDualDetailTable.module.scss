@import '../../../assets/scss/main.scss';

.tableWrapper {
  width: 100%;
  padding: 1em 0;
  max-height: 59vh;
  overflow-y: auto;
  -ms-overflow-style: none; /* For IE and Edge */
  scrollbar-width: none; /* For Firefox */

  &::-webkit-scrollbar {
    display: none; /* For Chrome, Safari, and Opera */
  }

  table {
    thead {
      background-color: #f5f5f5 !important;
      height: 5em !important;
      th {
        color: #717680 !important;
        text-align: start !important;
        padding: 1em !important;
      }
    }

    tbody {
      tr {
        background-color: $white;
        border-radius: 20px !important;
        cursor: pointer;
        -webkit-user-select: none;
        user-select: none;
      }

      td {
        padding: 1em !important;
        color: #717680;

        p {
          margin-bottom: 0.2em;
          font-size: 1.1em !important;
        }

        .ticketSubject {
          text-align: start !important;
          display: flex;
          align-items: center;
          gap: 1em;
          padding: 1em !important;
          font-weight: 900;
          font-size: 1rem !important;
          svg {
            width: 30px;
            height: 30px;
          }
          .logo {
            border: 2px solid $borderColor;
            border-radius: 10px;
            width: 45px;
            height: 45px;
            display: flex;
            justify-content: center;
            align-items: center;
          }
        }

        .category {
          height: 2.5em;
          padding: 0 1em;
          border-radius: 10px;
          display: flex;
          justify-content: center;
          align-items: center;
          color: #535862;
          background-color: #e9eaeb;
        }

        .businessInfo {
          text-align: start !important;
          display: flex;
          align-items: center;
          gap: 1em;
          padding: 1em !important;
          font-weight: 900;
          font-size: 1rem !important;
          .logo {
            border: 2px solid $borderColor;
            border-radius: 10px;
            width: 45px;
            height: 45px;
            display: flex;
            justify-content: center;
            align-items: center;
            background-color: #f5f5f5;
            svg {
              width: 25px;
              height: 25px;
            }
            img {
              width: 100%;
              height: 100%;
              border-radius: 10px;
            }
            svg,
            path {
              fill: #0a0d12;
            }
          }
        }

        .status {
          border-radius: 35px;
          background-color: #d5d5d569;
          text-align: center;
          height: 2.3em;
          font-size: 1em !important;
          display: flex;
          justify-content: center;
          align-items: center;
          width: auto;
          white-space: nowrap;
          padding: 0 1em;
          font-size: 1.1em !important;
          margin: 0;
        }
      }
    }
  }
}

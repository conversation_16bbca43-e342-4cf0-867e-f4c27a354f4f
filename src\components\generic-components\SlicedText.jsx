import React from 'react';

function SlicedText({ text, sliceTill = 20, className = '' }) {
  if (!text || text.length <= sliceTill) return <span className={`${className}`}>{text || ''}</span>;

  return (
    <span data-tooltip-id="tooltip" data-tooltip-content={String(text)} className={`cursor-pointer ${className}`}>
      {`${text.slice(0, sliceTill) || ''}...`}
    </span>
  );
}

export default React.memo(SlicedText);

import React, { useCallback, useEffect, useState } from 'react';
import style from './scss/commentInput.module.scss';
import FilePreview from '../../../custom-components/FilePreview';
import { useAuth } from '../../../../contexts/AuthContext';
import { AttachementsIcon, SendIcon } from '../../../../assets/svgs';
import { resturls } from '../../../utils/apiurls';
import uploadService from '../../../services/uploadService';
import { useParams } from 'react-router-dom';
import commentServices from '../../../services/commentServices';
import { toast } from 'react-toastify';
import dompurify from 'dompurify';
import { fileToPlainObject } from '../../../utils';

const defaultToastOptions = {
  autoClose: 2000,
  hideProgressBar: true,
  closeButton: true,
};

const PrivacyButton = React.memo(({ roleType, commentType, setCommentType }) => {
  if (roleType !== 'accountant') return <></>;
  return (
    <div className={style.commentTypeContainer}>
      <span>Send to</span>
      <p
        onClick={() => commentType !== 'private' && setCommentType('private')}
        className={commentType === 'private' && style.activeType}
      >
        Internal
      </p>
      <p
        onClick={() => commentType !== 'public' && setCommentType('public')}
        className={commentType === 'public' && style.activeType}
      >
        Public
      </p>
    </div>
  );
});

function CommentInput() {
  const { roleType, userInfo } = useAuth();
  const [input, setInput] = useState('');
  const [commentType, setCommentType] = useState(() => (roleType === 'user' ? 'public' : 'private'));
  const [attachedFileIds, setAttachedFileIds] = useState([]);
  const [uploadingFileStatus, setUploadingFileStatus] = useState(null);
  const [previews, setPreviews] = useState([]);
  const [isFileUploading, setIsFileUploading] = useState(false);
  const { id } = useParams();

  const handleAttachment = useCallback(() => {
    const fileInput = document.createElement('input');
    fileInput.type = 'file';
    fileInput.accept = 'image/*,.pdf,.doc,.docx,.txt,.xlsx,.pptx,.csv,.zip,.rar';
    fileInput.multiple = true;
    fileInput.onchange = async (e) => {
      const files = Array.from(e.target.files);
      const fileCount = files.length;
      if (fileCount === 0) return;
      setIsFileUploading(true);

      const uploadingToastId = toast.loading('Uploading files...');
      try {
        for (const file of files) {
          const res = await uploadService.uploadFile(resturls.addNewDocuments, file, (percent) => {
            setUploadingFileStatus({ file, uploaded: percent });
          });
          setAttachedFileIds((prev) => [...prev, res.id]);
          setPreviews((prev) => [
            ...prev,
            {
              ...fileToPlainObject(file),
              id: res.id,
              url: file.type.startsWith('image/') ? URL.createObjectURL(file) : null,
            },
          ]);
        }
        toast.update(uploadingToastId, {
          render: `${fileCount} ${fileCount > 1 ? 'files' : 'file'} uploaded successfully`,
          type: 'success',
          isLoading: false,
          ...defaultToastOptions,
        });
      } catch (err) {
        toast.update(uploadingToastId, {
          render: `Something went wrong, ${err.message || err.message[0] || 'Please try again'}`,
          type: 'error',
          isLoading: false,
          ...defaultToastOptions,
        });
      } finally {
        setUploadingFileStatus(null);
        setIsFileUploading(false);
      }
    };
    fileInput.click();
  }, []);

  const handleInputChange = useCallback((e) => {
    const text = e.target.value;
    setInput(text);
    const target = e.target;
    target.style.height = 'auto';
    target.style.height = `${target.scrollHeight}px`;
  }, []);

  const handleSendComment = useCallback(
    (input, attachedFileIds, commentType) => {
      const userId = userInfo?.userId;
      const backUpInput = input;
      const senatizedInput = dompurify.sanitize(backUpInput);
      setInput('');
      let obj = {
        description: senatizedInput,
        ticket: id,
        user: userId,
        documents: attachedFileIds,
        visiblity: commentType,
      };
      if (roleType === 'user') delete obj.visiblity;
      commentServices
        .sendComment(obj)
        .then(() => {
          toast.success('Comment sent successfully');
          setAttachedFileIds([]);
          setPreviews([]);
        })
        .catch((err) => {
          setInput(backUpInput);
          toast.error(`Something went wrong, ${err.message || 'Please try again'}`);
        });
    },
    [id, userInfo?.userId]
  );

  const handleRemove = useCallback((id) => {
    setPreviews((prev) => prev.filter((el) => el.id !== id));
    setAttachedFileIds((prev) => prev.filter((el) => el !== id));
  }, []);

  useEffect(() => {
    if (input.length === 0) {
      const commentInput = document.getElementById('commentInput');
      commentInput.style.height = 'auto';
      commentInput.style.height = `${commentInput.scrollHeight}px`;
    }
  }, [input]);

  return (
    <div className={style.commentInputWrapper}>
      {previews.length > 0 && (
        <div className={style.previewContainer}>
          {previews.map((preview) => {
            return (
              <FilePreview
                key={preview.id}
                preview={preview}
                handleRemove={(e) => {
                  e.stopPropagation();
                  handleRemove(preview.id);
                }}
                uploadingState={uploadingFileStatus}
              />
            );
          })}
        </div>
      )}
      <PrivacyButton roleType={roleType} commentType={commentType} setCommentType={setCommentType} />
      <div className={style.commentInputContainer}>
        <AttachementsIcon onClick={handleAttachment} className="cursor-pointer" />
        <textarea
          id="commentInput"
          value={input}
          rows={1}
          placeholder="Type your message/concern here"
          onChange={handleInputChange}
          onKeyDown={(e) => {
            if (e.key === 'Enter' && e.ctrlKey) {
              e.preventDefault();
              setInput((prev) => prev + '\n');
            } else if (e.key === 'Enter' && !e.shiftKey) {
              e.preventDefault();
              (input.trim().length > 0 || attachedFileIds.length > 0) &&
                !isFileUploading &&
                handleSendComment(input, attachedFileIds, commentType);
            }
          }}
        />
        {(input.trim().length > 0 || attachedFileIds.length > 0) && !isFileUploading && (
          <SendIcon
            className={style.sendIconSvg}
            onClick={() => handleSendComment(input, attachedFileIds, commentType)}
          />
        )}
      </div>
    </div>
  );
}

export default React.memo(CommentInput);

@import '../../../assets/scss/main.scss';

.cardsContainer {
  width: 100% !important;
  background-color: $white;
  border-radius: 10px;
  box-shadow: 0 0 1px 1px $borderColor;
  margin: 1em 0;
  padding: 1.5em 0;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
}

.card {
  margin: 0;
  background-color: $white;
  border: 1px solid $borderColor;
  border-radius: 15px;
  width: 87%;
  overflow: hidden;
  cursor: pointer;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  margin-bottom: 16px;
}

.card:last-child {
  margin-bottom: 0;
}

.leftContent {
  width: 10%;
}

.rightContent {
  width: 90%;
  height: 12em;
}

.card h5 {
  word-wrap: break-word;
  word-break: break-word;
  overflow-wrap: break-word;
  overflow: hidden;
  white-space: normal;
}

.card p {
  white-space: normal;
  overflow: hidden;
  text-overflow: ellipsis;
  width: 100%;
  word-wrap: break-word;
  word-break: break-word;
  overflow-wrap: break-word;
  overflow: hidden;
  white-space: normal;
}

.ticketContent {
  display: flex;
  gap: 1em;
  padding: 16px;
}

.ticketContent h5 {
  margin: 0;
  margin-bottom: 0.5em;
  font-size: 1.3em;
  font-weight: 600;
  text-transform: capitalize;
}

.date {
  font-size: 1em;
  color: #858585;
}

.bottomContent {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border-top: 1px solid #f0f0f0;
}

.msgIcon {
  cursor: pointer;
  height: 2.6em;
  align-items: center;
  display: flex;
  gap: 0.5em;
  justify-content: center;
  border-radius: 35px;
}

.msgIcon svg {
  width: 25px;
  height: 25px;
}

.iconWrapper {
  display: flex;
  justify-content: flex-end;
  width: 100%;
}

.iconWrapper span {
  font-size: 1.2em;
  color: #363f72;
}

.status {
  border-radius: 35px;
  background-color: #d5d5d569;
  text-align: center;
  height: 2.3em;
  font-size: 1em;
  display: flex;
  justify-content: center;
  align-items: center;
  width: auto;
  white-space: nowrap;
  padding: 0 1em;
  font-size: 1.1em;
  margin: 0;
}

.flag {
  height: 2.3em;
  padding: 0 1em;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #fee4e2;
  margin-left: 1em;
  border-radius: 15px;
}

.flag svg {
  width: 25px;
  height: 25px;
}

.flag svg path {
  fill: #b42318;
}

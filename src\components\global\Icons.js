export const dashboardIcon = () => (
  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path
      fill-rule="evenodd"
      clip-rule="evenodd"
      d="M2.71094 3.33203C2.71094 2.98685 2.99076 2.70703 3.33594 2.70703H8.33594C8.68112 2.70703 8.96094 2.98685 8.96094 3.33203V9.9987C8.96094 10.3439 8.68112 10.6237 8.33594 10.6237H3.33594C2.99076 10.6237 2.71094 10.3439 2.71094 9.9987V3.33203ZM3.96094 3.95703V9.3737H7.71094V3.95703H3.96094ZM11.0443 3.33203C11.0443 2.98685 11.3241 2.70703 11.6693 2.70703H16.6693C17.0144 2.70703 17.2943 2.98685 17.2943 3.33203V6.66536C17.2943 7.01054 17.0144 7.29036 16.6693 7.29036H11.6693C11.3241 7.29036 11.0443 7.01054 11.0443 6.66536V3.33203ZM12.2943 3.95703V6.04036H16.0443V3.95703H12.2943ZM11.0443 9.9987C11.0443 9.65352 11.3241 9.3737 11.6693 9.3737H16.6693C17.0144 9.3737 17.2943 9.65352 17.2943 9.9987V16.6654C17.2943 17.0105 17.0144 17.2904 16.6693 17.2904H11.6693C11.3241 17.2904 11.0443 17.0105 11.0443 16.6654V9.9987ZM12.2943 10.6237V16.0404H16.0443V10.6237H12.2943ZM2.71094 13.332C2.71094 12.9869 2.99076 12.707 3.33594 12.707H8.33594C8.68112 12.707 8.96094 12.9869 8.96094 13.332V16.6654C8.96094 17.0105 8.68112 17.2904 8.33594 17.2904H3.33594C2.99076 17.2904 2.71094 17.0105 2.71094 16.6654V13.332ZM3.96094 13.957V16.0404H7.71094V13.957H3.96094Z"
      fill="#717680"
    />
  </svg>
);

export const organisationIcon = () => (
  <svg width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path
      fill-rule="evenodd"
      clip-rule="evenodd"
      d="M10.1619 0.968344C10.3462 1.08222 10.4583 1.28339 10.4583 1.5V4.49884L15.18 7.64664C15.3539 7.76255 15.4583 7.9577 15.4583 8.16667V15.875H16.5C16.8452 15.875 17.125 16.1548 17.125 16.5C17.125 16.8452 16.8452 17.125 16.5 17.125H1.5C1.15482 17.125 0.875 16.8452 0.875 16.5C0.875 16.1548 1.15482 15.875 1.5 15.875H2.54167V4.83333C2.54167 4.5966 2.67542 4.38019 2.88716 4.27432L9.55382 0.940984C9.74757 0.844112 9.97766 0.854465 10.1619 0.968344ZM3.79167 15.875H9.20833V4.84187C9.20824 4.8358 9.20824 4.82973 9.20833 4.82365V2.51127L3.79167 5.21961V15.875ZM10.4583 6.00116V15.875H14.2083V8.50116L10.4583 6.00116ZM6.5 5.875C6.84518 5.875 7.125 6.15482 7.125 6.5V6.50833C7.125 6.85351 6.84518 7.13333 6.5 7.13333C6.15482 7.13333 5.875 6.85351 5.875 6.50833V6.5C5.875 6.15482 6.15482 5.875 6.5 5.875ZM6.5 8.375C6.84518 8.375 7.125 8.65482 7.125 9V9.00833C7.125 9.35351 6.84518 9.63333 6.5 9.63333C6.15482 9.63333 5.875 9.35351 5.875 9.00833V9C5.875 8.65482 6.15482 8.375 6.5 8.375ZM6.5 10.875C6.84518 10.875 7.125 11.1548 7.125 11.5V11.5083C7.125 11.8535 6.84518 12.1333 6.5 12.1333C6.15482 12.1333 5.875 11.8535 5.875 11.5083V11.5C5.875 11.1548 6.15482 10.875 6.5 10.875ZM6.5 13.375C6.84518 13.375 7.125 13.6548 7.125 14V14.0083C7.125 14.3535 6.84518 14.6333 6.5 14.6333C6.15482 14.6333 5.875 14.3535 5.875 14.0083V14C5.875 13.6548 6.15482 13.375 6.5 13.375Z"
      fill="#717680"
    />
  </svg>
);

export const userManagementIcon = () => (
  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path
      fill-rule="evenodd"
      clip-rule="evenodd"
      d="M9.16667 3.125C8.8904 3.125 8.62545 3.23475 8.4301 3.4301C8.23475 3.62545 8.125 3.8904 8.125 4.16667V5.83333C8.125 6.1096 8.23475 6.37455 8.4301 6.5699C8.62545 6.76525 8.8904 6.875 9.16667 6.875H10.8333C11.1096 6.875 11.3746 6.76525 11.5699 6.5699C11.7653 6.37455 11.875 6.1096 11.875 5.83333V4.16667C11.875 3.8904 11.7653 3.62545 11.5699 3.4301C11.3746 3.23475 11.1096 3.125 10.8333 3.125H9.16667ZM10.625 8.125H10.8333C11.4411 8.125 12.024 7.88356 12.4538 7.45379C12.8836 7.02402 13.125 6.44112 13.125 5.83333V4.16667C13.125 3.55888 12.8836 2.97598 12.4538 2.54621C12.024 2.11644 11.4411 1.875 10.8333 1.875H9.16667C8.55888 1.875 7.97598 2.11644 7.54621 2.54621C7.11644 2.97598 6.875 3.55888 6.875 4.16667V5.83333C6.875 6.44112 7.11644 7.02402 7.54621 7.45379C7.97598 7.88356 8.55888 8.125 9.16667 8.125H9.375V9.375H6.66667C6.05888 9.375 5.47598 9.61644 5.04621 10.0462C4.61644 10.476 4.375 11.0589 4.375 11.6667V11.875H4.16667C3.55888 11.875 2.97598 12.1164 2.54621 12.5462C2.11644 12.976 1.875 13.5589 1.875 14.1667V15.8333C1.875 16.4411 2.11644 17.024 2.54621 17.4538C2.97598 17.8836 3.55888 18.125 4.16667 18.125H5.83333C6.44112 18.125 7.02402 17.8836 7.45379 17.4538C7.88356 17.024 8.125 16.4411 8.125 15.8333V14.1667C8.125 13.5589 7.88356 12.976 7.45379 12.5462C7.02402 12.1164 6.44112 11.875 5.83333 11.875H5.625V11.6667C5.625 11.3904 5.73475 11.1254 5.9301 10.9301C6.12545 10.7347 6.3904 10.625 6.66667 10.625H13.3333C13.6096 10.625 13.8746 10.7347 14.0699 10.9301C14.2653 11.1254 14.375 11.3904 14.375 11.6667V11.875H14.1667C13.5589 11.875 12.976 12.1164 12.5462 12.5462C12.1164 12.976 11.875 13.5589 11.875 14.1667V15.8333C11.875 16.4411 12.1164 17.024 12.5462 17.4538C12.976 17.8836 13.5589 18.125 14.1667 18.125H15.8333C16.4411 18.125 17.024 17.8836 17.4538 17.4538C17.8836 17.024 18.125 16.4411 18.125 15.8333V14.1667C18.125 13.5589 17.8836 12.976 17.4538 12.5462C17.024 12.1164 16.4411 11.875 15.8333 11.875H15.625V11.6667C15.625 11.0589 15.3836 10.476 14.9538 10.0462C14.524 9.61644 13.9411 9.375 13.3333 9.375H10.625V8.125ZM14.1667 13.125C13.8904 13.125 13.6254 13.2347 13.4301 13.4301C13.2347 13.6254 13.125 13.8904 13.125 14.1667V15.8333C13.125 16.1096 13.2347 16.3746 13.4301 16.5699C13.6254 16.7653 13.8904 16.875 14.1667 16.875H15.8333C16.1096 16.875 16.3746 16.7653 16.5699 16.5699C16.7653 16.3746 16.875 16.1096 16.875 15.8333V14.1667C16.875 13.8904 16.7653 13.6254 16.5699 13.4301C16.3746 13.2347 16.1096 13.125 15.8333 13.125H14.1667ZM4.16667 13.125C3.8904 13.125 3.62545 13.2347 3.4301 13.4301C3.23475 13.6254 3.125 13.8904 3.125 14.1667V15.8333C3.125 16.1096 3.23475 16.3746 3.4301 16.5699C3.62545 16.7653 3.8904 16.875 4.16667 16.875H5.83333C6.1096 16.875 6.37455 16.7653 6.5699 16.5699C6.76525 16.3746 6.875 16.1096 6.875 15.8333V14.1667C6.875 13.8904 6.76525 13.6254 6.5699 13.4301C6.37455 13.2347 6.1096 13.125 5.83333 13.125H4.16667Z"
      fill="#717680"
    />
  </svg>
);

export const usersIcon = () => (
  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path
      fill-rule="evenodd"
      clip-rule="evenodd"
      d="M4.70104 3.03437C5.44337 2.29204 6.45019 1.875 7.5 1.875C8.54982 1.875 9.55663 2.29204 10.299 3.03437C11.0413 3.7767 11.4583 4.78352 11.4583 5.83333C11.4583 6.88315 11.0413 7.88997 10.299 8.6323C9.55663 9.37463 8.54982 9.79167 7.5 9.79167C6.45019 9.79167 5.44337 9.37463 4.70104 8.6323C3.9587 7.88997 3.54167 6.88315 3.54167 5.83333C3.54167 4.78352 3.9587 3.7767 4.70104 3.03437ZM7.5 3.125C6.78171 3.125 6.09283 3.41034 5.58492 3.91825C5.07701 4.42616 4.79167 5.11504 4.79167 5.83333C4.79167 6.55163 5.07701 7.2405 5.58492 7.74841C6.09283 8.25633 6.78171 8.54167 7.5 8.54167C8.2183 8.54167 8.90717 8.25633 9.41508 7.74841C9.92299 7.2405 10.2083 6.55163 10.2083 5.83333C10.2083 5.11504 9.92299 4.42616 9.41508 3.91825C8.90717 3.41034 8.2183 3.125 7.5 3.125ZM12.7279 2.45331C12.8135 2.11892 13.154 1.91725 13.4884 2.00287C14.3398 2.22087 15.0945 2.71606 15.6334 3.41036C16.1723 4.10466 16.4649 4.95859 16.4649 5.8375C16.4649 6.71642 16.1723 7.57034 15.6334 8.26464C15.0945 8.95895 14.3398 9.45413 13.4884 9.67214C13.154 9.75776 12.8135 9.55609 12.7279 9.2217C12.6422 8.8873 12.8439 8.54682 13.1783 8.4612C13.7609 8.31204 14.2772 7.97323 14.646 7.49818C15.0147 7.02313 15.2149 6.43887 15.2149 5.8375C15.2149 5.23614 15.0147 4.65188 14.646 4.17683C14.2772 3.70178 13.7609 3.36297 13.1783 3.21381C12.8439 3.12819 12.6422 2.7877 12.7279 2.45331ZM5.83333 13.125C5.11504 13.125 4.42616 13.4103 3.91825 13.9183C3.41034 14.4262 3.125 15.115 3.125 15.8333V17.5C3.125 17.8452 2.84518 18.125 2.5 18.125C2.15482 18.125 1.875 17.8452 1.875 17.5V15.8333C1.875 14.7835 2.29204 13.7767 3.03437 13.0344C3.7767 12.292 4.78352 11.875 5.83333 11.875H9.16667C10.2165 11.875 11.2233 12.292 11.9656 13.0344C12.708 13.7767 13.125 14.7835 13.125 15.8333V17.5C13.125 17.8452 12.8452 18.125 12.5 18.125C12.1548 18.125 11.875 17.8452 11.875 17.5V15.8333C11.875 15.115 11.5897 14.4262 11.0817 13.9183C10.5738 13.4103 9.88496 13.125 9.16667 13.125H5.83333ZM14.3948 12.4688C14.4811 12.1346 14.822 11.9336 15.1562 12.0199C16.0022 12.2383 16.7521 12.7304 17.2891 13.4195C17.8261 14.1087 18.12 14.9561 18.125 15.8298L18.125 15.8334L18.125 17.5C18.125 17.8452 17.8452 18.125 17.5 18.125C17.1548 18.125 16.875 17.8452 16.875 17.5V15.8351C16.8712 15.238 16.6701 14.6589 16.3031 14.1878C15.9356 13.7163 15.4225 13.3796 14.8438 13.2302C14.5095 13.1439 14.3086 12.803 14.3948 12.4688Z"
      fill="#717680"
    />
  </svg>
);
export const ticketManageIcon = () => (
  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path
      fill-rule="evenodd"
      clip-rule="evenodd"
      d="M3.33333 3.95703C3.27808 3.95703 3.22509 3.97898 3.18602 4.01805C3.14695 4.05712 3.125 4.11011 3.125 4.16536V7.4987C3.125 7.55395 3.14695 7.60694 3.18602 7.64601C3.22509 7.68508 3.27808 7.70703 3.33333 7.70703H6.66667C6.72192 7.70703 6.77491 7.68508 6.81398 7.64601C6.85305 7.60694 6.875 7.55395 6.875 7.4987V4.16536C6.875 4.11011 6.85305 4.05712 6.81398 4.01805C6.77491 3.97898 6.72192 3.95703 6.66667 3.95703H3.33333ZM2.30214 3.13417C2.57563 2.86068 2.94656 2.70703 3.33333 2.70703H6.66667C7.05344 2.70703 7.42437 2.86068 7.69786 3.13417C7.97135 3.40766 8.125 3.77859 8.125 4.16536V7.4987C8.125 7.88547 7.97135 8.2564 7.69786 8.5299C7.42437 8.80339 7.05344 8.95703 6.66667 8.95703H3.33333C2.94656 8.95703 2.57563 8.80339 2.30214 8.5299C2.02865 8.2564 1.875 7.88547 1.875 7.4987V4.16536C1.875 3.77859 2.02865 3.40766 2.30214 3.13417ZM10.2083 4.16536C10.2083 3.82019 10.4882 3.54036 10.8333 3.54036H17.5C17.8452 3.54036 18.125 3.82019 18.125 4.16536C18.125 4.51054 17.8452 4.79036 17.5 4.79036H10.8333C10.4882 4.79036 10.2083 4.51054 10.2083 4.16536ZM10.2083 7.4987C10.2083 7.15352 10.4882 6.8737 10.8333 6.8737H15C15.3452 6.8737 15.625 7.15352 15.625 7.4987C15.625 7.84388 15.3452 8.1237 15 8.1237H10.8333C10.4882 8.1237 10.2083 7.84388 10.2083 7.4987ZM3.33333 12.2904C3.27808 12.2904 3.22509 12.3123 3.18602 12.3514C3.14695 12.3905 3.125 12.4434 3.125 12.4987V15.832C3.125 15.8873 3.14695 15.9403 3.18602 15.9793C3.22509 16.0184 3.27808 16.0404 3.33333 16.0404H6.66667C6.72192 16.0404 6.77491 16.0184 6.81398 15.9793C6.85305 15.9403 6.875 15.8873 6.875 15.832V12.4987C6.875 12.4434 6.85305 12.3905 6.81398 12.3514C6.77491 12.3123 6.72192 12.2904 6.66667 12.2904H3.33333ZM2.30214 11.4675C2.57563 11.194 2.94656 11.0404 3.33333 11.0404H6.66667C7.05344 11.0404 7.42437 11.194 7.69786 11.4675C7.97135 11.741 8.125 12.1119 8.125 12.4987V15.832C8.125 16.2188 7.97135 16.5897 7.69786 16.8632C7.42437 17.1367 7.05344 17.2904 6.66667 17.2904H3.33333C2.94656 17.2904 2.57563 17.1367 2.30214 16.8632C2.02864 16.5897 1.875 16.2188 1.875 15.832V12.4987C1.875 12.1119 2.02865 11.741 2.30214 11.4675ZM10.2083 12.4987C10.2083 12.1535 10.4882 11.8737 10.8333 11.8737H17.5C17.8452 11.8737 18.125 12.1535 18.125 12.4987C18.125 12.8439 17.8452 13.1237 17.5 13.1237H10.8333C10.4882 13.1237 10.2083 12.8439 10.2083 12.4987ZM10.2083 15.832C10.2083 15.4869 10.4882 15.207 10.8333 15.207H15C15.3452 15.207 15.625 15.4869 15.625 15.832C15.625 16.1772 15.3452 16.457 15 16.457H10.8333C10.4882 16.457 10.2083 16.1772 10.2083 15.832Z"
      fill="#717680"
    />
  </svg>
);

export const systemActivityIcon = () => (
  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path
      d="M2.5024 8.30209V5.06459C2.5024 4.68125 2.63101 4.36097 2.88823 4.10375C3.14545 3.84653 3.46545 3.7182 3.84823 3.71875H16.1566C16.5399 3.71875 16.8599 3.84709 17.1166 4.10375C17.3732 4.36042 17.5018 4.6807 17.5024 5.06459V8.30209H16.6691V5.06459C16.6691 4.93681 16.6157 4.81931 16.5091 4.71209C16.4018 4.60542 16.2841 4.55209 16.1557 4.55209H3.84906C3.72073 4.55209 3.60295 4.60542 3.49573 4.71209C3.38851 4.81875 3.33517 4.93625 3.33573 5.06459V8.30209H2.5024ZM3.84906 14.5521C3.46517 14.5521 3.1449 14.4235 2.88823 14.1663C2.63156 13.909 2.50295 13.5888 2.5024 13.2054V9.13542H3.33573V13.2054C3.33573 13.3338 3.38906 13.4513 3.49573 13.5579C3.6024 13.6646 3.7199 13.7182 3.84823 13.7188H16.1566C16.2843 13.7188 16.4018 13.6651 16.5091 13.5579C16.6163 13.4507 16.6696 13.3332 16.6691 13.2054V9.13542H17.5024V13.2054C17.5024 13.5888 17.3741 13.909 17.1174 14.1663C16.8607 14.4235 16.5405 14.5521 16.1566 14.5521H3.84906ZM1.47656 17.8854V17.0521H18.5282V17.8854H1.47656ZM2.5024 9.13542V8.30209H6.66906C6.74684 8.30209 6.82073 8.32236 6.89073 8.36292C6.95962 8.40347 7.01295 8.46097 7.05073 8.53542L8.39823 11.2271L11.2974 6.02625C11.3346 5.95459 11.3866 5.90459 11.4532 5.87625C11.5193 5.84792 11.5913 5.83375 11.6691 5.83375C11.7468 5.83375 11.8207 5.84792 11.8907 5.87625C11.9607 5.90459 12.0141 5.95764 12.0507 6.03542L13.1841 8.30209H17.5024V9.13542H13.0791C12.9585 9.13542 12.8421 9.10236 12.7299 9.03625C12.6177 8.9707 12.5324 8.87764 12.4741 8.75709L11.6274 7.07542L8.7124 12.2429C8.67517 12.3179 8.62184 12.374 8.5524 12.4113C8.48295 12.4485 8.40906 12.4674 8.33073 12.4679C8.2524 12.4685 8.17962 12.4482 8.1124 12.4071C8.04517 12.366 7.9924 12.3082 7.95406 12.2338L6.40406 9.13375L2.5024 9.13542Z"
      fill="#717680"
    />
    <path
      d="M2.5024 8.30209V5.06459C2.5024 4.68125 2.63101 4.36097 2.88823 4.10375C3.14545 3.84653 3.46545 3.7182 3.84823 3.71875H16.1566C16.5399 3.71875 16.8599 3.84709 17.1166 4.10375C17.3732 4.36042 17.5018 4.6807 17.5024 5.06459V8.30209M2.5024 8.30209H3.33573V5.06459C3.33517 4.93625 3.38851 4.81875 3.49573 4.71209C3.60295 4.60542 3.72073 4.55209 3.84906 4.55209H16.1557C16.2841 4.55209 16.4018 4.60542 16.5091 4.71209C16.6157 4.81931 16.6691 4.93681 16.6691 5.06459V8.30209H17.5024M2.5024 8.30209V9.13542M2.5024 8.30209H6.66906C6.74684 8.30209 6.82073 8.32236 6.89073 8.36292C6.95962 8.40347 7.01295 8.46097 7.05073 8.53542L8.39823 11.2271L11.2974 6.02625C11.3346 5.95459 11.3866 5.90459 11.4532 5.87625C11.5193 5.84792 11.5913 5.83375 11.6691 5.83375C11.7468 5.83375 11.8207 5.84792 11.8907 5.87625C11.9607 5.90459 12.0141 5.95764 12.0507 6.03542L13.1841 8.30209H17.5024M17.5024 8.30209V9.13542M2.5024 9.13542V13.2054C2.50295 13.5888 2.63156 13.909 2.88823 14.1663C3.1449 14.4235 3.46517 14.5521 3.84906 14.5521H16.1566C16.5405 14.5521 16.8607 14.4235 17.1174 14.1663C17.3741 13.909 17.5024 13.5888 17.5024 13.2054V9.13542M2.5024 9.13542H3.33573V13.2054C3.33573 13.3338 3.38906 13.4513 3.49573 13.5579C3.6024 13.6646 3.7199 13.7182 3.84823 13.7188H16.1566C16.2843 13.7188 16.4018 13.6651 16.5091 13.5579C16.6163 13.4507 16.6696 13.3332 16.6691 13.2054V9.13542H17.5024M2.5024 9.13542L6.40406 9.13375L7.95406 12.2338C7.9924 12.3082 8.04517 12.366 8.1124 12.4071C8.17962 12.4482 8.2524 12.4685 8.33073 12.4679C8.40906 12.4674 8.48295 12.4485 8.5524 12.4113C8.62184 12.374 8.67517 12.3179 8.7124 12.2429L11.6274 7.07542L12.4741 8.75709C12.5324 8.87764 12.6177 8.9707 12.7299 9.03625C12.8421 9.10236 12.9585 9.13542 13.0791 9.13542H17.5024M1.47656 17.8854V17.0521H18.5282V17.8854H1.47656Z"
      stroke="#717680"
      stroke-width="0.5"
    />
  </svg>
);

export const FileManagementIcon = () => (
  <svg width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
    <g clip-path="url(#clip0_1965_3127)">
      <path d="M13.5 7.5H9.75" stroke="#717680" stroke-width="1.2" stroke-linecap="round" />
      <path
        d="M7.5 2.25H12.375C12.723 2.25 12.8977 2.25 13.044 2.2695C13.5349 2.33424 13.9907 2.55909 14.3408 2.90919C14.6909 3.2593 14.9158 3.71512 14.9805 4.206C15 4.35225 15 4.527 15 4.875"
        stroke="#717680"
        stroke-width="1.2"
      />
      <path
        d="M1.5 5.2125C1.5 4.55025 1.5 4.2195 1.5525 3.9435C1.66442 3.35088 1.95235 2.80575 2.37874 2.37923C2.80512 1.95271 3.35016 1.6646 3.94275 1.5525C4.2195 1.5 4.551 1.5 5.2125 1.5C5.502 1.5 5.6475 1.5 5.787 1.51275C6.38778 1.56917 6.95759 1.80546 7.422 2.19075C7.53 2.28 7.632 2.382 7.8375 2.5875L8.25 3C8.862 3.612 9.168 3.918 9.534 4.12125C9.73516 4.23337 9.94857 4.32195 10.17 4.38525C10.5735 4.5 11.0062 4.5 11.871 4.5H12.1515C14.1255 4.5 15.1132 4.5 15.7545 5.0775C15.814 5.13 15.87 5.186 15.9225 5.2455C16.5 5.88675 16.5 6.8745 16.5 8.8485V10.5C16.5 13.3282 16.5 14.7427 15.621 15.621C14.742 16.4992 13.3282 16.5 10.5 16.5H7.5C4.67175 16.5 3.25725 16.5 2.379 15.621C1.50075 14.742 1.5 13.3282 1.5 10.5V5.2125Z"
        stroke="#717680"
        stroke-width="1.2"
      />
    </g>
    <defs>
      <clipPath id="clip0_1965_3127">
        <rect width="18" height="18" fill="white" />
      </clipPath>
    </defs>
  </svg>
);

export const emailServerIcon = () => (
  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path
      fill-rule="evenodd"
      clip-rule="evenodd"
      d="M4.16667 4.79297C3.8904 4.79297 3.62545 4.90272 3.4301 5.09807C3.30832 5.21984 3.21981 5.36867 3.17041 5.53042L10 10.0835L16.8296 5.53042C16.7802 5.36867 16.6917 5.21984 16.5699 5.09807C16.3746 4.90272 16.1096 4.79297 15.8333 4.79297H4.16667ZM16.875 7.00246V10.8346C16.875 11.1798 17.1548 11.4596 17.5 11.4596C17.8452 11.4596 18.125 11.1798 18.125 10.8346V5.83464C18.125 5.22685 17.8836 4.64395 17.4538 4.21418C17.024 3.78441 16.4411 3.54297 15.8333 3.54297H4.16667C3.55888 3.54297 2.97598 3.78441 2.54621 4.21418C2.11644 4.64395 1.875 5.22685 1.875 5.83464V14.168C1.875 14.7758 2.11644 15.3587 2.54621 15.7884C2.97598 16.2182 3.55888 16.4596 4.16667 16.4596H9.16667C9.51184 16.4596 9.79167 16.1798 9.79167 15.8346C9.79167 15.4895 9.51184 15.2096 9.16667 15.2096H4.16667C3.8904 15.2096 3.62545 15.0999 3.4301 14.9045C3.23475 14.7092 3.125 14.4442 3.125 14.168V7.00246L9.65331 11.3547C9.86325 11.4946 10.1368 11.4946 10.3467 11.3547L16.875 7.00246ZM13.7247 13.726C13.9688 13.4819 14.3645 13.4819 14.6086 13.726C14.8527 13.9701 14.8527 14.3658 14.6086 14.6099L13.3839 15.8346L14.6086 17.0594C14.8527 17.3034 14.8527 17.6992 14.6086 17.9432C14.3645 18.1873 13.9688 18.1873 13.7247 17.9432L12.0581 16.2766C11.814 16.0325 11.814 15.6368 12.0581 15.3927L13.7247 13.726ZM16.2247 13.726C16.4688 13.4819 16.8645 13.4819 17.1086 13.726L18.7753 15.3927C19.0194 15.6368 19.0194 16.0325 18.7753 16.2766L17.1086 17.9432C16.8645 18.1873 16.4688 18.1873 16.2247 17.9432C15.9806 17.6992 15.9806 17.3034 16.2247 17.0594L17.4494 15.8346L16.2247 14.6099C15.9806 14.3658 15.9806 13.9701 16.2247 13.726Z"
      fill="#717680"
    />
  </svg>
);

export const fileManageIcon = () => (
  <svg width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
    <g clip-path="url(#clip0_346_20871)">
      <path d="M13.5 7.5H9.75" stroke="#717680" stroke-width="1.2" stroke-linecap="round" />
      <path
        d="M7.5 2.25H12.375C12.723 2.25 12.8977 2.25 13.044 2.2695C13.5349 2.33424 13.9907 2.55909 14.3408 2.90919C14.6909 3.2593 14.9158 3.71512 14.9805 4.206C15 4.35225 15 4.527 15 4.875"
        stroke="#717680"
        stroke-width="1.2"
      />
      <path
        d="M1.5 5.2125C1.5 4.55025 1.5 4.2195 1.5525 3.9435C1.66442 3.35088 1.95235 2.80575 2.37874 2.37923C2.80512 1.95271 3.35016 1.6646 3.94275 1.5525C4.2195 1.5 4.551 1.5 5.2125 1.5C5.502 1.5 5.6475 1.5 5.787 1.51275C6.38778 1.56917 6.95759 1.80546 7.422 2.19075C7.53 2.28 7.632 2.382 7.8375 2.5875L8.25 3C8.862 3.612 9.168 3.918 9.534 4.12125C9.73516 4.23337 9.94857 4.32195 10.17 4.38525C10.5735 4.5 11.0062 4.5 11.871 4.5H12.1515C14.1255 4.5 15.1132 4.5 15.7545 5.0775C15.814 5.13 15.87 5.186 15.9225 5.2455C16.5 5.88675 16.5 6.8745 16.5 8.8485V10.5C16.5 13.3282 16.5 14.7427 15.621 15.621C14.742 16.4992 13.3282 16.5 10.5 16.5H7.5C4.67175 16.5 3.25725 16.5 2.379 15.621C1.50075 14.742 1.5 13.3282 1.5 10.5V5.2125Z"
        stroke="#717680"
        stroke-width="1.2"
      />
    </g>
    <defs>
      <clipPath id="clip0_346_20871">
        <rect width="18" height="18" fill="white" />
      </clipPath>
    </defs>
  </svg>
);

export const defaultLogo2 = () => (
  <svg width="16" height="15" viewBox="0 0 16 15" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path
      fill-rule="evenodd"
      clip-rule="evenodd"
      d="M2.26126 1.26126C2.78871 0.733816 3.50408 0.4375 4.25 0.4375H7.25C7.99592 0.4375 8.71129 0.733816 9.23874 1.26126C9.76618 1.78871 10.0625 2.50408 10.0625 3.25V13.1875H12.6875V11.2343C12.3518 11.1392 12.0427 10.9595 11.7916 10.7084C11.4048 10.3216 11.1875 9.79701 11.1875 9.25V7.75C11.1875 7.20299 11.4048 6.67839 11.7916 6.29159C12.1784 5.9048 12.703 5.6875 13.25 5.6875C13.797 5.6875 14.3216 5.9048 14.7084 6.29159C15.0952 6.67839 15.3125 7.20299 15.3125 7.75V9.25C15.3125 9.79701 15.0952 10.3216 14.7084 10.7084C14.4573 10.9595 14.1482 11.1392 13.8125 11.2343V13.1875H14.75C15.0607 13.1875 15.3125 13.4393 15.3125 13.75C15.3125 14.0607 15.0607 14.3125 14.75 14.3125H1.25C0.93934 14.3125 0.6875 14.0607 0.6875 13.75C0.6875 13.4393 0.93934 13.1875 1.25 13.1875H1.4375V3.25C1.4375 2.50408 1.73382 1.78871 2.26126 1.26126ZM2.5625 13.1875H5.1875V10.75C5.1875 10.4393 5.43934 10.1875 5.75 10.1875C6.06066 10.1875 6.3125 10.4393 6.3125 10.75V13.1875H8.9375V3.25C8.9375 2.80245 8.75971 2.37323 8.44324 2.05676C8.12678 1.74029 7.69755 1.5625 7.25 1.5625H4.25C3.80245 1.5625 3.37323 1.74029 3.05676 2.05676C2.74029 2.37323 2.5625 2.80245 2.5625 3.25V13.1875ZM4.4375 4.75C4.4375 4.43934 4.68934 4.1875 5 4.1875H6.5C6.81066 4.1875 7.0625 4.43934 7.0625 4.75C7.0625 5.06066 6.81066 5.3125 6.5 5.3125H5C4.68934 5.3125 4.4375 5.06066 4.4375 4.75ZM13.25 6.8125C13.0014 6.8125 12.7629 6.91127 12.5871 7.08709C12.4113 7.2629 12.3125 7.50136 12.3125 7.75V9.25C12.3125 9.49864 12.4113 9.7371 12.5871 9.91291C12.7629 10.0887 13.0014 10.1875 13.25 10.1875C13.4986 10.1875 13.7371 10.0887 13.9129 9.91291C14.0887 9.7371 14.1875 9.49864 14.1875 9.25V7.75C14.1875 7.50136 14.0887 7.2629 13.9129 7.08709C13.7371 6.91127 13.4986 6.8125 13.25 6.8125ZM4.4375 7.75C4.4375 7.43934 4.68934 7.1875 5 7.1875H6.5C6.81066 7.1875 7.0625 7.43934 7.0625 7.75C7.0625 8.06066 6.81066 8.3125 6.5 8.3125H5C4.68934 8.3125 4.4375 8.06066 4.4375 7.75Z"
      fill="#0A0D12"
    />
  </svg>
);

export const defaultLogo = () => (
  <svg width="16" height="15" viewBox="0 0 16 15" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path
      fill-rule="evenodd"
      clip-rule="evenodd"
      d="M2.26126 1.26126C2.78871 0.733816 3.50408 0.4375 4.25 0.4375H7.25C7.99592 0.4375 8.71129 0.733816 9.23874 1.26126C9.76618 1.78871 10.0625 2.50408 10.0625 3.25V13.1875H12.6875V11.2343C12.3518 11.1392 12.0427 10.9595 11.7916 10.7084C11.4048 10.3216 11.1875 9.79701 11.1875 9.25V7.75C11.1875 7.20299 11.4048 6.67839 11.7916 6.29159C12.1784 5.9048 12.703 5.6875 13.25 5.6875C13.797 5.6875 14.3216 5.9048 14.7084 6.29159C15.0952 6.67839 15.3125 7.20299 15.3125 7.75V9.25C15.3125 9.79701 15.0952 10.3216 14.7084 10.7084C14.4573 10.9595 14.1482 11.1392 13.8125 11.2343V13.1875H14.75C15.0607 13.1875 15.3125 13.4393 15.3125 13.75C15.3125 14.0607 15.0607 14.3125 14.75 14.3125H1.25C0.93934 14.3125 0.6875 14.0607 0.6875 13.75C0.6875 13.4393 0.93934 13.1875 1.25 13.1875H1.4375V3.25C1.4375 2.50408 1.73382 1.78871 2.26126 1.26126ZM2.5625 13.1875H5.1875V10.75C5.1875 10.4393 5.43934 10.1875 5.75 10.1875C6.06066 10.1875 6.3125 10.4393 6.3125 10.75V13.1875H8.9375V3.25C8.9375 2.80245 8.75971 2.37323 8.44324 2.05676C8.12678 1.74029 7.69755 1.5625 7.25 1.5625H4.25C3.80245 1.5625 3.37323 1.74029 3.05676 2.05676C2.74029 2.37323 2.5625 2.80245 2.5625 3.25V13.1875ZM4.4375 4.75C4.4375 4.43934 4.68934 4.1875 5 4.1875H6.5C6.81066 4.1875 7.0625 4.43934 7.0625 4.75C7.0625 5.06066 6.81066 5.3125 6.5 5.3125H5C4.68934 5.3125 4.4375 5.06066 4.4375 4.75ZM13.25 6.8125C13.0014 6.8125 12.7629 6.91127 12.5871 7.08709C12.4113 7.2629 12.3125 7.50136 12.3125 7.75V9.25C12.3125 9.49864 12.4113 9.7371 12.5871 9.91291C12.7629 10.0887 13.0014 10.1875 13.25 10.1875C13.4986 10.1875 13.7371 10.0887 13.9129 9.91291C14.0887 9.7371 14.1875 9.49864 14.1875 9.25V7.75C14.1875 7.50136 14.0887 7.2629 13.9129 7.08709C13.7371 6.91127 13.4986 6.8125 13.25 6.8125ZM4.4375 7.75C4.4375 7.43934 4.68934 7.1875 5 7.1875H6.5C6.81066 7.1875 7.0625 7.43934 7.0625 7.75C7.0625 8.06066 6.81066 8.3125 6.5 8.3125H5C4.68934 8.3125 4.4375 8.06066 4.4375 7.75Z"
      fill="#0A0D12"
    />
  </svg>
);

export const plusIcon = () => (
  <svg width="17" height="16" viewBox="0 0 17 16" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path
      fill-rule="evenodd"
      clip-rule="evenodd"
      d="M8.5 2.83398C8.77614 2.83398 9 3.05784 9 3.33398V12.6673C9 12.9435 8.77614 13.1673 8.5 13.1673C8.22386 13.1673 8 12.9435 8 12.6673V3.33398C8 3.05784 8.22386 2.83398 8.5 2.83398Z"
      fill="#FDFDFD"
    />
    <path
      fill-rule="evenodd"
      clip-rule="evenodd"
      d="M3.33594 8C3.33594 7.72386 3.5598 7.5 3.83594 7.5H13.1693C13.4454 7.5 13.6693 7.72386 13.6693 8C13.6693 8.27614 13.4454 8.5 13.1693 8.5H3.83594C3.5598 8.5 3.33594 8.27614 3.33594 8Z"
      fill="#FDFDFD"
    />
  </svg>
);

export const homeIcon = () => (
  <svg width="13" height="14" viewBox="0 0 13 14" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path
      fill-rule="evenodd"
      clip-rule="evenodd"
      d="M6.14646 0.646447C6.34172 0.451184 6.65831 0.451184 6.85357 0.646447L12.8536 6.64645C12.9966 6.78945 13.0393 7.0045 12.962 7.19134C12.8846 7.37818 12.7022 7.5 12.5 7.5H11.6667V11.6667C11.6667 12.1529 11.4735 12.6192 11.1297 12.963C10.7859 13.3068 10.3196 13.5 9.83335 13.5H3.16668C2.68045 13.5 2.21414 13.3068 1.87032 12.963C1.5265 12.6192 1.33335 12.1529 1.33335 11.6667V7.5H0.500015C0.297783 7.5 0.115465 7.37818 0.0380748 7.19134C-0.0393157 7.0045 0.00346223 6.78945 0.146461 6.64645L6.14646 0.646447ZM5.00001 12.5H8.00001V9C8.00001 8.77899 7.91222 8.56703 7.75594 8.41074C7.59966 8.25446 7.38769 8.16667 7.16668 8.16667H5.83335C5.61233 8.16667 5.40037 8.25446 5.24409 8.41074C5.08781 8.56703 5.00001 8.77899 5.00001 9V12.5ZM9.00001 12.5V9C9.00001 8.51377 8.80686 8.04745 8.46304 7.70364C8.11923 7.35982 7.65291 7.16667 7.16668 7.16667H5.83335C5.34712 7.16667 4.8808 7.35982 4.53699 7.70364C4.19317 8.04745 4.00001 8.51377 4.00001 9V12.5H3.16668C2.94567 12.5 2.73371 12.4122 2.57743 12.2559C2.42115 12.0996 2.33335 11.8877 2.33335 11.6667V7C2.33335 6.72386 2.10949 6.5 1.83335 6.5H1.70712L6.50001 1.70711L11.2929 6.5H11.1667C10.8905 6.5 10.6667 6.72386 10.6667 7V11.6667C10.6667 11.8877 10.5789 12.0996 10.4226 12.2559C10.2663 12.4122 10.0544 12.5 9.83335 12.5H9.00001Z"
      fill="#0A0D12"
    />
  </svg>
);
export const logIcon = () => (
  <svg width="11" height="14" viewBox="0 0 11 14" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path
      fill-rule="evenodd"
      clip-rule="evenodd"
      d="M4.83594 1.5C4.61492 1.5 4.40296 1.5878 4.24668 1.74408C4.0904 1.90036 4.0026 2.11232 4.0026 2.33333C4.0026 2.55435 4.0904 2.76631 4.24668 2.92259C4.40296 3.07887 4.61492 3.16667 4.83594 3.16667H6.16927C6.39029 3.16667 6.60225 3.07887 6.75853 2.92259C6.91481 2.76631 7.0026 2.55435 7.0026 2.33333C7.0026 2.11232 6.91481 1.90036 6.75853 1.74408C6.60225 1.5878 6.39029 1.5 6.16927 1.5H4.83594ZM3.53958 1.03697C3.88339 0.693154 4.34971 0.5 4.83594 0.5H6.16927C6.6555 0.5 7.12182 0.693154 7.46563 1.03697C7.68882 1.26016 7.84852 1.53497 7.93311 1.83333H8.83594C9.32217 1.83333 9.78848 2.02649 10.1323 2.3703C10.4761 2.71412 10.6693 3.18044 10.6693 3.66667V11.6667C10.6693 12.1529 10.4761 12.6192 10.1323 12.963C9.78848 13.3068 9.32217 13.5 8.83594 13.5H2.16927C1.68304 13.5 1.21672 13.3068 0.872908 12.963C0.529092 12.6192 0.335938 12.1529 0.335938 11.6667V3.66667C0.335938 3.18044 0.529092 2.71412 0.872908 2.3703C1.21673 2.02649 1.68304 1.83333 2.16927 1.83333H3.0721C3.15669 1.53497 3.31639 1.26016 3.53958 1.03697ZM3.0721 2.83333H2.16927C1.94826 2.83333 1.7363 2.92113 1.58002 3.07741C1.42373 3.23369 1.33594 3.44565 1.33594 3.66667V11.6667C1.33594 11.8877 1.42373 12.0996 1.58002 12.2559C1.7363 12.4122 1.94826 12.5 2.16927 12.5H8.83594C9.05695 12.5 9.26891 12.4122 9.42519 12.2559C9.58147 12.0996 9.66927 11.8877 9.66927 11.6667V3.66667C9.66927 3.44565 9.58147 3.23369 9.42519 3.07741C9.26891 2.92113 9.05695 2.83333 8.83594 2.83333H7.93311C7.84852 3.1317 7.68882 3.40651 7.46563 3.6297C7.12182 3.97351 6.6555 4.16667 6.16927 4.16667H4.83594C4.34971 4.16667 3.88339 3.97351 3.53958 3.6297C3.31639 3.40651 3.15669 3.1317 3.0721 2.83333ZM3.0026 7C3.0026 6.72386 3.22646 6.5 3.5026 6.5H3.50927C3.78541 6.5 4.00927 6.72386 4.00927 7C4.00927 7.27614 3.78541 7.5 3.50927 7.5H3.5026C3.22646 7.5 3.0026 7.27614 3.0026 7ZM5.66927 7C5.66927 6.72386 5.89313 6.5 6.16927 6.5H7.5026C7.77875 6.5 8.0026 6.72386 8.0026 7C8.0026 7.27614 7.77875 7.5 7.5026 7.5H6.16927C5.89313 7.5 5.66927 7.27614 5.66927 7ZM3.0026 9.66667C3.0026 9.39052 3.22646 9.16667 3.5026 9.16667H3.50927C3.78541 9.16667 4.00927 9.39052 4.00927 9.66667C4.00927 9.94281 3.78541 10.1667 3.50927 10.1667H3.5026C3.22646 10.1667 3.0026 9.94281 3.0026 9.66667ZM5.66927 9.66667C5.66927 9.39052 5.89313 9.16667 6.16927 9.16667H7.5026C7.77875 9.16667 8.0026 9.39052 8.0026 9.66667C8.0026 9.94281 7.77875 10.1667 7.5026 10.1667H6.16927C5.89313 10.1667 5.66927 9.94281 5.66927 9.66667Z"
      fill="#535862"
    />
  </svg>
);
export const reportIcon = () => (
  <svg width="13" height="12" viewBox="0 0 13 12" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path
      fill-rule="evenodd"
      clip-rule="evenodd"
      d="M2.4974 1.16602C2.27638 1.16602 2.06442 1.25381 1.90814 1.41009C1.75186 1.56637 1.66406 1.77833 1.66406 1.99935V9.99935C1.66406 10.2204 1.75186 10.4323 1.90814 10.5886C2.06442 10.7449 2.27638 10.8327 2.4974 10.8327H10.4974C10.7184 10.8327 10.9304 10.7449 11.0867 10.5886C11.2429 10.4323 11.3307 10.2204 11.3307 9.99935V1.99935C11.3307 1.77834 11.2429 1.56637 11.0867 1.41009C10.9304 1.25381 10.7184 1.16602 10.4974 1.16602H2.4974ZM1.20103 0.702986C1.54485 0.35917 2.01117 0.166016 2.4974 0.166016H10.4974C10.9836 0.166016 11.4499 0.35917 11.7938 0.702986C12.1376 1.0468 12.3307 1.51312 12.3307 1.99935V9.99935C12.3307 10.4856 12.1376 10.9519 11.7938 11.2957C11.4499 11.6395 10.9836 11.8327 10.4974 11.8327H2.4974C2.01117 11.8327 1.54485 11.6395 1.20103 11.2957C0.857217 10.9519 0.664062 10.4856 0.664062 9.99935V1.99935C0.664062 1.51312 0.857217 1.0468 1.20103 0.702986ZM8.14384 4.31246C8.3391 4.1172 8.65569 4.1172 8.85095 4.31246L10.1843 5.64579C10.3795 5.84106 10.3795 6.15764 10.1843 6.3529C9.98902 6.54816 9.67244 6.54816 9.47718 6.3529L8.4974 5.37312L6.85095 7.01957C6.65569 7.21483 6.3391 7.21483 6.14384 7.01957L5.16406 6.03979L3.51762 7.68623C3.32235 7.8815 3.00577 7.8815 2.81051 7.68623C2.61525 7.49097 2.61525 7.17439 2.81051 6.97913L4.81051 4.97913C5.00577 4.78387 5.32235 4.78387 5.51762 4.97913L6.4974 5.95891L8.14384 4.31246Z"
      fill="#535862"
    />
  </svg>
);
export const menuIcon = () => (
  <svg width="13" height="10" viewBox="0 0 13 10" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path
      fill-rule="evenodd"
      clip-rule="evenodd"
      d="M0.664062 1C0.664062 0.723858 0.88792 0.5 1.16406 0.5H11.8307C12.1069 0.5 12.3307 0.723858 12.3307 1C12.3307 1.27614 12.1069 1.5 11.8307 1.5H1.16406C0.88792 1.5 0.664062 1.27614 0.664062 1ZM0.664062 5C0.664062 4.72386 0.88792 4.5 1.16406 4.5H11.8307C12.1069 4.5 12.3307 4.72386 12.3307 5C12.3307 5.27614 12.1069 5.5 11.8307 5.5H1.16406C0.88792 5.5 0.664062 5.27614 0.664062 5ZM0.664062 9C0.664062 8.72386 0.88792 8.5 1.16406 8.5H11.8307C12.1069 8.5 12.3307 8.72386 12.3307 9C12.3307 9.27614 12.1069 9.5 11.8307 9.5H1.16406C0.88792 9.5 0.664062 9.27614 0.664062 9Z"
      fill="#535862"
    />
  </svg>
);

export const bussinessIcon = () => (
  <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path
      d="M6.25 12C6.05109 12 5.86032 12.079 5.71967 12.2197C5.57902 12.3603 5.5 12.5511 5.5 12.75C5.5 12.9489 5.57902 13.1397 5.71967 13.2803C5.86032 13.421 6.05109 13.5 6.25 13.5H6.75C6.94891 13.5 7.13968 13.421 7.28033 13.2803C7.42098 13.1397 7.5 12.9489 7.5 12.75C7.5 12.5511 7.42098 12.3603 7.28033 12.2197C7.13968 12.079 6.94891 12 6.75 12H6.25ZM5.5 9.25C5.5 9.05109 5.57902 8.86032 5.71967 8.71967C5.86032 8.57902 6.05109 8.5 6.25 8.5H6.75C6.94891 8.5 7.13968 8.57902 7.28033 8.71967C7.42098 8.86032 7.5 9.05109 7.5 9.25C7.5 9.44891 7.42098 9.63968 7.28033 9.78033C7.13968 9.92098 6.94891 10 6.75 10H6.25C6.05109 10 5.86032 9.92098 5.71967 9.78033C5.57902 9.63968 5.5 9.44891 5.5 9.25ZM6.25 5C6.05109 5 5.86032 5.07902 5.71967 5.21967C5.57902 5.36032 5.5 5.55109 5.5 5.75C5.5 5.94891 5.57902 6.13968 5.71967 6.28033C5.86032 6.42098 6.05109 6.5 6.25 6.5H6.75C6.94891 6.5 7.13968 6.42098 7.28033 6.28033C7.42098 6.13968 7.5 5.94891 7.5 5.75C7.5 5.55109 7.42098 5.36032 7.28033 5.21967C7.13968 5.07902 6.94891 5 6.75 5H6.25ZM9 12.75C9 12.5511 9.07902 12.3603 9.21967 12.2197C9.36032 12.079 9.55109 12 9.75 12H10.25C10.4489 12 10.6397 12.079 10.7803 12.2197C10.921 12.3603 11 12.5511 11 12.75C11 12.9489 10.921 13.1397 10.7803 13.2803C10.6397 13.421 10.4489 13.5 10.25 13.5H9.75C9.55109 13.5 9.36032 13.421 9.21967 13.2803C9.07902 13.1397 9 12.9489 9 12.75ZM9.75 8.5C9.55109 8.5 9.36032 8.57902 9.21967 8.71967C9.07902 8.86032 9 9.05109 9 9.25C9 9.44891 9.07902 9.63968 9.21967 9.78033C9.36032 9.92098 9.55109 10 9.75 10H10.25C10.4489 10 10.6397 9.92098 10.7803 9.78033C10.921 9.63968 11 9.44891 11 9.25C11 9.05109 10.921 8.86032 10.7803 8.71967C10.6397 8.57902 10.4489 8.5 10.25 8.5H9.75ZM9 5.75C9 5.55109 9.07902 5.36032 9.21967 5.21967C9.36032 5.07902 9.55109 5 9.75 5H10.25C10.4489 5 10.6397 5.07902 10.7803 5.21967C10.921 5.36032 11 5.55109 11 5.75C11 5.94891 10.921 6.13968 10.7803 6.28033C10.6397 6.42098 10.4489 6.5 10.25 6.5H9.75C9.55109 6.5 9.36032 6.42098 9.21967 6.28033C9.07902 6.13968 9 5.94891 9 5.75ZM13.25 12C13.0511 12 12.8603 12.079 12.7197 12.2197C12.579 12.3603 12.5 12.5511 12.5 12.75C12.5 12.9489 12.579 13.1397 12.7197 13.2803C12.8603 13.421 13.0511 13.5 13.25 13.5H13.75C13.9489 13.5 14.1397 13.421 14.2803 13.2803C14.421 13.1397 14.5 12.9489 14.5 12.75C14.5 12.5511 14.421 12.3603 14.2803 12.2197C14.1397 12.079 13.9489 12 13.75 12H13.25ZM12.5 9.25C12.5 9.05109 12.579 8.86032 12.7197 8.71967C12.8603 8.57902 13.0511 8.5 13.25 8.5H13.75C13.9489 8.5 14.1397 8.57902 14.2803 8.71967C14.421 8.86032 14.5 9.05109 14.5 9.25C14.5 9.44891 14.421 9.63968 14.2803 9.78033C14.1397 9.92098 13.9489 10 13.75 10H13.25C13.0511 10 12.8603 9.92098 12.7197 9.78033C12.579 9.63968 12.5 9.44891 12.5 9.25ZM13.25 5C13.0511 5 12.8603 5.07902 12.7197 5.21967C12.579 5.36032 12.5 5.55109 12.5 5.75C12.5 5.94891 12.579 6.13968 12.7197 6.28033C12.8603 6.42098 13.0511 6.5 13.25 6.5H13.75C13.9489 6.5 14.1397 6.42098 14.2803 6.28033C14.421 6.13968 14.5 5.94891 14.5 5.75C14.5 5.55109 14.421 5.36032 14.2803 5.21967C14.1397 5.07902 13.9489 5 13.75 5H13.25Z"
      fill="black"
    />
    <path
      d="M2 20V3C2 2.46957 2.21071 1.96086 2.58579 1.58579C2.96086 1.21071 3.46957 1 4 1H16C16.5304 1 17.0391 1.21071 17.4142 1.58579C17.7893 1.96086 18 2.46957 18 3V20C18 20.1733 17.979 20.34 17.937 20.5H20C20.1326 20.5 20.2598 20.4473 20.3536 20.3536C20.4473 20.2598 20.5 20.1326 20.5 20V12C20.5 11.9224 20.4819 11.8458 20.4472 11.7764C20.4125 11.707 20.3621 11.6466 20.3 11.6L19.8 11.225C19.6409 11.1057 19.5357 10.928 19.5075 10.7311C19.4794 10.5342 19.5307 10.3341 19.65 10.175C19.7693 10.0159 19.947 9.91067 20.1439 9.88254C20.3408 9.85441 20.5409 9.90565 20.7 10.025L21.2 10.4C21.704 10.778 22 11.37 22 12V20C22 20.5304 21.7893 21.0391 21.4142 21.4142C21.0391 21.7893 20.5304 22 20 22H16.438C16.3823 21.9986 16.3267 21.9926 16.272 21.982C16.1827 21.994 16.092 22 16 22H12.25C12.0511 22 11.8603 21.921 11.7197 21.7803C11.579 21.6397 11.5 21.4489 11.5 21.25V19H8.5V21.25C8.5 21.4489 8.42098 21.6397 8.28033 21.7803C8.13968 21.921 7.94891 22 7.75 22H4C3.46957 22 2.96086 21.7893 2.58579 21.4142C2.21071 21.0391 2 20.5304 2 20ZM4 20.5H7V18.25C7 18.0511 7.07902 17.8603 7.21967 17.7197C7.36032 17.579 7.55109 17.5 7.75 17.5H12.25C12.4489 17.5 12.6397 17.579 12.7803 17.7197C12.921 17.8603 13 18.0511 13 18.25V20.5H16C16.1326 20.5 16.2598 20.4473 16.3536 20.3536C16.4473 20.2598 16.5 20.1326 16.5 20V3C16.5 2.86739 16.4473 2.74021 16.3536 2.64645C16.2598 2.55268 16.1326 2.5 16 2.5H4C3.86739 2.5 3.74021 2.55268 3.64645 2.64645C3.55268 2.74021 3.5 2.86739 3.5 3V20C3.5 20.1326 3.55268 20.2598 3.64645 20.3536C3.74021 20.4473 3.86739 20.5 4 20.5Z"
      fill="black"
    />
  </svg>
);

export const userIcon = () => (
  <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path
      d="M17.9267 19.6348H20.0647C20.2528 19.6378 20.4388 19.5951 20.6068 19.5106C20.7748 19.426 20.9198 19.302 21.0295 19.1492C21.1391 18.9964 21.2102 18.8194 21.2365 18.6331C21.2628 18.4469 21.2437 18.2571 21.1807 18.0798C20.6356 16.913 19.7725 15.9234 18.6905 15.2247C17.6086 14.5261 16.3515 14.1466 15.0637 14.1298M15.0637 11.3708C15.545 11.371 16.0215 11.2763 16.4662 11.0922C16.9108 10.9081 17.3149 10.6383 17.6552 10.298C17.9955 9.95777 18.2655 9.55381 18.4497 9.10921C18.6339 8.66461 18.7287 8.18808 18.7287 7.70683C18.73 7.22474 18.6362 6.74713 18.4526 6.30137C18.269 5.85562 17.9992 5.45047 17.6587 5.10917C17.3183 4.76786 16.9138 4.4971 16.4685 4.3124C16.0232 4.1277 15.5458 4.03269 15.0637 4.03283M9.33872 11.4858C10.4405 11.4832 11.4962 11.0436 12.2742 10.2635C13.0522 9.48343 13.489 8.42657 13.4887 7.32483C13.4887 6.22391 13.0514 5.16809 12.2729 4.38963C11.4945 3.61116 10.4386 3.17383 9.33772 3.17383C8.23681 3.17383 7.18098 3.61116 6.40252 4.38963C5.62406 5.16809 5.18672 6.22391 5.18672 7.32483C5.18672 8.42657 5.62377 9.48332 6.40197 10.2632C7.18018 11.0431 8.23598 11.4824 9.33772 11.4848M14.0237 20.8278C14.376 20.8273 14.7212 20.7294 15.0212 20.5448C15.3211 20.3601 15.5642 20.0961 15.7233 19.7819C15.8824 19.4676 15.9515 19.1155 15.9228 18.7644C15.8941 18.4133 15.7688 18.0771 15.5607 17.7928C14.8318 16.8253 13.8935 16.0352 12.816 15.4817C11.7385 14.9283 10.5497 14.6257 9.33872 14.5968C8.12763 14.6259 6.93881 14.9285 5.8613 15.4822C4.78378 16.0358 3.84549 16.8261 3.11672 17.7938C2.90938 18.0781 2.78462 18.4141 2.75623 18.7648C2.72783 19.1155 2.79691 19.4672 2.95583 19.7811C3.11474 20.095 3.35732 20.3589 3.65677 20.5436C3.95621 20.7283 4.30088 20.8267 4.65272 20.8278H14.0237Z"
      stroke="black"
      stroke-width="1.5"
      stroke-linecap="round"
      stroke-linejoin="round"
    />
  </svg>
);

export const whitePlusIcon = () => (
  <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
    <rect width="24" height="24" rx="4" fill="#FDFDFD" />
    <path
      d="M6 12H18M12 18V6"
      stroke="#252B37"
      stroke-width="1.5"
      stroke-miterlimit="10"
      stroke-linecap="round"
      stroke-linejoin="round"
    />
  </svg>
);

export const rightArrowIcon = () => (
  <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path
      fill-rule="evenodd"
      clip-rule="evenodd"
      d="M5.64645 3.64645C5.84171 3.45118 6.15829 3.45118 6.35355 3.64645L10.3536 7.64645C10.5488 7.84171 10.5488 8.15829 10.3536 8.35355L6.35355 12.3536C6.15829 12.5488 5.84171 12.5488 5.64645 12.3536C5.45118 12.1583 5.45118 11.8417 5.64645 11.6464L9.29289 8L5.64645 4.35355C5.45118 4.15829 5.45118 3.84171 5.64645 3.64645Z"
      fill="#0A0D12"
    />
  </svg>
);

export const progressSvg = () => (
  <svg width="503" height="22" viewBox="0 0 503 22" fill="none" xmlns="http://www.w3.org/2000/svg">
    <rect y="0.0664062" width="503" height="21.8696" rx="6" fill="#EAECF5" />
    <rect y="0.0664062" width="192.079" height="21.8696" rx="6" fill="#293056" />
    <rect x="171.109" y="0.0664062" width="145.5" height="21.8696" fill="#4E5BA6" />
    <rect x="302.328" y="0.0664062" width="120.791" height="21.8696" fill="#9EA5D1" />
  </svg>
);

export const emptyBox = () => (
  <svg width="49" height="49" viewBox="0 0 49 49" fill="none" xmlns="http://www.w3.org/2000/svg">
    <g clip-path="url(#clip0_346_18791)">
      <path
        d="M41.5884 36.4034L24.2512 45.9234L6.91406 36.4034V17.3638L24.2512 7.84375L41.5884 17.3638V36.4034Z"
        fill="#4E5BA6"
      />
      <path
        d="M6.91406 17.3638V17.3641L24.2512 26.8841L41.5884 17.3641V17.3638L24.2512 7.84375L6.91406 17.3638Z"
        fill="#EAECF5"
      />
      <path
        d="M6.91406 17.373V36.4068L24.2512 45.9269L24.2615 45.9214V26.8872L6.92432 17.3672L6.91406 17.373Z"
        fill="#4E5BA6"
      />
      <path d="M41.5871 17.3672L47.3125 23.0922L29.9753 32.6122L24.25 26.8868L41.5871 17.3672Z" fill="#EAECF5" />
      <path d="M6.91284 17.3672L1.1875 23.0922L18.5246 32.6122L24.25 26.8868L6.91284 17.3672Z" fill="#EAECF5" />
      <path d="M41.5871 17.3661L47.3125 11.6411L29.9753 2.12109L24.25 7.84643L41.5871 17.3661Z" fill="#EAECF5" />
      <path d="M6.91284 17.3661L1.1875 11.6411L18.5246 2.12109L24.25 7.84643L6.91284 17.3661Z" fill="#EAECF5" />
      <path
        d="M42.9131 17.3661L47.9752 12.304C48.184 12.0956 48.2821 11.8008 48.2404 11.5089C48.199 11.2171 48.0221 10.9615 47.7636 10.8194L41.6779 7.4777C41.2241 7.22867 40.6543 7.39457 40.4049 7.8483C40.1559 8.3024 40.3214 8.87223 40.7755 9.12125L45.7663 11.8616L41.4219 16.2056L25.7968 7.62565L30.1408 3.28165L34.532 5.69315C34.9857 5.94217 35.5559 5.77665 35.805 5.32255C36.0543 4.86881 35.8885 4.29862 35.4343 4.0496L30.4264 1.29935C30.0609 1.09867 29.6068 1.16349 29.3124 1.45829L24.2499 6.52079L19.1874 1.45829C18.8926 1.16349 18.4385 1.09904 18.0734 1.29935L0.73625 10.8194C0.477705 10.9615 0.300826 11.2167 0.259444 11.5089C0.217696 11.8008 0.31584 12.0956 0.52458 12.304L5.58671 17.3661L0.52458 22.4279C0.31584 22.6366 0.217696 22.9311 0.259444 23.2233C0.300826 23.5152 0.477705 23.7708 0.73625 23.9125L5.97526 26.7895V36.4054C5.97526 36.7478 6.16166 37.0628 6.46159 37.2276L23.7987 46.7472C23.9394 46.8245 24.0946 46.8629 24.2499 46.8629C24.4052 46.8629 24.5605 46.8245 24.7011 46.7472L42.0382 37.2276C42.3382 37.0628 42.5246 36.7478 42.5246 36.4054V26.7895L47.7636 23.9129C48.0221 23.7708 48.199 23.5152 48.2404 23.2233C48.2821 22.9314 48.184 22.6366 47.9752 22.4282L42.9131 17.3661ZM24.2499 25.8161L8.86063 17.3658L24.2499 8.91507L39.6392 17.3658L24.2499 25.8161ZM18.3594 3.28165L22.703 7.62565L7.07792 16.2056L2.73393 11.8616L18.3594 3.28165ZM7.07865 18.5263L22.7038 27.1062L18.3594 31.4506L2.73393 22.8706L7.07865 18.5263ZM40.6496 35.851L25.1874 44.3412V34.7578C25.1874 34.24 24.7677 33.8203 24.2499 33.8203C23.7321 33.8203 23.3124 34.24 23.3124 34.7578V44.3412L7.85026 35.851V27.8192L18.0734 33.4329C18.2151 33.5105 18.3704 33.5486 18.5242 33.5486C18.767 33.5486 19.0072 33.4541 19.1874 33.2739L24.2499 28.2114L29.3124 33.2739C29.4926 33.4545 29.7328 33.5486 29.9756 33.5486C30.1294 33.5486 30.2847 33.5105 30.4264 33.4329L40.6496 27.8192V35.851ZM30.1408 31.4506L25.7964 27.1059L41.4215 18.5263L45.7663 22.8703L30.1408 31.4506Z"
        fill="#0A0D12"
      />
      <path
        d="M38.2031 7.57422C38.4496 7.57422 38.6906 7.47388 38.866 7.29919C39.0403 7.12488 39.1406 6.88318 39.1406 6.63672C39.1406 6.38989 39.0403 6.14819 38.866 5.97388C38.6916 5.79919 38.4496 5.69922 38.2031 5.69922C37.9556 5.69922 37.7146 5.79919 37.5403 5.97388C37.3649 6.14819 37.2656 6.38989 37.2656 6.63672C37.2656 6.88318 37.3649 7.12488 37.5403 7.29919C37.7146 7.47388 37.9567 7.57422 38.2031 7.57422Z"
        fill="#0A0D12"
      />
      <path
        d="M24.25 30.707C24.0035 30.707 23.7615 30.807 23.5872 30.9817C23.4128 31.1567 23.3125 31.3977 23.3125 31.6445C23.3125 31.8917 23.4128 32.1327 23.5872 32.3081C23.7615 32.4824 24.0035 32.582 24.25 32.582C24.4965 32.582 24.7385 32.4824 24.9128 32.3081C25.0872 32.1327 25.1875 31.8917 25.1875 31.6445C25.1875 31.3977 25.0872 31.1567 24.9128 30.9817C24.7385 30.807 24.4965 30.707 24.25 30.707Z"
        fill="#0A0D12"
      />
    </g>
    <defs>
      <clipPath id="clip0_346_18791">
        <rect width="48" height="48" fill="white" transform="translate(0.25 0.0585938)" />
      </clipPath>
    </defs>
  </svg>
);

export const downloadIcon = () => (
  <svg width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path
      fill-rule="evenodd"
      clip-rule="evenodd"
      d="M9 2.4375C9.31066 2.4375 9.5625 2.68934 9.5625 3V10.642L12.3523 7.85225C12.5719 7.63258 12.9281 7.63258 13.1477 7.85225C13.3674 8.07192 13.3674 8.42808 13.1477 8.64775L9.39775 12.3977C9.17808 12.6174 8.82192 12.6174 8.60225 12.3977L4.85225 8.64775C4.63258 8.42808 4.63258 8.07192 4.85225 7.85225C5.07192 7.63258 5.42808 7.63258 5.64775 7.85225L8.4375 10.642V3C8.4375 2.68934 8.68934 2.4375 9 2.4375ZM3 12.1875C3.31066 12.1875 3.5625 12.4393 3.5625 12.75V14.25C3.5625 14.4986 3.66127 14.7371 3.83709 14.9129C4.0129 15.0887 4.25136 15.1875 4.5 15.1875H13.5C13.7486 15.1875 13.9871 15.0887 14.1629 14.9129C14.3387 14.7371 14.4375 14.4986 14.4375 14.25V12.75C14.4375 12.4393 14.6893 12.1875 15 12.1875C15.3107 12.1875 15.5625 12.4393 15.5625 12.75V14.25C15.5625 14.797 15.3452 15.3216 14.9584 15.7084C14.5716 16.0952 14.047 16.3125 13.5 16.3125H4.5C3.95299 16.3125 3.42839 16.0952 3.04159 15.7084C2.6548 15.3216 2.4375 14.797 2.4375 14.25V12.75C2.4375 12.4393 2.68934 12.1875 3 12.1875Z"
      fill="#293056"
    />
  </svg>
);

export const dropdownIcon = () => (
  <svg width="19" height="18" viewBox="0 0 19 18" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path
      fill-rule="evenodd"
      clip-rule="evenodd"
      d="M4.60225 6.35225C4.82192 6.13258 5.17808 6.13258 5.39775 6.35225L9.5 10.4545L13.6023 6.35225C13.8219 6.13258 14.1781 6.13258 14.3977 6.35225C14.6174 6.57192 14.6174 6.92808 14.3977 7.14775L9.89775 11.6477C9.67808 11.8674 9.32192 11.8674 9.10225 11.6477L4.60225 7.14775C4.38258 6.92808 4.38258 6.57192 4.60225 6.35225Z"
      fill="#000000"
    />
  </svg>
);

export const DropdownIcon = () => (
  <svg width="19" height="18" viewBox="0 0 19 18" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path
      fill-rule="evenodd"
      clip-rule="evenodd"
      d="M4.60225 6.35225C4.82192 6.13258 5.17808 6.13258 5.39775 6.35225L9.5 10.4545L13.6023 6.35225C13.8219 6.13258 14.1781 6.13258 14.3977 6.35225C14.6174 6.57192 14.6174 6.92808 14.3977 7.14775L9.89775 11.6477C9.67808 11.8674 9.32192 11.8674 9.10225 11.6477L4.60225 7.14775C4.38258 6.92808 4.38258 6.57192 4.60225 6.35225Z"
      fill="#FDFDFD"
    />
  </svg>
);

export const ticketIcon = () => (
  <svg width="37" height="36" viewBox="0 0 37 36" fill="none" xmlns="http://www.w3.org/2000/svg">
    <rect x="0.753906" y="0.5" width="35" height="35" rx="7.5" fill="#FDFDFD" />
    <rect x="0.753906" y="0.5" width="35" height="35" rx="7.5" stroke="#E9EAEB" />
    <path
      fill-rule="evenodd"
      clip-rule="evenodd"
      d="M28.4256 25.5655V19.1943C28.4256 18.4941 28.4229 17.7939 28.4202 17.0918L28.4202 17.0907V17.0904C28.4146 15.6802 28.4091 14.2627 28.4256 12.8231C28.4256 12.3273 28.2769 11.9802 27.9298 11.6331C26.8638 10.5919 25.773 9.52591 24.7318 8.45991C24.4095 8.13763 24.0873 8.01367 23.641 8.01367H15.6584C14.2453 8.01367 13.576 8.68302 13.576 10.0961C13.576 12.0794 13.576 14.0626 13.6008 16.0459C13.6008 16.4673 13.4768 16.6161 13.0802 16.7152C9.88217 17.4094 7.97328 20.4586 8.71701 23.6318C9.43594 26.6315 12.386 28.5156 15.4105 27.8958C15.692 27.8473 15.9658 27.7532 16.2425 27.6581C16.6726 27.5103 17.1094 27.3601 17.5921 27.3752C19.8228 27.4124 22.0675 27.4078 24.3156 27.4031H24.3158C25.0657 27.4016 25.816 27.4 26.5663 27.4C27.7315 27.4 28.4256 26.7059 28.4256 25.5655ZM19.9467 23.1613C19.7731 24.3017 19.2773 25.3181 18.4592 26.2353C18.5832 26.2601 18.6575 26.2849 18.6575 26.3097C19.5252 26.3097 20.3929 26.307 21.2606 26.3042C22.9959 26.2987 24.7313 26.2932 26.4666 26.3097C27.0368 26.3345 27.2351 26.0122 27.2351 25.5164V13.4433C27.2351 13.121 27.136 13.0219 26.8137 13.0219C26.2518 13.0384 25.6899 13.0329 25.1279 13.0274C24.847 13.0246 24.566 13.0219 24.285 13.0219C23.6157 12.9971 23.4422 12.8483 23.4422 12.179C23.4174 11.3609 23.4174 10.518 23.4422 9.69992C23.4669 9.27847 23.3182 9.2041 22.9216 9.2041H15.6083C14.8894 9.2041 14.7158 9.35285 14.7158 10.047V16.2447C14.7158 16.5422 14.7902 16.6165 15.0877 16.6661C16.1785 16.8149 17.1949 17.2363 18.013 18.0048C18.3105 18.2775 18.608 18.3519 19.0046 18.3519H24.88C25.3015 18.3519 25.6981 18.3767 25.6981 18.9221C25.6981 19.4675 25.2767 19.4675 24.8552 19.4675H23.3678H19.2525C19.5748 20.0872 19.8227 20.6574 19.8971 21.2772C19.9467 21.6243 20.0954 21.7234 20.4673 21.7234C21.4102 21.7069 22.3642 21.7124 23.3144 21.7179H23.3145H23.3145C23.7883 21.7207 24.2612 21.7234 24.7313 21.7234C24.9544 21.7234 25.1775 21.7234 25.4006 21.7978C25.6485 21.897 25.7229 22.0953 25.7229 22.3432C25.7229 22.5911 25.599 22.7894 25.351 22.839C25.1527 22.8886 24.9296 22.8886 24.7313 22.8886H20.4673C20.428 22.8886 20.388 22.8855 20.3485 22.8825C20.1631 22.8683 19.9875 22.8548 19.9467 23.1613ZM9.56055 22.2694C9.56055 19.6416 11.643 17.584 14.2956 17.584C16.9234 17.584 19.0058 19.6664 19.0058 22.2942C19.0058 24.8973 16.8986 27.0045 14.2956 27.0045C11.6182 27.0293 9.58534 24.9716 9.56055 22.2694ZM24.8361 10.3097C24.7611 10.2314 24.6847 10.1518 24.6066 10.0704C24.6066 10.6406 24.6066 11.0621 24.6314 11.4835C24.6314 11.6322 24.6314 11.781 24.8297 11.781H26.2676C25.7722 11.2856 25.333 10.8277 24.8361 10.3097Z"
      fill="#0A0D12"
    />
    <path
      d="M9.55957 22.2675C9.55957 19.6397 11.642 17.582 14.2946 17.582C16.9224 17.582 19.0048 19.6645 19.0048 22.2923C19.0048 24.8953 16.8976 27.0025 14.2946 27.0025C11.6172 27.0273 9.58436 24.9697 9.55957 22.2675Z"
      fill="#4E5BA6"
    />
    <path
      d="M20.9886 16.0949C19.6994 16.0949 18.3855 16.0949 17.0964 16.0949C16.675 16.0949 16.2783 16.0453 16.2783 15.4999C16.2783 14.9545 16.6998 14.9297 17.1212 14.9297C19.7242 14.9297 22.3025 14.9297 24.9055 14.9297C25.203 14.9297 25.5005 14.9297 25.674 15.2272C25.9219 15.6486 25.5996 16.0949 25.0047 16.0949C23.666 16.0949 22.3273 16.0949 20.9886 16.0949Z"
      fill="#0A0D12"
    />
    <path
      d="M13.6005 23.2114C14.2202 23.7816 14.7656 24.327 15.3358 24.8724C15.435 24.9716 15.6085 25.0459 15.5341 25.2195C15.4598 25.393 15.2862 25.3186 15.1623 25.3434C14.5177 25.4178 14.0467 25.2443 13.6253 24.7484C13.2038 24.2774 12.7576 23.8808 12.2866 23.4593C12.0387 23.2362 11.9891 23.0131 11.9643 22.6908C11.9643 22.3189 12.0882 22.195 12.4601 22.195C12.8815 22.195 13.3278 22.2942 13.7492 22.0958C13.9723 21.9967 14.2202 21.7983 14.1707 21.5504C14.1211 21.3025 13.8236 21.4513 13.6253 21.4265C13.2038 21.4017 12.8072 21.4017 12.3857 21.4265C12.0387 21.4513 11.9643 21.3025 11.9643 20.9802C11.9643 20.6828 12.0387 20.534 12.3857 20.534C12.9063 20.5588 13.4269 20.534 14.0219 20.534C13.5757 19.9886 13.0303 20.2365 12.5841 20.1622C12.3857 20.1374 12.113 20.2861 12.0139 19.9638C11.8403 19.5176 12.0387 19.1953 12.5345 19.1953C13.6005 19.1953 14.6913 19.1953 15.7573 19.1953C16.1043 19.1953 16.2779 19.2697 16.2779 19.6663C16.2779 20.0878 16.0548 20.063 15.7573 20.0878C15.5837 20.0878 15.2367 19.9142 15.2367 20.2613C15.2367 20.6084 15.5837 20.534 15.8068 20.534C16.1043 20.5092 16.2779 20.5836 16.2779 20.9307C16.2779 21.2529 16.2035 21.4513 15.8316 21.4017C15.6333 21.3769 15.4102 21.3273 15.3606 21.6496C15.1375 22.5916 14.5177 23.0131 13.6005 23.2114Z"
      fill="#FDFDFD"
    />
  </svg>
);

export const messageIcon = () => (
  <svg width="17" height="16" viewBox="0 0 17 16" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path
      fill-rule="evenodd"
      clip-rule="evenodd"
      d="M8.25358 6.66797C8.62177 6.66797 8.92025 6.96645 8.92025 7.33464V7.3413C8.92025 7.70949 8.62177 8.00797 8.25358 8.00797C7.88539 8.00797 7.58691 7.70949 7.58691 7.3413V7.33464C7.58691 6.96645 7.88539 6.66797 8.25358 6.66797Z"
      fill="#363F72"
    />
    <path
      fill-rule="evenodd"
      clip-rule="evenodd"
      d="M5.58757 6.66797C5.95575 6.66797 6.25423 6.96645 6.25423 7.33464V7.3413C6.25423 7.70949 5.95575 8.00797 5.58757 8.00797C5.21938 8.00797 4.9209 7.70949 4.9209 7.3413V7.33464C4.9209 6.96645 5.21938 6.66797 5.58757 6.66797Z"
      fill="#363F72"
    />
    <path
      fill-rule="evenodd"
      clip-rule="evenodd"
      d="M10.9206 6.66797C11.2888 6.66797 11.5872 6.96645 11.5872 7.33464V7.3413C11.5872 7.70949 11.2888 8.00797 10.9206 8.00797C10.5524 8.00797 10.2539 7.70949 10.2539 7.3413V7.33464C10.2539 6.96645 10.5524 6.66797 10.9206 6.66797Z"
      fill="#363F72"
    />
    <path
      fill-rule="evenodd"
      clip-rule="evenodd"
      d="M4.25358 3.33333C3.89996 3.33333 3.56082 3.47381 3.31077 3.72386C3.06072 3.97391 2.92025 4.31304 2.92025 4.66667V10C2.92025 10.3536 3.06072 10.6928 3.31077 10.9428C3.56082 11.1929 3.89996 11.3333 4.25358 11.3333H5.58691C5.9551 11.3333 6.25358 11.6318 6.25358 12V12.8225L8.57725 11.4283C8.68086 11.3662 8.79942 11.3333 8.92025 11.3333H12.2536C12.6072 11.3333 12.9463 11.1929 13.1964 10.9428C13.4464 10.6928 13.5869 10.3536 13.5869 10V4.66667C13.5869 4.31304 13.4464 3.97391 13.1964 3.72386C12.9463 3.47381 12.6072 3.33333 12.2536 3.33333H4.25358ZM2.36796 2.78105C2.86806 2.28095 3.54634 2 4.25358 2H12.2536C12.9608 2 13.6391 2.28095 14.1392 2.78105C14.6393 3.28115 14.9202 3.95942 14.9202 4.66667V10C14.9202 10.7072 14.6393 11.3855 14.1392 11.8856C13.6391 12.3857 12.9608 12.6667 12.2536 12.6667H9.1049L5.92991 14.5717C5.72396 14.6952 5.46745 14.6985 5.25845 14.5801C5.04944 14.4618 4.92025 14.2402 4.92025 14V12.6667H4.25358C3.54634 12.6667 2.86806 12.3857 2.36796 11.8856C1.86787 11.3855 1.58691 10.7072 1.58691 10V4.66667C1.58691 3.95942 1.86787 3.28115 2.36796 2.78105Z"
      fill="#363F72"
    />
  </svg>
);

export const phoneIcon = () => (
  <svg width="21" height="20" viewBox="0 0 21 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path
      fill-rule="evenodd"
      clip-rule="evenodd"
      d="M4.66667 3.95703C4.3904 3.95703 4.12545 4.06678 3.9301 4.26213C3.73875 4.45347 3.62954 4.71159 3.62514 4.98172C3.81806 8.07219 5.13275 10.9862 7.32263 13.1761C9.51252 15.366 12.4265 16.6806 15.517 16.8736C15.7871 16.8692 16.0452 16.7599 16.2366 16.5686C16.4319 16.3733 16.5417 16.1083 16.5417 15.832V12.9218L13.2621 11.61L12.2859 13.2369C12.1186 13.5158 11.7653 13.6198 11.4736 13.4759C9.54056 12.5226 7.97611 10.9581 7.0228 9.02514C6.87892 8.73342 6.98286 8.38012 7.26177 8.21277L8.88869 7.23662L7.57685 3.95703H4.66667ZM3.04621 3.37824C3.47598 2.94847 4.05888 2.70703 4.66667 2.70703H8C8.25557 2.70703 8.48538 2.86263 8.5803 3.09991L10.247 7.26658C10.3609 7.5514 10.2513 7.8768 9.98823 8.03463L8.41508 8.97852C9.16174 10.2664 10.2323 11.337 11.5202 12.0836L12.4641 10.5105C12.6219 10.2474 12.9473 10.1378 13.2321 10.2517L17.3988 11.9184C17.6361 12.0133 17.7917 12.2431 17.7917 12.4987V15.832C17.7917 16.4398 17.5502 17.0227 17.1205 17.4525C16.6907 17.8823 16.1078 18.1237 15.5 18.1237C15.4874 18.1237 15.4747 18.1233 15.4621 18.1225C12.0591 17.9157 8.84946 16.4707 6.43875 14.0599C4.02804 11.6492 2.58295 8.4396 2.37615 5.03661C2.37538 5.02399 2.375 5.01134 2.375 4.9987C2.375 4.39091 2.61644 3.80802 3.04621 3.37824Z"
      fill="#4E5BA6"
    />
  </svg>
);

export const mailIcon = () => (
  <svg width="21" height="20" viewBox="0 0 21 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path
      fill-rule="evenodd"
      clip-rule="evenodd"
      d="M4.66667 4.79297C4.3904 4.79297 4.12545 4.90272 3.9301 5.09807C3.80832 5.21984 3.71981 5.36867 3.67041 5.53042L10.5 10.0835L17.3296 5.53042C17.2802 5.36867 17.1917 5.21984 17.0699 5.09807C16.8746 4.90272 16.6096 4.79297 16.3333 4.79297H4.66667ZM17.375 7.00246L10.8467 11.3547C10.6368 11.4946 10.3632 11.4946 10.1533 11.3547L3.625 7.00246V14.168C3.625 14.4442 3.73475 14.7092 3.9301 14.9045C4.12545 15.0999 4.3904 15.2096 4.66667 15.2096H16.3333C16.6096 15.2096 16.8746 15.0999 17.0699 14.9045C17.2653 14.7092 17.375 14.4442 17.375 14.168V7.00246ZM3.04621 4.21418C3.47598 3.78441 4.05888 3.54297 4.66667 3.54297H16.3333C16.9411 3.54297 17.524 3.78441 17.9538 4.21418C18.3836 4.64395 18.625 5.22685 18.625 5.83464V14.168C18.625 14.7758 18.3836 15.3587 17.9538 15.7884C17.524 16.2182 16.9411 16.4596 16.3333 16.4596H4.66667C4.05888 16.4596 3.47598 16.2182 3.04621 15.7884C2.61644 15.3587 2.375 14.7758 2.375 14.168V5.83464C2.375 5.22685 2.61644 4.64395 3.04621 4.21418Z"
      fill="#4E5BA6"
    />
  </svg>
);

export const backIcon = () => (
  <svg width="25" height="24" viewBox="0 0 25 24" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path
      fill-rule="evenodd"
      clip-rule="evenodd"
      d="M12.0303 5.46967C12.3232 5.76256 12.3232 6.23744 12.0303 6.53033L7.31066 11.25H19.5C19.9142 11.25 20.25 11.5858 20.25 12C20.25 12.4142 19.9142 12.75 19.5 12.75H7.31066L12.0303 17.4697C12.3232 17.7626 12.3232 18.2374 12.0303 18.5303C11.7374 18.8232 11.2626 18.8232 10.9697 18.5303L4.96967 12.5303C4.67678 12.2374 4.67678 11.7626 4.96967 11.4697L10.9697 5.46967C11.2626 5.17678 11.7374 5.17678 12.0303 5.46967Z"
      fill="#0A0D12"
    />
  </svg>
);

export const orgIcon = () => (
  <svg width="16" height="15" viewBox="0 0 16 15" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path
      fill-rule="evenodd"
      clip-rule="evenodd"
      d="M2.26126 1.26126C2.78871 0.733816 3.50408 0.4375 4.25 0.4375H7.25C7.99592 0.4375 8.71129 0.733816 9.23874 1.26126C9.76618 1.78871 10.0625 2.50408 10.0625 3.25V13.1875H12.6875V11.2343C12.3518 11.1392 12.0427 10.9595 11.7916 10.7084C11.4048 10.3216 11.1875 9.79701 11.1875 9.25V7.75C11.1875 7.20299 11.4048 6.67839 11.7916 6.29159C12.1784 5.9048 12.703 5.6875 13.25 5.6875C13.797 5.6875 14.3216 5.9048 14.7084 6.29159C15.0952 6.67839 15.3125 7.20299 15.3125 7.75V9.25C15.3125 9.79701 15.0952 10.3216 14.7084 10.7084C14.4573 10.9595 14.1482 11.1392 13.8125 11.2343V13.1875H14.75C15.0607 13.1875 15.3125 13.4393 15.3125 13.75C15.3125 14.0607 15.0607 14.3125 14.75 14.3125H1.25C0.93934 14.3125 0.6875 14.0607 0.6875 13.75C0.6875 13.4393 0.93934 13.1875 1.25 13.1875H1.4375V3.25C1.4375 2.50408 1.73382 1.78871 2.26126 1.26126ZM2.5625 13.1875H5.1875V10.75C5.1875 10.4393 5.43934 10.1875 5.75 10.1875C6.06066 10.1875 6.3125 10.4393 6.3125 10.75V13.1875H8.9375V3.25C8.9375 2.80245 8.75971 2.37322 8.44324 2.05676C8.12678 1.74029 7.69755 1.5625 7.25 1.5625H4.25C3.80245 1.5625 3.37322 1.74029 3.05676 2.05676C2.74029 2.37323 2.5625 2.80245 2.5625 3.25V13.1875ZM4.4375 4.75C4.4375 4.43934 4.68934 4.1875 5 4.1875H6.5C6.81066 4.1875 7.0625 4.43934 7.0625 4.75C7.0625 5.06066 6.81066 5.3125 6.5 5.3125H5C4.68934 5.3125 4.4375 5.06066 4.4375 4.75ZM13.25 6.8125C13.0014 6.8125 12.7629 6.91127 12.5871 7.08709C12.4113 7.2629 12.3125 7.50136 12.3125 7.75V9.25C12.3125 9.49864 12.4113 9.7371 12.5871 9.91291C12.7629 10.0887 13.0014 10.1875 13.25 10.1875C13.4986 10.1875 13.7371 10.0887 13.9129 9.91291C14.0887 9.7371 14.1875 9.49864 14.1875 9.25V7.75C14.1875 7.50136 14.0887 7.2629 13.9129 7.08709C13.7371 6.91127 13.4986 6.8125 13.25 6.8125ZM4.4375 7.75C4.4375 7.43934 4.68934 7.1875 5 7.1875H6.5C6.81066 7.1875 7.0625 7.43934 7.0625 7.75C7.0625 8.06066 6.81066 8.3125 6.5 8.3125H5C4.68934 8.3125 4.4375 8.06066 4.4375 7.75Z"
      fill="#717680"
    />
  </svg>
);

export const editIcon = () => (
  <svg width="25" height="24" viewBox="0 0 25 24" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path
      fill-rule="evenodd"
      clip-rule="evenodd"
      d="M14.4697 4.96997C15.1408 4.29889 16.0509 3.92188 17 3.92188C17.9491 3.92188 18.8592 4.29889 19.5303 4.96997C20.2014 5.64106 20.5784 6.55124 20.5784 7.5003C20.5784 8.44936 20.2014 9.35955 19.5303 10.0306L18.5316 11.0294C18.5312 11.0298 18.5307 11.0302 18.5303 11.0306C18.5299 11.0311 18.5295 11.0315 18.5291 11.0319L9.03033 20.5306C8.88968 20.6713 8.69891 20.7503 8.5 20.7503H4.5C4.08579 20.7503 3.75 20.4145 3.75 20.0003V16.0003C3.75 15.8014 3.82902 15.6106 3.96967 15.47L14.4697 4.96997ZM14 7.56096L5.25 16.311V19.2503H8.18934L16.9393 10.5003L14 7.56096ZM18 9.43964L15.0607 6.5003L15.5303 6.03063C15.9201 5.64085 16.4488 5.42188 17 5.42188C17.5512 5.42188 18.0799 5.64085 18.4697 6.03063C18.8595 6.42041 19.0784 6.94907 19.0784 7.5003C19.0784 8.05154 18.8595 8.58019 18.4697 8.96997L18 9.43964Z"
      fill="#717680"
    />
  </svg>
);

export const deleteIcon = () => (
  <svg width="25" height="24" viewBox="0 0 25 24" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path
      fill-rule="evenodd"
      clip-rule="evenodd"
      d="M10.5 3.75C10.4337 3.75 10.3701 3.77634 10.3232 3.82322C10.2763 3.87011 10.25 3.9337 10.25 4V6.25H14.75V4C14.75 3.9337 14.7237 3.87011 14.6768 3.82322C14.6299 3.77634 14.5663 3.75 14.5 3.75H10.5ZM16.25 6.25V4C16.25 3.53587 16.0656 3.09075 15.7374 2.76256C15.4092 2.43437 14.9641 2.25 14.5 2.25H10.5C10.0359 2.25 9.59075 2.43437 9.26256 2.76256C8.93437 3.09075 8.75 3.53587 8.75 4V6.25H5.50877C5.50349 6.24994 5.4982 6.24994 5.4929 6.25H4.5C4.08579 6.25 3.75 6.58579 3.75 7C3.75 7.41421 4.08579 7.75 4.5 7.75H4.8099L5.75021 19.0337C5.75898 19.7508 6.04767 20.4368 6.55546 20.9445C7.07118 21.4603 7.77065 21.75 8.5 21.75H16.5C17.2293 21.75 17.9288 21.4603 18.4445 20.9445C18.9523 20.4368 19.241 19.7508 19.2498 19.0337L20.1901 7.75H20.5C20.9142 7.75 21.25 7.41421 21.25 7C21.25 6.58579 20.9142 6.25 20.5 6.25H19.5071C19.5018 6.24994 19.4965 6.24994 19.4912 6.25H16.25ZM6.3151 7.75L7.24741 18.9377C7.24914 18.9584 7.25 18.9792 7.25 19C7.25 19.3315 7.3817 19.6495 7.61612 19.8839C7.85054 20.1183 8.16848 20.25 8.5 20.25H16.5C16.8315 20.25 17.1495 20.1183 17.3839 19.8839C17.6183 19.6495 17.75 19.3315 17.75 19C17.75 18.9792 17.7509 18.9584 17.7526 18.9377L18.6849 7.75H6.3151ZM10.5 10.25C10.9142 10.25 11.25 10.5858 11.25 11V17C11.25 17.4142 10.9142 17.75 10.5 17.75C10.0858 17.75 9.75 17.4142 9.75 17V11C9.75 10.5858 10.0858 10.25 10.5 10.25ZM14.5 10.25C14.9142 10.25 15.25 10.5858 15.25 11V17C15.25 17.4142 14.9142 17.75 14.5 17.75C14.0858 17.75 13.75 17.4142 13.75 17V11C13.75 10.5858 14.0858 10.25 14.5 10.25Z"
      fill="#717680"
    />
  </svg>
);

export const copyIcon = () => (
  <svg width="21" height="20" viewBox="0 0 21 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path
      fill-rule="evenodd"
      clip-rule="evenodd"
      d="M3.88215 3.37824C4.31192 2.94847 4.89482 2.70703 5.5026 2.70703H12.1693C12.7771 2.70703 13.36 2.94847 13.7897 3.37824C14.2195 3.80802 14.4609 4.39091 14.4609 4.9987V6.04036H15.5026C16.1104 6.04036 16.6933 6.28181 17.1231 6.71158C17.5528 7.14135 17.7943 7.72424 17.7943 8.33203V14.9987C17.7943 15.6065 17.5528 16.1894 17.1231 16.6191C16.6933 17.0489 16.1104 17.2904 15.5026 17.2904H8.83594C8.22815 17.2904 7.64526 17.0489 7.21548 16.6191C6.78571 16.1894 6.54427 15.6065 6.54427 14.9987V13.957H5.5026C4.89482 13.957 4.31192 13.7156 3.88215 13.2858C3.45238 12.856 3.21094 12.2732 3.21094 11.6654V4.9987C3.21094 4.39091 3.45238 3.80802 3.88215 3.37824ZM7.79427 14.9987C7.79427 15.275 7.90402 15.5399 8.09937 15.7353C8.29472 15.9306 8.55967 16.0404 8.83594 16.0404H15.5026C15.7789 16.0404 16.0438 15.9306 16.2392 15.7353C16.4345 15.5399 16.5443 15.275 16.5443 14.9987V8.33203C16.5443 8.05576 16.4345 7.79081 16.2392 7.59546C16.0438 7.40011 15.7789 7.29036 15.5026 7.29036H8.83594C8.55967 7.29036 8.29472 7.40011 8.09937 7.59546C7.90402 7.79081 7.79427 8.05576 7.79427 8.33203V14.9987ZM13.2109 6.04036H8.83594C8.22815 6.04036 7.64526 6.28181 7.21548 6.71158C6.78571 7.14135 6.54427 7.72424 6.54427 8.33203V12.707H5.5026C5.22634 12.707 4.96139 12.5973 4.76603 12.4019C4.57068 12.2066 4.46094 11.9416 4.46094 11.6654V4.9987C4.46094 4.72243 4.57068 4.45748 4.76603 4.26213C4.96138 4.06678 5.22634 3.95703 5.5026 3.95703H12.1693C12.4455 3.95703 12.7105 4.06678 12.9058 4.26213C13.1012 4.45748 13.2109 4.72243 13.2109 4.9987V6.04036Z"
      fill="#717680"
    />
  </svg>
);

export const threeDotIcon = () => (
  <svg width="25" height="24" viewBox="0 0 25 24" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path
      fill-rule="evenodd"
      clip-rule="evenodd"
      d="M11.2626 3.76256C11.5908 3.43438 12.0359 3.25 12.5 3.25C12.9641 3.25 13.4092 3.43438 13.7374 3.76256C14.0656 4.09075 14.25 4.53587 14.25 5C14.25 5.46413 14.0656 5.90925 13.7374 6.23744C13.4092 6.56562 12.9641 6.75 12.5 6.75C12.0359 6.75 11.5908 6.56562 11.2626 6.23744C10.9344 5.90925 10.75 5.46413 10.75 5C10.75 4.53587 10.9344 4.09075 11.2626 3.76256ZM12.5 4.75C12.4337 4.75 12.3701 4.77634 12.3232 4.82322C12.2763 4.87011 12.25 4.9337 12.25 5C12.25 5.0663 12.2763 5.12989 12.3232 5.17678C12.3701 5.22366 12.4337 5.25 12.5 5.25C12.5663 5.25 12.6299 5.22366 12.6768 5.17678C12.7237 5.12989 12.75 5.0663 12.75 5C12.75 4.9337 12.7237 4.87011 12.6768 4.82322C12.6299 4.77634 12.5663 4.75 12.5 4.75ZM11.2626 10.7626C11.5908 10.4344 12.0359 10.25 12.5 10.25C12.9641 10.25 13.4092 10.4344 13.7374 10.7626C14.0656 11.0908 14.25 11.5359 14.25 12C14.25 12.4641 14.0656 12.9092 13.7374 13.2374C13.4092 13.5656 12.9641 13.75 12.5 13.75C12.0359 13.75 11.5908 13.5656 11.2626 13.2374C10.9344 12.9092 10.75 12.4641 10.75 12C10.75 11.5359 10.9344 11.0908 11.2626 10.7626ZM12.5 11.75C12.4337 11.75 12.3701 11.7763 12.3232 11.8232C12.2763 11.8701 12.25 11.9337 12.25 12C12.25 12.0663 12.2763 12.1299 12.3232 12.1768C12.3701 12.2237 12.4337 12.25 12.5 12.25C12.5663 12.25 12.6299 12.2237 12.6768 12.1768C12.7237 12.1299 12.75 12.0663 12.75 12C12.75 11.9337 12.7237 11.8701 12.6768 11.8232C12.6299 11.7763 12.5663 11.75 12.5 11.75ZM11.2626 17.7626C11.5908 17.4344 12.0359 17.25 12.5 17.25C12.9641 17.25 13.4092 17.4344 13.7374 17.7626C14.0656 18.0908 14.25 18.5359 14.25 19C14.25 19.4641 14.0656 19.9092 13.7374 20.2374C13.4092 20.5656 12.9641 20.75 12.5 20.75C12.0359 20.75 11.5908 20.5656 11.2626 20.2374C10.9344 19.9092 10.75 19.4641 10.75 19C10.75 18.5359 10.9344 18.0908 11.2626 17.7626ZM12.5 18.75C12.4337 18.75 12.3701 18.7763 12.3232 18.8232C12.2763 18.8701 12.25 18.9337 12.25 19C12.25 19.0663 12.2763 19.1299 12.3232 19.1768C12.3701 19.2237 12.4337 19.25 12.5 19.25C12.5663 19.25 12.6299 19.2237 12.6768 19.1768C12.7237 19.1299 12.75 19.0663 12.75 19C12.75 18.9337 12.7237 18.8701 12.6768 18.8232C12.6299 18.7763 12.5663 18.75 12.5 18.75Z"
      fill="#717680"
    />
  </svg>
);

export const closeIcon = () => (
  <svg width="19" height="18" viewBox="0 0 19 18" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path
      fill-rule="evenodd"
      clip-rule="evenodd"
      d="M4.60225 4.10225C4.82192 3.88258 5.17808 3.88258 5.39775 4.10225L9.5 8.20451L13.6023 4.10225C13.8219 3.88258 14.1781 3.88258 14.3977 4.10225C14.6174 4.32192 14.6174 4.67808 14.3977 4.89775L10.2955 9L14.3977 13.1023C14.6174 13.3219 14.6174 13.6781 14.3977 13.8977C14.1781 14.1174 13.8219 14.1174 13.6023 13.8977L9.5 9.79549L5.39775 13.8977C5.17808 14.1174 4.82192 14.1174 4.60225 13.8977C4.38258 13.6781 4.38258 13.3219 4.60225 13.1023L8.70451 9L4.60225 4.89775C4.38258 4.67808 4.38258 4.32192 4.60225 4.10225Z"
      fill="#FDFDFD"
    />
  </svg>
);

export const removeUser = () => (
  <svg width="19" height="20" viewBox="0 0 19 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path
      fill-rule="evenodd"
      clip-rule="evenodd"
      d="M4.14124 1.64124C5.03204 0.750445 6.24022 0.25 7.5 0.25C8.75978 0.25 9.96796 0.750445 10.8588 1.64124C11.7496 2.53204 12.25 3.74022 12.25 5C12.25 6.25978 11.7496 7.46796 10.8588 8.35876C9.96796 9.24956 8.75978 9.75 7.5 9.75C6.24022 9.75 5.03204 9.24956 4.14124 8.35876C3.25044 7.46796 2.75 6.25978 2.75 5C2.75 3.74022 3.25044 2.53204 4.14124 1.64124ZM7.5 1.75C6.63805 1.75 5.8114 2.09241 5.2019 2.7019C4.59241 3.3114 4.25 4.13805 4.25 5C4.25 5.86195 4.59241 6.6886 5.2019 7.2981C5.8114 7.90759 6.63805 8.25 7.5 8.25C8.36195 8.25 9.1886 7.90759 9.7981 7.2981C10.4076 6.6886 10.75 5.86195 10.75 5C10.75 4.13805 10.4076 3.3114 9.7981 2.7019C9.1886 2.09241 8.36195 1.75 7.5 1.75ZM5.5 13.75C4.63805 13.75 3.8114 14.0924 3.2019 14.7019C2.59241 15.3114 2.25 16.138 2.25 17V19C2.25 19.4142 1.91421 19.75 1.5 19.75C1.08579 19.75 0.75 19.4142 0.75 19V17C0.75 15.7402 1.25044 14.532 2.14124 13.6412C3.03204 12.7504 4.24022 12.25 5.5 12.25H9.5C9.91294 12.25 10.3136 12.3034 10.6957 12.4016C11.0968 12.5047 11.3385 12.9135 11.2354 13.3147C11.1323 13.7158 10.7235 13.9575 10.3223 13.8544C10.0584 13.7866 9.78306 13.75 9.5 13.75H5.5ZM10.75 17C10.75 16.5858 11.0858 16.25 11.5 16.25H17.5C17.9142 16.25 18.25 16.5858 18.25 17C18.25 17.4142 17.9142 17.75 17.5 17.75H11.5C11.0858 17.75 10.75 17.4142 10.75 17Z"
      fill="#717680"
    />
  </svg>
);

export const lockIcon = () => (
  <svg width="25" height="24" viewBox="0 0 25 24" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path
      fill-rule="evenodd"
      clip-rule="evenodd"
      d="M12.5 3.75C11.638 3.75 10.8114 4.09241 10.2019 4.7019C9.59241 5.3114 9.25 6.13805 9.25 7V10.25H15.75V7C15.75 6.13805 15.4076 5.3114 14.7981 4.7019C14.1886 4.09241 13.362 3.75 12.5 3.75ZM17.25 10.25V7C17.25 5.74022 16.7496 4.53204 15.8588 3.64124C14.968 2.75044 13.7598 2.25 12.5 2.25C11.2402 2.25 10.032 2.75044 9.14124 3.64124C8.25044 4.53204 7.75 5.74022 7.75 7V10.25H7.5C6.77065 10.25 6.07118 10.5397 5.55546 11.0555C5.03973 11.5712 4.75 12.2707 4.75 13V19C4.75 19.7293 5.03973 20.4288 5.55546 20.9445C6.07118 21.4603 6.77065 21.75 7.5 21.75H15.5C15.9142 21.75 16.25 21.4142 16.25 21C16.25 20.5858 15.9142 20.25 15.5 20.25H7.5C7.16848 20.25 6.85054 20.1183 6.61612 19.8839C6.3817 19.6495 6.25 19.3315 6.25 19V13C6.25 12.6685 6.3817 12.3505 6.61612 12.1161C6.85054 11.8817 7.16848 11.75 7.5 11.75H17.5C17.7197 11.7499 17.9359 11.8078 18.1262 11.9177C18.3165 12.0277 18.4744 12.1858 18.584 12.3763C18.7907 12.7352 19.2493 12.8586 19.6082 12.652C19.9672 12.4453 20.0906 11.9867 19.884 11.6277C19.6427 11.2088 19.2953 10.8608 18.8767 10.6189C18.4582 10.3771 17.9833 10.2499 17.5 10.25C17.4999 10.25 17.5001 10.25 17.5 10.25H17.25ZM11.2626 14.7626C11.5908 14.4344 12.0359 14.25 12.5 14.25C12.9641 14.25 13.4092 14.4344 13.7374 14.7626C14.0656 15.0908 14.25 15.5359 14.25 16C14.25 16.4641 14.0656 16.9092 13.7374 17.2374C13.4092 17.5656 12.9641 17.75 12.5 17.75C12.0359 17.75 11.5908 17.5656 11.2626 17.2374C10.9344 16.9092 10.75 16.4641 10.75 16C10.75 15.5359 10.9344 15.0908 11.2626 14.7626ZM12.5 15.75C12.4337 15.75 12.3701 15.7763 12.3232 15.8232C12.2763 15.8701 12.25 15.9337 12.25 16C12.25 16.0663 12.2763 16.1299 12.3232 16.1768C12.3701 16.2237 12.4337 16.25 12.5 16.25C12.5663 16.25 12.6299 16.2237 12.6768 16.1768C12.7237 16.1299 12.75 16.0663 12.75 16C12.75 15.9337 12.7237 15.8701 12.6768 15.8232C12.6299 15.7763 12.5663 15.75 12.5 15.75ZM19.5 15.25C19.9142 15.25 20.25 15.5858 20.25 16V19C20.25 19.4142 19.9142 19.75 19.5 19.75C19.0858 19.75 18.75 19.4142 18.75 19V16C18.75 15.5858 19.0858 15.25 19.5 15.25ZM19.5 21.25C19.9142 21.25 20.25 21.5858 20.25 22V22.01C20.25 22.4242 19.9142 22.76 19.5 22.76C19.0858 22.76 18.75 22.4242 18.75 22.01V22C18.75 21.5858 19.0858 21.25 19.5 21.25Z"
      fill="#717680"
    />
  </svg>
);

export const superUserIcon = () => (
  <svg width="25" height="24" viewBox="0 0 25 24" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path
      fill-rule="evenodd"
      clip-rule="evenodd"
      d="M9.14124 3.64124C10.032 2.75044 11.2402 2.25 12.5 2.25C13.7598 2.25 14.968 2.75044 15.8588 3.64124C16.7496 4.53204 17.25 5.74022 17.25 7C17.25 8.25978 16.7496 9.46796 15.8588 10.3588C14.968 11.2496 13.7598 11.75 12.5 11.75C11.2402 11.75 10.032 11.2496 9.14124 10.3588C8.25044 9.46796 7.75 8.25978 7.75 7C7.75 5.74022 8.25044 4.53204 9.14124 3.64124ZM12.5 3.75C11.638 3.75 10.8114 4.09241 10.2019 4.7019C9.59241 5.3114 9.25 6.13805 9.25 7C9.25 7.86195 9.59241 8.6886 10.2019 9.2981C10.8114 9.90759 11.638 10.25 12.5 10.25C13.362 10.25 14.1886 9.90759 14.7981 9.2981C15.4076 8.6886 15.75 7.86195 15.75 7C15.75 6.13805 15.4076 5.3114 14.7981 4.7019C14.1886 4.09241 13.362 3.75 12.5 3.75ZM10.5 15.75C9.63805 15.75 8.8114 16.0924 8.2019 16.7019C7.59241 17.3114 7.25 18.138 7.25 19V21C7.25 21.4142 6.91421 21.75 6.5 21.75C6.08579 21.75 5.75 21.4142 5.75 21V19C5.75 17.7402 6.25044 16.532 7.14124 15.6412C8.03204 14.7504 9.24022 14.25 10.5 14.25H14.5C14.8156 14.25 15.1265 14.2807 15.427 14.3403C15.8333 14.421 16.0973 14.8157 16.0167 15.222C15.936 15.6283 15.5413 15.8923 15.135 15.8117C14.9315 15.7713 14.7184 15.75 14.5 15.75H10.5ZM19.916 15.376C20.2607 15.6057 20.3538 16.0714 20.124 16.416L18.9014 18.25H21.5C21.7766 18.25 22.0307 18.4022 22.1613 18.6461C22.2918 18.89 22.2775 19.1859 22.124 19.416L20.124 22.416C19.8943 22.7607 19.4286 22.8538 19.084 22.624C18.7393 22.3943 18.6462 21.9286 18.876 21.584L20.0986 19.75H17.5C17.2234 19.75 16.9693 19.5978 16.8387 19.3539C16.7082 19.11 16.7225 18.8141 16.876 18.584L18.876 15.584C19.1057 15.2393 19.5714 15.1462 19.916 15.376Z"
      fill="#717680"
    />
  </svg>
);

export const orLineSvg = () => (
  <svg width="815" height="27" viewBox="0 0 815 27" fill="none" xmlns="http://www.w3.org/2000/svg">
    <rect width="370.5" height="1" transform="translate(0 13)" fill="#A4A7AE" />
    <path
      d="M401.853 19.728C400.574 19.728 399.446 19.443 398.471 18.873C397.508 18.2903 396.755 17.486 396.21 16.46C395.678 15.4213 395.412 14.218 395.412 12.85C395.412 11.4947 395.678 10.304 396.21 9.278C396.755 8.23933 397.508 7.42867 398.471 6.846C399.446 6.26333 400.574 5.972 401.853 5.972C403.158 5.972 404.291 6.26333 405.254 6.846C406.217 7.42867 406.964 8.23933 407.496 9.278C408.041 10.304 408.313 11.4947 408.313 12.85C408.313 14.218 408.041 15.4213 407.496 16.46C406.964 17.486 406.217 18.2903 405.254 18.873C404.291 19.443 403.158 19.728 401.853 19.728ZM401.853 18.322C402.816 18.322 403.658 18.1067 404.38 17.676C405.115 17.2327 405.678 16.6057 406.071 15.795C406.476 14.9717 406.679 13.99 406.679 12.85C406.679 11.71 406.476 10.7347 406.071 9.924C405.678 9.10067 405.115 8.47367 404.38 8.043C403.658 7.61233 402.816 7.397 401.853 7.397C400.903 7.397 400.061 7.61233 399.326 8.043C398.604 8.47367 398.04 9.10067 397.635 9.924C397.242 10.7347 397.046 11.71 397.046 12.85C397.046 13.99 397.242 14.9717 397.635 15.795C398.04 16.6057 398.604 17.2327 399.326 17.676C400.061 18.1067 400.903 18.322 401.853 18.322ZM410.639 19.5V6.2H414.933C415.972 6.2 416.82 6.37733 417.479 6.732C418.138 7.074 418.625 7.53633 418.942 8.119C419.259 8.70167 419.417 9.36033 419.417 10.095C419.417 10.8043 419.252 11.4567 418.923 12.052C418.606 12.6473 418.112 13.1223 417.441 13.477C416.77 13.8317 415.908 14.009 414.857 14.009H412.235V19.5H410.639ZM417.669 19.5L414.743 13.553H416.529L419.55 19.5H417.669ZM412.235 12.755H414.819C415.832 12.755 416.573 12.508 417.042 12.014C417.523 11.52 417.764 10.8867 417.764 10.114C417.764 9.32867 417.53 8.708 417.061 8.252C416.605 7.78333 415.851 7.549 414.8 7.549H412.235V12.755Z"
      fill="#A4A7AE"
    />
    <rect width="370.5" height="1" transform="translate(444.5 13)" fill="#A4A7AE" />
  </svg>
);

export const deactiveUserIcon = () => (
  <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path
      fill-rule="evenodd"
      clip-rule="evenodd"
      d="M8.64124 3.64124C9.53204 2.75044 10.7402 2.25 12 2.25C13.2598 2.25 14.468 2.75044 15.3588 3.64124C16.2496 4.53204 16.75 5.74022 16.75 7C16.75 8.25978 16.2496 9.46796 15.3588 10.3588C14.468 11.2496 13.2598 11.75 12 11.75C10.7402 11.75 9.53204 11.2496 8.64124 10.3588C7.75044 9.46796 7.25 8.25978 7.25 7C7.25 5.74022 7.75044 4.53204 8.64124 3.64124ZM12 3.75C11.138 3.75 10.3114 4.09241 9.7019 4.7019C9.09241 5.3114 8.75 6.13805 8.75 7C8.75 7.86195 9.09241 8.6886 9.7019 9.2981C10.3114 9.90759 11.138 10.25 12 10.25C12.862 10.25 13.6886 9.90759 14.2981 9.2981C14.9076 8.6886 15.25 7.86195 15.25 7C15.25 6.13805 14.9076 5.3114 14.2981 4.7019C13.6886 4.09241 12.862 3.75 12 3.75ZM10 15.75C9.13805 15.75 8.3114 16.0924 7.7019 16.7019C7.09241 17.3114 6.75 18.138 6.75 19V21C6.75 21.4142 6.41421 21.75 6 21.75C5.58579 21.75 5.25 21.4142 5.25 21V19C5.25 17.7402 5.75044 16.532 6.64124 15.6412C7.53204 14.7504 8.74022 14.25 10 14.25H14C14.4129 14.25 14.8136 14.3034 15.1957 14.4016C15.5968 14.5047 15.8385 14.9135 15.7354 15.3147C15.6323 15.7158 15.2235 15.9575 14.8223 15.8544C14.5584 15.7866 14.2831 15.75 14 15.75H10ZM15.25 19C15.25 18.5858 15.5858 18.25 16 18.25H22C22.4142 18.25 22.75 18.5858 22.75 19C22.75 19.4142 22.4142 19.75 22 19.75H16C15.5858 19.75 15.25 19.4142 15.25 19Z"
      fill="#717680"
    />
  </svg>
);

export const ticketLogIcon = () => (
  <svg width="33" height="34" viewBox="0 0 33 34" fill="none" xmlns="http://www.w3.org/2000/svg">
    <rect x="0.444799" y="1.43198" width="31.1359" height="31.1359" rx="6.67199" fill="#FDFDFD" />
    <rect
      x="0.444799"
      y="1.43198"
      width="31.1359"
      height="31.1359"
      rx="6.67199"
      stroke="#E9EAEB"
      stroke-width="0.889598"
    />
    <path
      fill-rule="evenodd"
      clip-rule="evenodd"
      d="M25.0619 23.7305V18.0627C25.0619 17.4398 25.0595 16.8169 25.057 16.1923V16.1919V16.1914V16.1912V16.191C25.0521 14.9365 25.0472 13.6755 25.0619 12.3949C25.0619 11.9538 24.9296 11.6451 24.6208 11.3363C23.6725 10.4101 22.7021 9.46174 21.7759 8.51342C21.4892 8.22672 21.2025 8.11646 20.8055 8.11646H13.7042C12.4471 8.11646 11.8517 8.71191 11.8517 9.96897V9.96909C11.8517 11.7334 11.8517 13.4976 11.8737 15.2619C11.8737 15.6368 11.7635 15.7691 11.4106 15.8573C8.56566 16.4748 6.86752 19.1875 7.52914 22.0103C8.1687 24.6789 10.7931 26.3549 13.4837 25.8036C13.7341 25.7604 13.9777 25.6767 14.2238 25.5921C14.6064 25.4606 14.995 25.3271 15.4244 25.3405C17.4088 25.3735 19.4056 25.3694 21.4055 25.3653H21.4056H21.4058C22.0729 25.3639 22.7404 25.3625 23.4079 25.3625C24.4444 25.3625 25.0619 24.745 25.0619 23.7305ZM17.5191 21.5917C17.3647 22.6062 16.9236 23.5104 16.1958 24.3264C16.3061 24.3484 16.3723 24.3705 16.3723 24.3926C17.1442 24.3926 17.916 24.3901 18.6879 24.3877C20.2317 24.3828 21.7755 24.3779 23.3192 24.3926C23.8265 24.4146 24.0029 24.1279 24.0029 23.6868V12.9466C24.0029 12.6599 23.9147 12.5717 23.628 12.5717C23.1281 12.5864 22.6282 12.5815 22.1283 12.5766C21.8784 12.5742 21.6284 12.5717 21.3785 12.5717C20.783 12.5497 20.6287 12.4173 20.6287 11.8219C20.6066 11.0941 20.6066 10.3443 20.6287 9.61651C20.6507 9.2416 20.5184 9.17544 20.1655 9.17544H13.6597C13.0201 9.17544 12.8657 9.30776 12.8657 9.92527V15.4387C12.8657 15.7034 12.9319 15.7695 13.1965 15.8136C14.1669 15.946 15.0711 16.3209 15.7989 17.0045C16.0635 17.2471 16.3282 17.3133 16.681 17.3133H21.9078C22.2827 17.3133 22.6356 17.3353 22.6356 17.8205C22.6356 18.3057 22.2606 18.3057 21.8857 18.3057H20.5625H16.9016C17.1883 18.8571 17.4088 19.3643 17.475 19.9156C17.5191 20.2244 17.6514 20.3126 17.9822 20.3126C18.8211 20.2979 19.6697 20.3028 20.5151 20.3077C20.9366 20.3102 21.3572 20.3126 21.7755 20.3126C21.9739 20.3126 22.1724 20.3126 22.3709 20.3788C22.5914 20.467 22.6576 20.6434 22.6576 20.8639C22.6576 21.0845 22.5473 21.2609 22.3268 21.305C22.1504 21.3491 21.9519 21.3491 21.7755 21.3491H17.9822C17.9473 21.3491 17.9117 21.3464 17.8765 21.3437C17.7116 21.3311 17.5554 21.3191 17.5191 21.5917ZM8.27959 20.7983C8.27959 18.4606 10.1321 16.6302 12.4919 16.6302C14.8296 16.6302 16.6821 18.4827 16.6821 20.8204C16.6821 23.136 14.8075 25.0106 12.4919 25.0106C10.1101 25.0327 8.30165 23.2022 8.27959 20.7983ZM21.8687 10.1589C21.8019 10.0893 21.734 10.0185 21.6645 9.94613C21.6645 10.4534 21.6645 10.8283 21.6866 11.2032C21.6866 11.3355 21.6866 11.4678 21.863 11.4678H23.1421C22.7014 11.0272 22.3107 10.6198 21.8687 10.1589Z"
      fill="#0A0D12"
    />
    <path
      d="M8.27881 20.7974C8.27881 18.4597 10.1313 16.6293 12.4911 16.6293C14.8288 16.6293 16.6813 18.4818 16.6813 20.8195C16.6813 23.1351 14.8067 25.0097 12.4911 25.0097C10.1093 25.0318 8.30086 23.2013 8.27881 20.7974Z"
      fill="#4E5BA6"
    />
    <path
      d="M18.4458 15.3063C17.299 15.3063 16.1302 15.3063 14.9834 15.3063C14.6085 15.3063 14.2556 15.2622 14.2556 14.777C14.2556 14.2918 14.6305 14.2698 15.0054 14.2698C17.3211 14.2698 19.6147 14.2698 21.9303 14.2698C22.195 14.2698 22.4596 14.2698 22.614 14.5344C22.8345 14.9093 22.5478 15.3063 22.0186 15.3063C20.8276 15.3063 19.6367 15.3063 18.4458 15.3063Z"
      fill="#0A0D12"
    />
    <path
      d="M11.8736 21.6355C12.4249 22.1427 12.9101 22.6279 13.4173 23.1131C13.5056 23.2013 13.6599 23.2674 13.5938 23.4218C13.5276 23.5762 13.3732 23.51 13.263 23.5321C12.6896 23.5982 12.2705 23.4439 11.8956 23.0028C11.5207 22.5838 11.1237 22.2309 10.7047 21.856C10.4842 21.6575 10.4401 21.459 10.418 21.1723C10.418 20.8415 10.5283 20.7313 10.8591 20.7313C11.234 20.7313 11.631 20.8195 12.0059 20.643C12.2044 20.5548 12.4249 20.3784 12.3808 20.1579C12.3367 19.9373 12.0721 20.0696 11.8956 20.0476C11.5207 20.0255 11.1679 20.0255 10.7929 20.0476C10.4842 20.0696 10.418 19.9373 10.418 19.6506C10.418 19.386 10.4842 19.2536 10.7929 19.2536C11.2561 19.2757 11.7192 19.2536 12.2485 19.2536C11.8515 18.7685 11.3663 18.989 10.9694 18.9228C10.7929 18.9008 10.5503 19.0331 10.4621 18.7464C10.3078 18.3494 10.4842 18.0627 10.9253 18.0627C11.8736 18.0627 12.8439 18.0627 13.7923 18.0627C14.101 18.0627 14.2554 18.1289 14.2554 18.4818C14.2554 18.8567 14.0569 18.8346 13.7923 18.8567C13.6379 18.8567 13.3291 18.7023 13.3291 19.0111C13.3291 19.3198 13.6379 19.2536 13.8364 19.2536C14.101 19.2316 14.2554 19.2978 14.2554 19.6065C14.2554 19.8932 14.1892 20.0696 13.8584 20.0255C13.682 20.0035 13.4835 19.9594 13.4394 20.2461C13.2409 21.0841 12.6896 21.459 11.8736 21.6355Z"
      fill="#FDFDFD"
    />
  </svg>
);

export const flagIcon = () => (
  <svg width="17" height="16" viewBox="0 0 17 16" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path
      fill-rule="evenodd"
      clip-rule="evenodd"
      d="M5.1665 13.9948C5.1665 13.6266 5.46498 13.3281 5.83317 13.3281H8.49984C8.86803 13.3281 9.1665 13.6266 9.1665 13.9948C9.1665 14.363 8.86803 14.6615 8.49984 14.6615H5.83317C5.46498 14.6615 5.1665 14.363 5.1665 13.9948Z"
      fill="#828282"
    />
    <path
      fill-rule="evenodd"
      clip-rule="evenodd"
      d="M7.16667 1.32812C7.53486 1.32812 7.83333 1.6266 7.83333 1.99479V13.9948C7.83333 14.363 7.53486 14.6615 7.16667 14.6615C6.79848 14.6615 6.5 14.363 6.5 13.9948V1.99479C6.5 1.6266 6.79848 1.32812 7.16667 1.32812Z"
      fill="#828282"
    />
    <path
      fill-rule="evenodd"
      clip-rule="evenodd"
      d="M6.55738 2.39217C6.70692 2.05571 7.10089 1.90418 7.43735 2.05372L13.4373 4.72039C13.6781 4.82739 13.8333 5.06613 13.8333 5.32959C13.8333 5.59305 13.6781 5.8318 13.4373 5.9388L7.43735 8.60547C7.10089 8.755 6.70692 8.60348 6.55738 8.26702C6.40785 7.93056 6.55937 7.53659 6.89583 7.38705L11.5251 5.32959L6.89583 3.27213C6.55937 3.1226 6.40785 2.72862 6.55738 2.39217Z"
      fill="#828282"
    />
  </svg>
);

export const ticketFlag = () => (
  <svg width="14" height="16" viewBox="0 0 14 16" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path
      fill-rule="evenodd"
      clip-rule="evenodd"
      d="M0.541656 1.16663C0.541656 0.821448 0.821478 0.541626 1.16666 0.541626H12.8333C13.0861 0.541626 13.314 0.693902 13.4107 0.927449C13.5075 1.161 13.454 1.42982 13.2753 1.60857L9.96721 4.91663L13.2753 8.22468C13.454 8.40343 13.5075 8.67226 13.4107 8.9058C13.314 9.13935 13.0861 9.29163 12.8333 9.29163H1.79166V14.5C1.79166 14.8451 1.51183 15.125 1.16666 15.125C0.821478 15.125 0.541656 14.8451 0.541656 14.5V1.16663ZM1.79166 8.04163H11.3244L8.64138 5.35857C8.3973 5.11449 8.3973 4.71876 8.64138 4.47468L11.3244 1.79163H1.79166V8.04163Z"
      fill="#4E5BA6"
    />
  </svg>
);

export const eye = () => (
  <svg width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path
      fill-rule="evenodd"
      clip-rule="evenodd"
      d="M2.91045 9C4.60774 11.688 6.6394 12.9375 9 12.9375C11.3606 12.9375 13.3923 11.688 15.0896 9C13.3923 6.31203 11.3606 5.0625 9 5.0625C6.6394 5.0625 4.60774 6.31203 2.91045 9ZM1.76766 8.7106C3.63498 5.59839 6.04375 3.9375 9 3.9375C11.9563 3.9375 14.365 5.59839 16.2323 8.7106C16.3392 8.88873 16.3392 9.11127 16.2323 9.2894C14.365 12.4016 11.9563 14.0625 9 14.0625C6.04375 14.0625 3.63498 12.4016 1.76766 9.2894C1.66078 9.11127 1.66078 8.88873 1.76766 8.7106ZM7.54159 7.54159C7.92839 7.1548 8.45299 6.9375 9 6.9375C9.54701 6.9375 10.0716 7.1548 10.4584 7.54159C10.8452 7.92839 11.0625 8.45299 11.0625 9C11.0625 9.54701 10.8452 10.0716 10.4584 10.4584C10.0716 10.8452 9.54701 11.0625 9 11.0625C8.45299 11.0625 7.92839 10.8452 7.54159 10.4584C7.1548 10.0716 6.9375 9.54701 6.9375 9C6.9375 8.45299 7.1548 7.92839 7.54159 7.54159ZM9 8.0625C8.75136 8.0625 8.5129 8.16127 8.33709 8.33709C8.16127 8.5129 8.0625 8.75136 8.0625 9C8.0625 9.24864 8.16127 9.4871 8.33709 9.66291C8.5129 9.83873 8.75136 9.9375 9 9.9375C9.24864 9.9375 9.4871 9.83873 9.66291 9.66291C9.83873 9.4871 9.9375 9.24864 9.9375 9C9.9375 8.75136 9.83873 8.5129 9.66291 8.33709C9.4871 8.16127 9.24864 8.0625 9 8.0625Z"
      fill="#717680"
    />
  </svg>
);

export const closedEye = () => (
  <svg width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path
      fill-rule="evenodd"
      clip-rule="evenodd"
      d="M1.85225 1.85225C2.07192 1.63258 2.42808 1.63258 2.64775 1.85225L12.8559 12.0604C12.8921 12.0885 12.9253 12.1215 12.9545 12.159L16.1477 15.3523C16.3674 15.5719 16.3674 15.9281 16.1477 16.1477C15.9281 16.3674 15.5719 16.3674 15.3523 16.1477L12.4153 13.2108C11.368 13.776 10.193 14.0701 8.99814 14.0625C6.04273 14.0618 3.63459 12.401 1.76766 9.2894C1.66078 9.11127 1.66078 8.88873 1.76766 8.7106C2.60527 7.31458 3.55194 6.2072 4.61561 5.4111L1.85225 2.64775C1.63258 2.42808 1.63258 2.07192 1.85225 1.85225ZM5.42063 6.21612C4.52073 6.85808 3.68157 7.77911 2.91046 9.00002C4.60775 11.688 6.63941 12.9375 9 12.9375H9.00371V12.9375C9.89606 12.9434 10.7752 12.7494 11.5779 12.3734L10.0058 10.8013C9.70104 10.9715 9.35525 11.0632 8.99999 11.0633C8.45297 11.0634 7.92832 10.8462 7.54145 10.4594C7.15458 10.0727 6.93718 9.54814 6.93709 9.00112C6.93703 8.64561 7.02874 8.29956 7.19913 7.99463L5.42063 6.21612ZM8.07165 8.86715C8.06531 8.9112 8.06208 8.95589 8.06209 9.00092C8.06213 9.24957 8.16095 9.48802 8.3368 9.66381C8.51266 9.8396 8.75114 9.93834 8.99979 9.93829C9.04471 9.93829 9.08929 9.93505 9.13323 9.92873L8.07165 8.86715ZM8.99855 5.0625C8.57813 5.06141 8.15867 5.1029 7.74661 5.18632C7.44213 5.24796 7.14532 5.05109 7.08368 4.74661C7.02204 4.44212 7.21891 4.14532 7.52339 4.08368C8.00966 3.98524 8.50463 3.93627 9.00076 3.9375C9.00099 3.9375 9.00122 3.9375 9.00145 3.9375L9 4.5V3.9375C9.00025 3.9375 9.00051 3.9375 9.00076 3.9375C11.9567 3.93778 14.3652 5.59866 16.2323 8.7106C16.3392 8.88873 16.3392 9.11127 16.2323 9.2894C15.7154 10.151 15.1568 10.9026 14.5553 11.5389C14.3419 11.7647 13.9859 11.7747 13.7601 11.5613C13.5343 11.3479 13.5243 10.9919 13.7377 10.7661C14.2091 10.2674 14.6603 9.67951 15.0895 8.99997C15.1493 9.09466 15.2087 9.19114 15.2677 9.2894L15.75 9L15.2677 8.7106C15.2087 8.80884 15.1493 8.90529 15.0895 8.99997C13.3923 6.31202 11.3606 5.0625 9 5.0625L8.99855 5.0625Z"
      fill="#717680"
    />
  </svg>
);

export const filterIcon = () => (
  <svg width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path
      fill-rule="evenodd"
      clip-rule="evenodd"
      d="M2.4375 3C2.4375 2.68934 2.68934 2.4375 3 2.4375H15C15.3107 2.4375 15.5625 2.68934 15.5625 3V4.629C15.5624 5.17594 15.345 5.70055 14.9582 6.08725C14.9582 6.08727 14.9583 6.08723 14.9582 6.08725L11.8125 9.23299V14.25C11.8125 14.4921 11.6576 14.7071 11.4279 14.7836L6.92788 16.2836C6.75634 16.3408 6.56778 16.312 6.4211 16.2063C6.27442 16.1006 6.1875 15.9308 6.1875 15.75V9.59247L2.97378 6.05738C2.62878 5.6778 2.43755 5.18325 2.4375 4.67031V3ZM3.5625 3.5625V4.67019C3.5625 4.67017 3.5625 4.67021 3.5625 4.67019C3.56254 4.90331 3.64943 5.12811 3.80622 5.30062C3.80621 5.30061 3.80623 5.30063 3.80622 5.30062L7.16622 8.99662C7.26034 9.10016 7.3125 9.23507 7.3125 9.375V14.9696L10.6875 13.8446V9C10.6875 8.85082 10.7468 8.70774 10.8523 8.60225L14.1628 5.29175C14.3386 5.11601 14.4374 4.87758 14.4375 4.629C14.4375 4.62896 14.4375 4.62904 14.4375 4.629V3.5625H3.5625Z"
      fill="#0A0D12"
    />
  </svg>
);

export const minusIcon = () => (
  <svg width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path
      fill-rule="evenodd"
      clip-rule="evenodd"
      d="M3.1875 9C3.1875 8.68934 3.43934 8.4375 3.75 8.4375H14.25C14.5607 8.4375 14.8125 8.68934 14.8125 9C14.8125 9.31066 14.5607 9.5625 14.25 9.5625H3.75C3.43934 9.5625 3.1875 9.31066 3.1875 9Z"
      fill="#0A0D12"
    />
  </svg>
);

export const activationIcon = () => (
  <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path
      fill-rule="evenodd"
      clip-rule="evenodd"
      d="M6 5.75C5.66848 5.75 5.35054 5.8817 5.11612 6.11612C4.8817 6.35054 4.75 6.66848 4.75 7V11C4.75 11.1989 4.67098 11.3897 4.53033 11.5303L4.06066 12L4.53033 12.4697C4.67098 12.6103 4.75 12.8011 4.75 13V17C4.75 17.3315 4.8817 17.6495 5.11612 17.8839C5.35054 18.1183 5.66848 18.25 6 18.25C6.41421 18.25 6.75 18.5858 6.75 19C6.75 19.4142 6.41421 19.75 6 19.75C5.27065 19.75 4.57118 19.4603 4.05546 18.9445C3.53973 18.4288 3.25 17.7293 3.25 17V13.3107L2.46967 12.5303C2.17678 12.2374 2.17678 11.7626 2.46967 11.4697L3.25 10.6893V7C3.25 6.27065 3.53973 5.57118 4.05546 5.05546C4.57118 4.53973 5.27065 4.25 6 4.25C6.41421 4.25 6.75 4.58579 6.75 5C6.75 5.41421 6.41421 5.75 6 5.75ZM17.25 5C17.25 4.58579 17.5858 4.25 18 4.25C18.7293 4.25 19.4288 4.53973 19.9445 5.05546C20.4603 5.57118 20.75 6.27065 20.75 7V10.6893L21.5303 11.4697C21.8232 11.7626 21.8232 12.2374 21.5303 12.5303L20.75 13.3107V17C20.75 17.7293 20.4603 18.4288 19.9445 18.9445C19.4288 19.4603 18.7293 19.75 18 19.75C17.5858 19.75 17.25 19.4142 17.25 19C17.25 18.5858 17.5858 18.25 18 18.25C18.3315 18.25 18.6495 18.1183 18.8839 17.8839C19.1183 17.6495 19.25 17.3315 19.25 17V13C19.25 12.8011 19.329 12.6103 19.4697 12.4697L19.9393 12L19.4697 11.5303C19.329 11.3897 19.25 11.1989 19.25 11V7C19.25 6.66848 19.1183 6.35054 18.8839 6.11612C18.6495 5.8817 18.3315 5.75 18 5.75C17.5858 5.75 17.25 5.41421 17.25 5ZM12 8.25C12.4142 8.25 12.75 8.58579 12.75 9V11.25H15C15.4142 11.25 15.75 11.5858 15.75 12C15.75 12.4142 15.4142 12.75 15 12.75H12.75V15C12.75 15.4142 12.4142 15.75 12 15.75C11.5858 15.75 11.25 15.4142 11.25 15V12.75H9C8.58579 12.75 8.25 12.4142 8.25 12C8.25 11.5858 8.58579 11.25 9 11.25H11.25V9C11.25 8.58579 11.5858 8.25 12 8.25Z"
      fill="#717680"
    />
  </svg>
);

export const noNotificaticon = () => (
  <svg width="36" height="36" viewBox="0 0 36 36" fill="none" xmlns="http://www.w3.org/2000/svg">
    <g clip-path="url(#clip0_1157_26366)">
      <path
        d="M32.9996 5.40002C33.5986 5.39977 34.1759 5.62383 34.6179 6.02806C35.0599 6.4323 35.3345 6.98741 35.3876 7.58402C35.3936 7.65602 35.3996 7.72802 35.3996 7.80002V27C35.3996 27.6365 35.1468 28.247 34.6967 28.6971C34.2466 29.1472 33.6361 29.4 32.9996 29.4H16.1996C16.1996 29.4 8.15961 35.154 8.15361 35.16C7.95536 35.3027 7.71963 35.384 7.47561 35.394C7.47117 35.397 7.46595 35.3986 7.46061 35.3986C7.45527 35.3986 7.45005 35.397 7.44561 35.394C7.4179 35.399 7.38974 35.401 7.36161 35.4C7.14419 35.3922 6.93137 35.3353 6.73912 35.2334C6.54687 35.1316 6.38019 34.9875 6.25161 34.812C6.24835 34.8088 6.24624 34.8046 6.24561 34.8C6.08374 34.5814 5.9974 34.316 5.99961 34.044V29.4H2.99961C2.36309 29.4 1.75264 29.1472 1.30255 28.6971C0.852466 28.247 0.599609 27.6365 0.599609 27V7.80002C0.599609 7.72802 0.605609 7.65602 0.611609 7.58402C0.664698 6.98741 0.939288 6.4323 1.38128 6.02806C1.82328 5.62383 2.40064 5.39977 2.99961 5.40002H32.9996Z"
        fill="#FDFDFD"
      />
      <path
        d="M21 21.6V22.8C21 23.5956 20.6839 24.3587 20.1213 24.9213C19.5587 25.4839 18.7956 25.8 18 25.8C17.2044 25.8 16.4413 25.4839 15.8787 24.9213C15.3161 24.3587 15 23.5956 15 22.8V21.6H21Z"
        fill="#4E5BA6"
      />
      <path
        d="M25.8002 13.164V18L28.6262 20.826C28.7366 20.9381 28.799 21.0887 28.8002 21.246V22.2C28.8002 22.3591 28.737 22.5117 28.6245 22.6242C28.5119 22.7368 28.3593 22.8 28.2002 22.8H7.8002C7.64107 22.8 7.48845 22.7368 7.37593 22.6242C7.26341 22.5117 7.2002 22.3591 7.2002 22.2V21.246C7.20143 21.0887 7.26384 20.9381 7.3742 20.826L10.2002 18V10.8C10.1994 9.14573 10.7252 7.53417 11.7014 6.1987C12.6777 4.86323 14.0537 3.87319 15.6302 3.37198C15.611 3.24889 15.601 3.12455 15.6002 2.99998C15.6002 2.36346 15.8531 1.75301 16.3031 1.30292C16.7532 0.852832 17.3637 0.599976 18.0002 0.599976C18.6367 0.599976 19.2472 0.852832 19.6973 1.30292C20.1473 1.75301 20.4002 2.36346 20.4002 2.99998C20.3994 3.12455 20.3894 3.24889 20.3702 3.37198C21.3775 3.69181 22.3086 4.21455 23.1062 4.90798L25.8002 13.164Z"
        fill="#4E5BA6"
      />
      <path
        d="M26.3996 13.2C29.0506 13.2 31.1996 11.0509 31.1996 8.39998C31.1996 5.74901 29.0506 3.59998 26.3996 3.59998C23.7486 3.59998 21.5996 5.74901 21.5996 8.39998C21.5996 11.0509 23.7486 13.2 26.3996 13.2Z"
        fill="#FDFDFD"
      />
      <path
        d="M33 4.79999H30.4086C30.0322 4.37393 29.5905 4.0104 29.1 3.72299C28.9626 3.64906 28.8019 3.63155 28.6518 3.67415C28.5017 3.71675 28.3742 3.81613 28.2961 3.9512C28.2181 4.08628 28.1957 4.24644 28.2338 4.39772C28.2719 4.549 28.3673 4.67951 28.5 4.76159C29.3827 5.27061 30.0503 6.08318 30.3783 7.0479C30.7063 8.01263 30.6725 9.06371 30.283 10.0053C29.8935 10.9469 29.175 11.7148 28.2614 12.1659C27.3477 12.6171 26.3012 12.7207 25.3168 12.4574C24.3325 12.1942 23.4773 11.582 22.9108 10.735C22.3443 9.88808 22.105 8.86404 22.2376 7.85373C22.3701 6.84341 22.8654 5.91574 23.6312 5.24353C24.397 4.57133 25.381 4.20045 26.4 4.19999C26.5591 4.19999 26.7117 4.13677 26.8243 4.02425C26.9368 3.91173 27 3.75912 27 3.59999C27 3.44086 26.9368 3.28825 26.8243 3.17572C26.7117 3.0632 26.5591 2.99999 26.4 2.99999C25.2042 3.00033 24.0427 3.40008 23.1 4.13579C22.4614 3.64223 21.7539 3.245 21 2.95679C21 2.16114 20.6839 1.39808 20.1213 0.835467C19.5587 0.272858 18.7957 -0.0432129 18 -0.0432129C17.2044 -0.0432129 16.4413 0.272858 15.8787 0.835467C15.3161 1.39808 15 2.16114 15 2.95679C13.9231 3.3659 12.9425 3.99346 12.12 4.79999H3C2.24927 4.79635 1.52477 5.07596 0.971126 5.583C0.417481 6.09004 0.0754133 6.78723 0.0132 7.53539C0.0066 7.62359 0 7.71179 0 7.79999V27C0.000952715 27.7953 0.317329 28.5579 0.879732 29.1203C1.44213 29.6827 2.20464 29.999 3 30H5.4V34.044C5.39771 34.4306 5.51646 34.8083 5.7396 35.124C5.748 35.1372 5.7576 35.1516 5.7666 35.1642C6.07197 35.585 6.52913 35.8704 7.04127 35.96C7.55341 36.0496 8.08031 35.9363 8.5104 35.6442C8.6736 35.5242 15.2856 30.7944 16.3926 30.0042H33C33.7954 30.0032 34.5579 29.6869 35.1203 29.1245C35.6827 28.5621 35.9991 27.7995 36 27.0042V7.79999C36 7.71179 35.9934 7.62359 35.9862 7.53119C35.9233 6.78378 35.5809 6.08755 35.0275 5.58134C34.474 5.07513 33.75 4.79613 33 4.79999ZM15.8112 3.94379C15.947 3.90059 16.063 3.81045 16.1384 3.68952C16.2139 3.56859 16.2438 3.42476 16.2228 3.28379C16.2085 3.18985 16.2008 3.09501 16.2 2.99999C16.2 2.5226 16.3896 2.06476 16.7272 1.7272C17.0648 1.38963 17.5226 1.19999 18 1.19999C18.4774 1.19999 18.9352 1.38963 19.2728 1.7272C19.6104 2.06476 19.8 2.5226 19.8 2.99999C19.7991 3.09461 19.7915 3.18905 19.7772 3.28259C19.7562 3.42348 19.786 3.56725 19.8613 3.68817C19.9366 3.80909 20.0525 3.89927 20.1882 3.94259C20.9197 4.17488 21.6087 4.52443 22.2282 4.97759C21.644 5.68803 21.2513 6.53609 21.0874 7.44112C20.9234 8.34616 20.9937 9.27809 21.2915 10.1483C21.5894 11.0185 22.1048 11.7981 22.7889 12.4129C23.473 13.0277 24.303 13.4573 25.2 13.6608V18C25.2 18.1591 25.2633 18.3117 25.3758 18.4242L28.2 21.246V22.2H7.8V21.2502L10.626 18.4242C10.7379 18.3114 10.8004 18.1588 10.8 18V10.8C10.7941 9.27195 11.2773 7.78211 12.179 6.54846C13.0807 5.3148 14.3535 4.40206 15.8112 3.94379ZM20.3148 23.4C20.1838 23.9145 19.8852 24.3708 19.466 24.6967C19.0468 25.0225 18.531 25.1994 18 25.1994C17.469 25.1994 16.9532 25.0225 16.534 24.6967C16.1148 24.3708 15.8162 23.9145 15.6852 23.4H20.3148ZM34.8 27C34.8 27.4774 34.6104 27.9352 34.2728 28.2728C33.9352 28.6103 33.4774 28.8 33 28.8H16.2C16.0747 28.8 15.9526 28.8393 15.8508 28.9122C9.2748 33.618 8.0508 34.4922 7.7982 34.6764C7.70958 34.7394 7.60629 34.7786 7.4982 34.7904C7.38909 34.8072 7.27767 34.8006 7.17129 34.7711C7.06491 34.7416 6.966 34.6899 6.8811 34.6194C6.7962 34.5488 6.72724 34.461 6.67878 34.3618C6.63032 34.2627 6.60347 34.1543 6.6 34.044V29.4C6.6 29.2409 6.53679 29.0882 6.42426 28.9757C6.31174 28.8632 6.15913 28.8 6 28.8H3C2.52261 28.8 2.06477 28.6103 1.72721 28.2728C1.38964 27.9352 1.2 27.4774 1.2 27V7.79999C1.2 7.74419 1.2054 7.68779 1.2096 7.63679C1.24734 7.18874 1.45262 6.77139 1.78447 6.468C2.11633 6.16461 2.55037 5.99749 3 5.99999H11.0964C10.1187 7.40942 9.59645 9.08465 9.6 10.8V17.7516L6.9498 20.4C6.72632 20.6249 6.60062 20.9289 6.6 21.246V22.2C6.6 22.5182 6.72643 22.8235 6.95147 23.0485C7.17652 23.2736 7.48174 23.4 7.8 23.4H14.454C14.595 24.2385 15.0286 24.9999 15.6778 25.549C16.327 26.0981 17.1497 26.3994 18 26.3994C18.8503 26.3994 19.673 26.0981 20.3222 25.549C20.9714 24.9999 21.405 24.2385 21.546 23.4H28.2C28.5183 23.4 28.8235 23.2736 29.0485 23.0485C29.2736 22.8235 29.4 22.5182 29.4 22.2V21.246C29.3994 20.9289 29.2737 20.6249 29.0502 20.4L26.4 17.7516V13.8C27.3203 13.8042 28.2263 13.5721 29.0312 13.1258C29.8361 12.6796 30.5129 12.0341 30.9969 11.2513C31.4808 10.4685 31.7557 9.57455 31.7952 8.65507C31.8346 7.73558 31.6373 6.82137 31.2222 5.99999H33C33.4489 5.99728 33.8824 6.16379 34.2141 6.46634C34.5458 6.76889 34.7513 7.18529 34.7898 7.63259C34.7946 7.68779 34.8 7.74419 34.8 7.79999V27Z"
        fill="#181D27"
      />
      <path
        fill-rule="evenodd"
        clip-rule="evenodd"
        d="M27 8.80005V7.80005C27 7.52391 26.7761 7.30005 26.5 7.30005C26.2239 7.30005 26 7.52391 26 7.80005V8.80005C26 9.07619 26.2239 9.30005 26.5 9.30005C26.7761 9.30005 27 9.07619 27 8.80005ZM26.5 6.30005C25.6716 6.30005 25 6.97162 25 7.80005V8.80005C25 9.62848 25.6716 10.3 26.5 10.3C27.3284 10.3 28 9.62848 28 8.80005V7.80005C28 6.97162 27.3284 6.30005 26.5 6.30005Z"
        fill="#181D27"
      />
    </g>
    <defs>
      <clipPath id="clip0_1157_26366">
        <rect width="36" height="36" fill="white" />
      </clipPath>
    </defs>
  </svg>
);

export const msgIcon = () => (
  <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path
      fill-rule="evenodd"
      clip-rule="evenodd"
      d="M7.99967 6.66797C8.36786 6.66797 8.66634 6.96645 8.66634 7.33464V7.3413C8.66634 7.70949 8.36786 8.00797 7.99967 8.00797C7.63148 8.00797 7.33301 7.70949 7.33301 7.3413V7.33464C7.33301 6.96645 7.63148 6.66797 7.99967 6.66797Z"
      fill="#363F72"
    />
    <path
      fill-rule="evenodd"
      clip-rule="evenodd"
      d="M5.33366 6.66797C5.70185 6.66797 6.00033 6.96645 6.00033 7.33464V7.3413C6.00033 7.70949 5.70185 8.00797 5.33366 8.00797C4.96547 8.00797 4.66699 7.70949 4.66699 7.3413V7.33464C4.66699 6.96645 4.96547 6.66797 5.33366 6.66797Z"
      fill="#363F72"
    />
    <path
      fill-rule="evenodd"
      clip-rule="evenodd"
      d="M10.6667 6.66797C11.0349 6.66797 11.3333 6.96645 11.3333 7.33464V7.3413C11.3333 7.70949 11.0349 8.00797 10.6667 8.00797C10.2985 8.00797 10 7.70949 10 7.3413V7.33464C10 6.96645 10.2985 6.66797 10.6667 6.66797Z"
      fill="#363F72"
    />
    <path
      fill-rule="evenodd"
      clip-rule="evenodd"
      d="M3.99967 3.33333C3.64605 3.33333 3.30691 3.47381 3.05687 3.72386C2.80682 3.97391 2.66634 4.31304 2.66634 4.66667V10C2.66634 10.3536 2.80682 10.6928 3.05687 10.9428C3.30691 11.1929 3.64605 11.3333 3.99967 11.3333H5.33301C5.7012 11.3333 5.99967 11.6318 5.99967 12V12.8225L8.32334 11.4283C8.42695 11.3662 8.54551 11.3333 8.66634 11.3333H11.9997C12.3533 11.3333 12.6924 11.1929 12.9425 10.9428C13.1925 10.6928 13.333 10.3536 13.333 10V4.66667C13.333 4.31304 13.1925 3.97391 12.9425 3.72386C12.6924 3.47381 12.3533 3.33333 11.9997 3.33333H3.99967ZM2.11406 2.78105C2.61415 2.28095 3.29243 2 3.99967 2H11.9997C12.7069 2 13.3852 2.28095 13.8853 2.78105C14.3854 3.28115 14.6663 3.95942 14.6663 4.66667V10C14.6663 10.7072 14.3854 11.3855 13.8853 11.8856C13.3852 12.3857 12.7069 12.6667 11.9997 12.6667H8.851L5.676 14.5717C5.47005 14.6952 5.21355 14.6985 5.00454 14.5801C4.79554 14.4618 4.66634 14.2402 4.66634 14V12.6667H3.99967C3.29243 12.6667 2.61415 12.3857 2.11406 11.8856C1.61396 11.3855 1.33301 10.7072 1.33301 10V4.66667C1.33301 3.95942 1.61396 3.28115 2.11406 2.78105Z"
      fill="#363F72"
    />
  </svg>
);

export const refreshIcon = () => (
  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path
      fill-rule="evenodd"
      clip-rule="evenodd"
      d="M6.29144 3.70978C7.69216 2.90056 9.32009 2.57434 10.9245 2.78135C12.5288 2.98837 14.0206 3.71714 15.1701 4.8554C16.3195 5.99367 17.0628 7.47828 17.2855 9.08055C17.333 9.42245 17.0943 9.73812 16.7524 9.78564C16.4106 9.83315 16.0949 9.59451 16.0474 9.25262C15.8624 7.92192 15.2451 6.68893 14.2905 5.74359C13.3359 4.79825 12.0969 4.193 10.7645 4.02107C9.43205 3.84915 8.08004 4.12008 6.91673 4.79214C6.03602 5.30094 5.30045 6.01828 4.77085 6.87492H6.66642C7.0116 6.87492 7.29142 7.15474 7.29142 7.49992C7.29142 7.8451 7.0116 8.12492 6.66642 8.12492H3.76372C3.75426 8.12514 3.74477 8.12514 3.73527 8.12492H3.33308C2.98791 8.12492 2.70808 7.8451 2.70808 7.49992V4.16659C2.70808 3.82141 2.98791 3.54159 3.33308 3.54159C3.67826 3.54159 3.95808 3.82141 3.95808 4.16659V5.83905C4.56932 4.97426 5.36347 4.24588 6.29144 3.70978ZM3.24705 10.2142C3.58895 10.1667 3.90462 10.4053 3.95214 10.7472C4.13706 12.0779 4.75438 13.3109 5.709 14.2562C6.66361 15.2016 7.90257 15.8068 9.23501 15.9788C10.5674 16.1507 11.9195 15.8797 13.0828 15.2077C13.9635 14.6989 14.6991 13.9815 15.2287 13.1249H13.3331C12.9879 13.1249 12.7081 12.8451 12.7081 12.4999C12.7081 12.1547 12.9879 11.8749 13.3331 11.8749H16.2358C16.2452 11.8747 16.2547 11.8747 16.2642 11.8749H16.6664C17.0116 11.8749 17.2914 12.1547 17.2914 12.4999V15.8332C17.2914 16.1784 17.0116 16.4582 16.6664 16.4582C16.3212 16.4582 16.0414 16.1784 16.0414 15.8332V14.1608C15.4302 15.0256 14.636 15.754 13.7081 16.2901C12.3073 17.0993 10.6794 17.4255 9.07504 17.2185C7.47068 17.0115 5.97887 16.2827 4.82944 15.1444C3.68001 14.0062 2.9367 12.5215 2.71403 10.9193C2.66652 10.5774 2.90516 10.2617 3.24705 10.2142Z"
      fill="#4E5BA6"
    />
  </svg>
);

export const documentIcon = () => (
  <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path
      d="M12.0564 16.9263C12.0564 19.0568 10.2198 20.8445 8.08931 20.8445C5.88533 20.8445 4.09766 19.0323 4.09766 16.8038C4.09766 14.7223 5.9588 12.9102 8.08931 12.9102C10.2688 12.9102 12.0564 14.7223 12.0564 16.9263Z"
      fill="#4E5BA6"
    />
    <path
      fill-rule="evenodd"
      clip-rule="evenodd"
      d="M21.1273 13.1757L21.1273 13.1735C21.1288 12.6 21.1303 12.0258 21.1303 11.4501V4.68306C21.1303 2.92601 20.1568 2 18.3998 2C17.5689 2.00478 16.739 2.01051 15.9099 2.01624C12.6162 2.039 9.33455 2.06168 6.05293 2.02374C4.81825 2.02374 3.53608 3.04473 3.58357 4.5406C3.60731 5.39538 3.93972 5.75154 4.7945 5.77528C5.50682 5.79903 6.24289 5.79903 6.97895 5.77528C7.43008 5.75154 7.59628 5.91775 7.59628 6.39262C7.57254 7.93598 7.5488 9.47933 7.59628 11.0227C7.62003 11.6163 7.47757 11.83 6.86023 11.9487C4.65204 12.3286 2.91873 14.4418 2.8475 16.6737C2.77627 18.9057 4.08219 20.8764 6.14791 21.66C8.23738 22.4435 10.4693 21.8499 11.9177 20.1166C12.3926 19.5468 12.5588 19.523 13.1286 19.9979C13.9834 20.7102 14.8619 20.7102 15.693 20.0216C16.3815 19.4518 16.9277 19.428 17.5925 20.0216C18.2336 20.5915 18.9696 20.7102 19.6819 20.354C20.4655 19.9504 21.1778 19.428 21.1541 18.3358C21.1185 16.6091 21.1229 14.8957 21.1273 13.1757ZM17.8302 14.8693H12.654C12.9627 15.6291 13.0814 16.3177 13.0577 17.1249C13.0488 17.2935 13.0001 17.4753 12.9526 17.653L12.9526 17.653L12.9526 17.653C12.8729 17.951 12.7962 18.2375 12.9152 18.4309C13.0151 18.5932 13.2265 18.6898 13.4529 18.7933C13.6572 18.8866 13.8735 18.9855 14.0312 19.1432C14.3161 19.4281 14.5535 19.4044 14.8622 19.1669C16.0969 18.1697 17.1654 18.1697 18.4475 19.1432C18.4837 19.1658 18.5181 19.1901 18.5519 19.2139C18.696 19.3157 18.8296 19.41 19.0411 19.3331C19.516 19.1669 19.9434 18.5733 19.9434 18.0272V5.0393C19.9434 3.73338 19.3973 3.21101 18.0914 3.21101H11.633H9.21115C8.80751 3.18727 8.61755 3.30599 8.78375 3.75712C8.92622 4.16077 8.92623 4.6119 8.92623 5.0393V11.4976C8.92623 11.8775 9.59105 12.2812 9.87598 12.0438C10.1372 11.8301 10.4221 11.8301 10.707 11.8301H17.7115C17.8056 11.8301 17.9023 11.8275 17.9999 11.8249C18.1977 11.8196 18.3992 11.8142 18.59 11.8301C18.8749 11.8301 19.0174 12.02 19.0411 12.2812C19.0649 12.5661 18.9462 12.7561 18.6612 12.8036C18.4475 12.8273 18.2338 12.851 18.0201 12.851H11.6805C11.6322 12.851 11.5818 12.843 11.5324 12.8351C11.4118 12.8159 11.2967 12.7975 11.2294 12.8985C11.6805 13.7533 12.3928 13.872 13.2714 13.8483C14.322 13.7974 15.3606 13.8071 16.4044 13.8168C16.8231 13.8207 17.2425 13.8245 17.664 13.8245C17.7414 13.8245 17.8214 13.822 17.903 13.8194H17.9031H17.9031H17.9031H17.9031C18.0719 13.8141 18.2478 13.8085 18.4238 13.8245C18.78 13.8245 19.0649 13.967 19.0411 14.3707C19.0174 14.7506 18.7562 14.8693 18.4238 14.8693H17.8302ZM7.92823 20.7102C5.76754 20.7102 4.10547 19.0244 4.10547 16.8637C4.10547 14.7268 5.86252 12.9697 7.97572 12.9697C10.0414 12.9697 11.846 14.7743 11.8222 16.8637C11.7985 19.2381 9.89898 20.8052 7.92823 20.7102ZM5.76617 4.55904C5.8928 4.56169 6.02033 4.56437 6.14785 4.56437H7.40628C7.71495 4.56437 7.78618 4.42191 7.71494 4.16072C7.54874 3.56713 6.88391 3.04476 6.26657 3.04476C5.578 3.02102 4.93691 3.51964 4.74696 4.13698C4.65198 4.44565 4.67573 4.58811 5.03189 4.56437C5.26849 4.5486 5.51557 4.55378 5.76617 4.55904Z"
      fill="#0A0D12"
    />
    <path
      d="M17.0228 7.15234C17.4977 7.15234 17.9726 7.15234 18.4475 7.15234C18.8036 7.15234 19.0411 7.29481 19.0648 7.65097C19.0886 8.00712 18.8036 8.17333 18.495 8.19708C17.5215 8.22082 16.5242 8.22082 15.5507 8.19708C15.2421 8.19708 14.9571 8.05461 14.9809 7.67471C14.9809 7.29481 15.2421 7.17609 15.5745 7.17609C16.0494 7.15234 16.5242 7.15234 17.0228 7.15234Z"
      fill="#0A0D12"
    />
    <path
      d="M16.9748 10.2393C16.4999 10.2393 16.0251 10.2393 15.5502 10.2393C15.2178 10.2393 14.9803 10.1206 14.9566 9.74068C14.9328 9.36077 15.194 9.19457 15.5264 9.19457C16.4999 9.17082 17.4972 9.17082 18.4707 9.19457C18.7794 9.19457 19.0643 9.36077 19.0405 9.71693C19.0168 10.0731 18.7794 10.2393 18.4469 10.2393C17.9483 10.2393 17.4497 10.2393 16.9748 10.2393Z"
      fill="#0A0D12"
    />
    <path
      d="M16.9511 6.10729C16.4762 6.10729 16.0013 6.10729 15.5264 6.10729C15.194 6.10729 14.9328 5.98857 14.9566 5.60867C14.9566 5.25251 15.1703 5.13379 15.5027 5.13379C16.4762 5.13379 17.4259 5.13379 18.3994 5.13379C18.7556 5.13379 19.0405 5.22876 19.0405 5.65615C19.0405 6.08355 18.7319 6.15478 18.3757 6.13103C17.8771 6.10729 17.426 6.10729 16.9511 6.10729Z"
      fill="#0A0D12"
    />
    <path
      d="M10.6354 15.368C10.6117 15.5817 10.493 15.7479 10.3505 15.8904C9.49572 16.8164 8.61719 17.7424 7.76241 18.6684C7.42999 19.0246 7.09758 19.0246 6.74142 18.6922C6.33778 18.2885 5.93414 17.8849 5.53049 17.4812C5.26931 17.22 5.15058 16.9114 5.45925 16.6027C5.76792 16.294 6.05285 16.4365 6.33777 16.6977C6.6227 16.9826 6.88388 17.505 7.24004 17.4812C7.57245 17.4337 7.83364 16.9589 8.11857 16.6739C8.61719 16.1516 9.11581 15.6054 9.61443 15.0593C9.80438 14.8456 10.0418 14.7744 10.303 14.8931C10.5404 14.9644 10.6117 15.1543 10.6354 15.368Z"
      fill="#FDFDFD"
    />
    <path
      fill-rule="evenodd"
      clip-rule="evenodd"
      d="M13.8942 8.19344C13.8915 8.02197 13.8889 7.84963 13.8889 7.67462C13.8889 7.22348 13.8889 6.77235 13.9126 6.34496V6.22624C13.8889 5.51392 13.5802 5.15776 12.8679 5.13402C12.203 5.11027 11.5145 5.11027 10.8496 5.13402C10.2086 5.15776 9.82866 5.46643 9.80492 6.10752C9.78117 7.15225 9.78117 8.19698 9.80492 9.24172C9.82866 9.8828 10.2086 10.2152 10.8734 10.239C11.5145 10.2627 12.1793 10.2627 12.8204 10.239C13.4852 10.2152 13.8651 9.85906 13.8889 9.21797C13.9046 8.87062 13.8994 8.53376 13.8942 8.19344ZM12.8905 8.27282C12.8663 8.09385 12.8423 7.91672 12.8681 7.77068C12.8681 6.90728 12.8681 6.4923 12.6608 6.29289C12.4633 6.10295 12.0777 6.1086 11.3247 6.1086C10.8736 6.1086 10.7786 6.27481 10.8024 6.65471C10.8261 7.2958 10.8499 7.96063 10.8024 8.60171C10.7786 9.07659 10.9211 9.21906 11.396 9.17157C11.5577 9.15454 11.7408 9.16804 11.9223 9.18142C12.247 9.20535 12.5666 9.2289 12.7494 9.07659C12.9767 8.90991 12.9332 8.5885 12.8905 8.27282Z"
      fill="#0A0D12"
    />
  </svg>
);

export const calenderIcon = () => (
  <svg width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path
      fill-rule="evenodd"
      clip-rule="evenodd"
      d="M6 1.6875C6.31066 1.6875 6.5625 1.93934 6.5625 2.25V3.1875H11.4375V2.25C11.4375 1.93934 11.6893 1.6875 12 1.6875C12.3107 1.6875 12.5625 1.93934 12.5625 2.25V3.1875H13.5C14.047 3.1875 14.5716 3.4048 14.9584 3.79159C15.3452 4.17839 15.5625 4.70299 15.5625 5.25V14.25C15.5625 14.797 15.3452 15.3216 14.9584 15.7084C14.5716 16.0952 14.047 16.3125 13.5 16.3125H4.5C3.95299 16.3125 3.42839 16.0952 3.04159 15.7084C2.6548 15.3216 2.4375 14.797 2.4375 14.25V5.25C2.4375 4.70299 2.6548 4.17839 3.04159 3.79159C3.42839 3.4048 3.95299 3.1875 4.5 3.1875H5.4375V2.25C5.4375 1.93934 5.68934 1.6875 6 1.6875ZM5.4375 4.3125H4.5C4.25136 4.3125 4.0129 4.41127 3.83709 4.58709C3.66127 4.7629 3.5625 5.00136 3.5625 5.25V7.6875H14.4375V5.25C14.4375 5.00136 14.3387 4.7629 14.1629 4.58709C13.9871 4.41127 13.7486 4.3125 13.5 4.3125H12.5625V5.25C12.5625 5.56066 12.3107 5.8125 12 5.8125C11.6893 5.8125 11.4375 5.56066 11.4375 5.25V4.3125H6.5625V5.25C6.5625 5.56066 6.31066 5.8125 6 5.8125C5.68934 5.8125 5.4375 5.56066 5.4375 5.25V4.3125ZM14.4375 8.8125H3.5625V14.25C3.5625 14.4986 3.66127 14.7371 3.83709 14.9129C4.0129 15.0887 4.25136 15.1875 4.5 15.1875H13.5C13.7486 15.1875 13.9871 15.0887 14.1629 14.9129C14.3387 14.7371 14.4375 14.4986 14.4375 14.25V8.8125ZM5.4375 11.25C5.4375 10.9393 5.68934 10.6875 6 10.6875H7.5C7.81066 10.6875 8.0625 10.9393 8.0625 11.25V12.75C8.0625 13.0607 7.81066 13.3125 7.5 13.3125H6C5.68934 13.3125 5.4375 13.0607 5.4375 12.75V11.25ZM6.5625 11.8125V12.1875H6.9375V11.8125H6.5625Z"
      fill="#252B37"
    />
  </svg>
);

export const radioSelectedIcon = () => (
  <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
    <g clip-path="url(#clip0_233_7011)">
      <circle cx="8.00056" cy="8.00007" r="7.1822" fill="#363F72" stroke="#363F72" stroke-width="0.798022" />
      <path
        d="M4.41016 8.00002L6.80422 10.3941L11.5924 5.60596"
        stroke="#FAFAFA"
        stroke-width="1.19703"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
    </g>
    <defs>
      <clipPath id="clip0_233_7011">
        <rect width="16" height="16" fill="white" />
      </clipPath>
    </defs>
  </svg>
);

export const downloadReportIcon = () => (
  <svg width="65" height="65" viewBox="0 0 65 65" fill="none" xmlns="http://www.w3.org/2000/svg">
    <g clip-path="url(#clip0_233_14320)">
      <path
        d="M28.45 26.3128L30.55 28.9378C30.6936 29.1172 30.8764 29.2612 31.0845 29.3587C31.2926 29.4563 31.5203 29.5047 31.75 29.5003H50.5C50.8978 29.5003 51.2794 29.6583 51.5607 29.9396C51.842 30.2209 52 30.6025 52 31.0003V54.2503C52 54.6481 51.842 55.0296 51.5607 55.3109C51.2794 55.5922 50.8978 55.7503 50.5 55.7503H14.5C14.1022 55.7503 13.7206 55.5922 13.4393 55.3109C13.158 55.0296 13 54.6481 13 54.2503V27.2503C13 26.8525 13.158 26.4709 13.4393 26.1896C13.7206 25.9083 14.1022 25.7503 14.5 25.7503H27.25C27.4797 25.7459 27.7074 25.7943 27.9155 25.8918C28.1236 25.9893 28.3064 26.1334 28.45 26.3128Z"
        fill="#FDFDFD"
      />
      <path
        d="M50.5 53.5H14.5C14.1022 53.5 13.7206 53.342 13.4393 53.0607C13.158 52.7794 13 52.3978 13 52V54.25C13 54.6478 13.158 55.0294 13.4393 55.3107C13.7206 55.592 14.1022 55.75 14.5 55.75H50.5C50.8978 55.75 51.2794 55.592 51.5607 55.3107C51.842 55.0294 52 54.6478 52 54.25V52C52 52.3978 51.842 52.7794 51.5607 53.0607C51.2794 53.342 50.8978 53.5 50.5 53.5Z"
        fill="#FDFDFD"
      />
      <path
        d="M46 15.25V42.25C46 42.6478 45.842 43.0294 45.5607 43.3107C45.2794 43.592 44.8978 43.75 44.5 43.75H20.5C20.1022 43.75 19.7206 43.592 19.4393 43.3107C19.158 43.0294 19 42.6478 19 42.25V10.75C19 10.3522 19.158 9.97064 19.4393 9.68934C19.7206 9.40804 20.1022 9.25 20.5 9.25H40L46 15.25Z"
        fill="#FDFDFD"
      />
      <path
        d="M44.5 41.5H20.5C20.1022 41.5 19.7206 41.342 19.4393 41.0607C19.158 40.7794 19 40.3978 19 40V42.25C19 42.6478 19.158 43.0294 19.4393 43.3107C19.7206 43.592 20.1022 43.75 20.5 43.75H44.5C44.8978 43.75 45.2794 43.592 45.5607 43.3107C45.842 43.0294 46 42.6478 46 42.25V40C46 40.3978 45.842 40.7794 45.5607 41.0607C45.2794 41.342 44.8978 41.5 44.5 41.5Z"
        fill="#FDFDFD"
      />
      <path
        d="M32.5 34C37.0563 34 40.75 30.3063 40.75 25.75C40.75 21.1937 37.0563 17.5 32.5 17.5C27.9437 17.5 24.25 21.1937 24.25 25.75C24.25 30.3063 27.9437 34 32.5 34Z"
        fill="#FDFDFD"
      />
      <path
        d="M40.75 25.75C40.7504 26.8335 40.5373 27.9065 40.1229 28.9077C39.7085 29.9089 39.101 30.8186 38.335 31.585L32.5 25.75H40.75Z"
        fill="#FDFDFD"
      />
      <path
        d="M40.75 25.75H32.5V17.5C34.688 17.5 36.7865 18.3692 38.3336 19.9164C39.8808 21.4635 40.75 23.562 40.75 25.75Z"
        fill="#4E5BA6"
      />
      <path d="M46 15.25H40V9.25L46 15.25Z" fill="#4E5BA6" />
      <path d="M34.75 46.75V40H30.25V46.75H28L32.5 52.75L37 46.75H34.75Z" fill="#4E5BA6" />
      <path
        d="M43.75 55H42.25C42.0511 55 41.8603 55.079 41.7197 55.2197C41.579 55.3603 41.5 55.5511 41.5 55.75C41.5 55.9489 41.579 56.1397 41.7197 56.2803C41.8603 56.421 42.0511 56.5 42.25 56.5H43.75C43.9489 56.5 44.1397 56.421 44.2803 56.2803C44.421 56.1397 44.5 55.9489 44.5 55.75C44.5 55.5511 44.421 55.3603 44.2803 55.2197C44.1397 55.079 43.9489 55 43.75 55Z"
        fill="#181D27"
      />
      <path
        d="M50.5 28.75H46.75V15.25C46.7506 15.1513 46.7317 15.0535 46.6943 14.9621C46.657 14.8707 46.602 14.7876 46.5325 14.7175L40.5325 8.71751C40.4624 8.648 40.3793 8.59301 40.2879 8.55568C40.1966 8.51836 40.0987 8.49944 40 8.50001H20.5C19.9033 8.50001 19.331 8.73707 18.909 9.15902C18.4871 9.58098 18.25 10.1533 18.25 10.75V22.75C18.25 22.9489 18.329 23.1397 18.4697 23.2803C18.6103 23.421 18.8011 23.5 19 23.5C19.1989 23.5 19.3897 23.421 19.5303 23.2803C19.671 23.1397 19.75 22.9489 19.75 22.75V10.75C19.75 10.5511 19.829 10.3603 19.9697 10.2197C20.1103 10.079 20.3011 10 20.5 10H39.25V15.25C39.25 15.4489 39.329 15.6397 39.4697 15.7803C39.6103 15.921 39.8011 16 40 16H45.25V42.25C45.25 42.4489 45.171 42.6397 45.0303 42.7803C44.8897 42.921 44.6989 43 44.5 43H35.5V40C35.5 39.8011 35.421 39.6103 35.2803 39.4697C35.1397 39.329 34.9489 39.25 34.75 39.25H30.25C30.0511 39.25 29.8603 39.329 29.7197 39.4697C29.579 39.6103 29.5 39.8011 29.5 40V43H20.5C20.3011 43 20.1103 42.921 19.9697 42.7803C19.829 42.6397 19.75 42.4489 19.75 42.25V25.75C19.75 25.5511 19.671 25.3603 19.5303 25.2197C19.3897 25.079 19.1989 25 19 25H14.5C13.9033 25 13.331 25.2371 12.909 25.659C12.4871 26.081 12.25 26.6533 12.25 27.25V54.25C12.25 54.8467 12.4871 55.419 12.909 55.841C13.331 56.263 13.9033 56.5 14.5 56.5H39.25C39.4489 56.5 39.6397 56.421 39.7803 56.2803C39.921 56.1397 40 55.9489 40 55.75C40 55.5511 39.921 55.3603 39.7803 55.2197C39.6397 55.079 39.4489 55 39.25 55H14.5C14.3011 55 14.1103 54.921 13.9697 54.7803C13.829 54.6397 13.75 54.4489 13.75 54.25V27.25C13.75 27.0511 13.829 26.8603 13.9697 26.7197C14.1103 26.579 14.3011 26.5 14.5 26.5H18.25V42.25C18.25 42.8467 18.4871 43.419 18.909 43.841C19.331 44.263 19.9033 44.5 20.5 44.5H29.5V46H28C27.8607 46 27.7242 46.0388 27.6057 46.112C27.4872 46.1852 27.3915 46.29 27.3292 46.4146C27.2669 46.5392 27.2405 46.6786 27.253 46.8174C27.2655 46.9561 27.3164 47.0886 27.4 47.2L31.9 53.2C31.9699 53.2932 32.0604 53.3688 32.1646 53.4208C32.2687 53.4729 32.3836 53.5 32.5 53.5C32.6164 53.5 32.7313 53.4729 32.8354 53.4208C32.9396 53.3688 33.0301 53.2932 33.1 53.2L37.6 47.2C37.6836 47.0886 37.7345 46.9561 37.747 46.8174C37.7595 46.6786 37.7331 46.5392 37.6708 46.4146C37.6085 46.29 37.5128 46.1852 37.3943 46.112C37.2758 46.0388 37.1393 46 37 46H35.5V44.5H44.5C45.0967 44.5 45.669 44.263 46.091 43.841C46.5129 43.419 46.75 42.8467 46.75 42.25V30.25H50.5C50.6989 30.25 50.8897 30.329 51.0303 30.4697C51.171 30.6103 51.25 30.8011 51.25 31V54.25C51.25 54.4489 51.171 54.6397 51.0303 54.7803C50.8897 54.921 50.6989 55 50.5 55H46.75C46.5511 55 46.3603 55.079 46.2197 55.2197C46.079 55.3603 46 55.5511 46 55.75C46 55.9489 46.079 56.1397 46.2197 56.2803C46.3603 56.421 46.5511 56.5 46.75 56.5H50.5C51.0967 56.5 51.669 56.263 52.091 55.841C52.5129 55.419 52.75 54.8467 52.75 54.25V31C52.75 30.4033 52.5129 29.831 52.091 29.409C51.669 28.9871 51.0967 28.75 50.5 28.75ZM32.5 51.4975L29.5 47.5H30.25C30.4489 47.5 30.6397 47.421 30.7803 47.2803C30.921 47.1397 31 46.9489 31 46.75V40.75H34V46.75C34 46.9489 34.079 47.1397 34.2197 47.2803C34.3603 47.421 34.5511 47.5 34.75 47.5H35.5L32.5 51.4975ZM40.75 11.0575L44.1925 14.5H40.75V11.0575Z"
        fill="#181D27"
      />
      <path
        d="M23.5 25.75C23.5 27.53 24.0278 29.2701 25.0168 30.7501C26.0057 32.2302 27.4113 33.3837 29.0558 34.0649C30.7004 34.7461 32.51 34.9243 34.2558 34.5771C36.0016 34.2298 37.6053 33.3726 38.864 32.114C40.1226 30.8553 40.9798 29.2516 41.3271 27.5058C41.6743 25.76 41.4961 23.9504 40.8149 22.3058C40.1337 20.6613 38.9802 19.2557 37.5001 18.2668C36.0201 17.2778 34.28 16.75 32.5 16.75C30.1131 16.75 27.8239 17.6982 26.136 19.386C24.4482 21.0739 23.5 23.3631 23.5 25.75ZM38.305 30.49L34.3075 26.5H40C39.8401 27.9679 39.2504 29.3558 38.305 30.49ZM40 25H33.25V18.2875C34.9781 18.4523 36.5955 19.2115 37.8265 20.4356C39.0574 21.6597 39.8256 23.2728 40 25ZM31.75 18.2875C31.75 26.4625 31.75 25.84 31.81 26.035C31.87 26.23 31.57 25.885 37.24 31.555C36.3342 32.2963 35.2674 32.8153 34.1251 33.0705C32.9827 33.3257 31.7965 33.3099 30.6614 33.0246C29.5262 32.7392 28.4736 32.192 27.5877 31.427C26.7019 30.6619 26.0074 29.7001 25.5599 28.6185C25.1123 27.537 24.9242 26.3657 25.0104 25.1984C25.0966 24.0311 25.4549 22.9001 26.0565 21.8961C26.6581 20.892 27.4863 20.0427 28.475 19.4161C29.4636 18.7895 30.5852 18.403 31.75 18.2875Z"
        fill="#181D27"
      />
      <path
        d="M22.75 16.7498H24.6925L25.2175 17.2823C25.3587 17.4236 25.5503 17.5029 25.75 17.5029C25.9497 17.5029 26.1413 17.4236 26.2825 17.2823C26.4237 17.1411 26.5031 16.9496 26.5031 16.7498C26.5031 16.5501 26.4237 16.3586 26.2825 16.2173L25.5325 15.4673C25.24 15.1748 25.1425 15.2498 22.75 15.2498C22.5511 15.2498 22.3603 15.3288 22.2197 15.4695C22.079 15.6102 22 15.8009 22 15.9998C22 16.1987 22.079 16.3895 22.2197 16.5302C22.3603 16.6708 22.5511 16.7498 22.75 16.7498Z"
        fill="#181D27"
      />
      <path
        d="M22.75 13.75H25C25.1989 13.75 25.3897 13.671 25.5303 13.5303C25.671 13.3897 25.75 13.1989 25.75 13C25.75 12.8011 25.671 12.6103 25.5303 12.4697C25.3897 12.329 25.1989 12.25 25 12.25H22.75C22.5511 12.25 22.3603 12.329 22.2197 12.4697C22.079 12.6103 22 12.8011 22 13C22 13.1989 22.079 13.3897 22.2197 13.5303C22.3603 13.671 22.5511 13.75 22.75 13.75Z"
        fill="#181D27"
      />
      <path
        d="M42.25 39.25H39.25C39.0511 39.25 38.8603 39.329 38.7197 39.4697C38.579 39.6103 38.5 39.8011 38.5 40C38.5 40.1989 38.579 40.3897 38.7197 40.5303C38.8603 40.671 39.0511 40.75 39.25 40.75H42.25C42.4489 40.75 42.6397 40.671 42.7803 40.5303C42.921 40.3897 43 40.1989 43 40C43 39.8011 42.921 39.6103 42.7803 39.4697C42.6397 39.329 42.4489 39.25 42.25 39.25Z"
        fill="#FDFDFD"
      />
      <path
        d="M39.25 37.75H42.25C42.4489 37.75 42.6397 37.671 42.7803 37.5303C42.921 37.3897 43 37.1989 43 37C43 36.8011 42.921 36.6103 42.7803 36.4697C42.6397 36.329 42.4489 36.25 42.25 36.25H39.25C39.0511 36.25 38.8603 36.329 38.7197 36.4697C38.579 36.6103 38.5 36.8011 38.5 37C38.5 37.1989 38.579 37.3897 38.7197 37.5303C38.8603 37.671 39.0511 37.75 39.25 37.75Z"
        fill="#FDFDFD"
      />
      <path
        d="M49 25.75C48.8517 25.75 48.7067 25.794 48.5833 25.8764C48.46 25.9588 48.3639 26.0759 48.3071 26.213C48.2503 26.35 48.2355 26.5008 48.2644 26.6463C48.2933 26.7918 48.3648 26.9254 48.4697 27.0303C48.5746 27.1352 48.7082 27.2067 48.8537 27.2356C48.9992 27.2645 49.15 27.2497 49.287 27.1929C49.4241 27.1361 49.5412 27.04 49.6236 26.9167C49.706 26.7933 49.75 26.6483 49.75 26.5C49.75 26.3011 49.671 26.1103 49.5303 25.9697C49.3897 25.829 49.1989 25.75 49 25.75Z"
        fill="#FDFDFD"
      />
      <path
        d="M49 24.25C49.1483 24.25 49.2933 24.206 49.4167 24.1236C49.54 24.0412 49.6361 23.9241 49.6929 23.787C49.7497 23.65 49.7645 23.4992 49.7356 23.3537C49.7067 23.2082 49.6352 23.0746 49.5303 22.9697C49.4254 22.8648 49.2918 22.7934 49.1463 22.7644C49.0008 22.7355 48.85 22.7503 48.713 22.8071C48.5759 22.8639 48.4588 22.96 48.3764 23.0833C48.294 23.2067 48.25 23.3517 48.25 23.5C48.25 23.6989 48.329 23.8897 48.4697 24.0303C48.6103 24.171 48.8011 24.25 49 24.25Z"
        fill="#FDFDFD"
      />
    </g>
    <defs>
      <clipPath id="clip0_233_14320">
        <rect width="48" height="48" fill="white" transform="translate(8.5 8.5)" />
      </clipPath>
    </defs>
  </svg>
);
export const revenueReportIcon = () => (
  <svg width="37" height="36" viewBox="0 0 37 36" fill="none" xmlns="http://www.w3.org/2000/svg">
    <rect x="0.0410156" width="36" height="36" rx="8" fill="#EAECF5" />
    <path
      d="M18.0975 22.9263C18.0975 25.0568 16.2608 26.8445 14.1303 26.8445C11.9263 26.8445 10.1387 25.0323 10.1387 22.8038C10.1387 20.7223 11.9998 18.9102 14.1303 18.9102C16.3098 18.9102 18.0975 20.7223 18.0975 22.9263Z"
      fill="#4E5BA6"
    />
    <path
      fill-rule="evenodd"
      clip-rule="evenodd"
      d="M27.1684 19.1757L27.1684 19.1735C27.1698 18.6 27.1713 18.0258 27.1713 17.4501V10.6831C27.1713 8.92601 26.1978 8 24.4408 8C23.6099 8.00478 22.78 8.01051 21.9509 8.01624C18.6572 8.039 15.3756 8.06168 12.0939 8.02374C10.8593 8.02374 9.5771 9.04473 9.62459 10.5406C9.64833 11.3954 9.98074 11.7515 10.8355 11.7753C11.5478 11.799 12.2839 11.799 13.02 11.7753C13.4711 11.7515 13.6373 11.9177 13.6373 12.3926C13.6136 13.936 13.5898 15.4793 13.6373 17.0227C13.661 17.6163 13.5186 17.83 12.9012 17.9487C10.6931 18.3286 8.95975 20.4418 8.88852 22.6737C8.81728 24.9057 10.1232 26.8764 12.1889 27.66C14.2784 28.4435 16.5103 27.8499 17.9587 26.1166C18.4336 25.5468 18.5998 25.523 19.1696 25.9979C20.0244 26.7102 20.9029 26.7102 21.734 26.0216C22.4226 25.4518 22.9687 25.428 23.6335 26.0216C24.2746 26.5915 25.0106 26.7102 25.723 26.354C26.5065 25.9504 27.2188 25.428 27.1951 24.3358C27.1595 22.6091 27.1639 20.8957 27.1684 19.1757ZM23.8712 20.8693H18.695C19.0037 21.6291 19.1224 22.3177 19.0987 23.1249C19.0898 23.2935 19.0412 23.4753 18.9936 23.653L18.9936 23.653L18.9936 23.653C18.9139 23.951 18.8372 24.2375 18.9562 24.4309C19.0561 24.5932 19.2675 24.6898 19.494 24.7933C19.6982 24.8866 19.9145 24.9855 20.0722 25.1432C20.3571 25.4281 20.5945 25.4044 20.9032 25.1669C22.1379 24.1697 23.2064 24.1697 24.4885 25.1432C24.5247 25.1658 24.5591 25.1901 24.5929 25.2139C24.737 25.3157 24.8706 25.41 25.0821 25.3331C25.557 25.1669 25.9844 24.5733 25.9844 24.0272V11.0393C25.9844 9.73338 25.4383 9.21101 24.1324 9.21101H17.674H15.2522C14.8485 9.18727 14.6586 9.30599 14.8248 9.75712C14.9672 10.1608 14.9672 10.6119 14.9672 11.0393V17.4976C14.9672 17.8775 15.6321 18.2812 15.917 18.0438C16.1782 17.8301 16.4631 17.8301 16.748 17.8301H23.7525C23.8466 17.8301 23.9433 17.8275 24.0409 17.8249C24.2387 17.8196 24.4402 17.8142 24.631 17.8301C24.9159 17.8301 25.0584 18.02 25.0821 18.2812C25.1059 18.5661 24.9872 18.7561 24.7022 18.8036C24.4886 18.8273 24.2749 18.851 24.0612 18.851H17.7215C17.6732 18.851 17.6228 18.843 17.5734 18.8351C17.4529 18.8159 17.3378 18.7975 17.2704 18.8985C17.7215 19.7533 18.4338 19.872 19.3124 19.8483C20.3631 19.7974 21.4016 19.8071 22.4454 19.8168C22.8641 19.8207 23.2836 19.8245 23.705 19.8245C23.7824 19.8245 23.8624 19.822 23.9441 19.8194H23.9441H23.9441H23.9441H23.9442C24.1129 19.8141 24.2888 19.8085 24.4648 19.8245C24.821 19.8245 25.1059 19.967 25.0821 20.3707C25.0584 20.7506 24.7972 20.8693 24.4648 20.8693H23.8712ZM13.9692 26.7102C11.8086 26.7102 10.1465 25.0244 10.1465 22.8637C10.1465 20.7268 11.9035 18.9697 14.0167 18.9697C16.0825 18.9697 17.887 20.7743 17.8633 22.8637C17.8395 25.2381 15.94 26.8052 13.9692 26.7102ZM11.8072 10.559C11.9338 10.5617 12.0613 10.5644 12.1889 10.5644H13.4473C13.756 10.5644 13.8272 10.4219 13.756 10.1607C13.5898 9.56713 12.9249 9.04476 12.3076 9.04476C11.619 9.02102 10.9779 9.51964 10.788 10.137C10.693 10.4457 10.7167 10.5881 11.0729 10.5644C11.3095 10.5486 11.5566 10.5538 11.8072 10.559Z"
      fill="#0A0D12"
    />
    <path
      d="M23.0639 13.1523C23.5387 13.1523 24.0136 13.1523 24.4885 13.1523C24.8447 13.1523 25.0821 13.2948 25.1059 13.651C25.1296 14.0071 24.8447 14.1733 24.536 14.1971C23.5625 14.2208 22.5652 14.2208 21.5917 14.1971C21.2831 14.1971 20.9982 14.0546 21.0219 13.6747C21.0219 13.2948 21.2831 13.1761 21.6155 13.1761C22.0904 13.1523 22.5652 13.1523 23.0639 13.1523Z"
      fill="#0A0D12"
    />
    <path
      d="M23.0158 16.2393C22.5409 16.2393 22.0661 16.2393 21.5912 16.2393C21.2588 16.2393 21.0213 16.1206 20.9976 15.7407C20.9738 15.3608 21.235 15.1946 21.5675 15.1946C22.541 15.1708 23.5382 15.1708 24.5117 15.1946C24.8204 15.1946 25.1053 15.3608 25.0816 15.7169C25.0578 16.0731 24.8204 16.2393 24.488 16.2393C23.9893 16.2393 23.4907 16.2393 23.0158 16.2393Z"
      fill="#0A0D12"
    />
    <path
      d="M22.9921 12.1073C22.5172 12.1073 22.0423 12.1073 21.5675 12.1073C21.235 12.1073 20.9738 11.9886 20.9976 11.6087C20.9976 11.2525 21.2113 11.1338 21.5437 11.1338C22.5172 11.1338 23.467 11.1338 24.4405 11.1338C24.7966 11.1338 25.0816 11.2288 25.0816 11.6562C25.0816 12.0835 24.7729 12.1548 24.4167 12.131C23.9181 12.1073 23.467 12.1073 22.9921 12.1073Z"
      fill="#0A0D12"
    />
    <path
      d="M16.6764 21.368C16.6527 21.5817 16.534 21.7479 16.3915 21.8904C15.5367 22.8164 14.6582 23.7424 13.8034 24.6684C13.471 25.0246 13.1386 25.0246 12.7824 24.6922C12.3788 24.2885 11.9752 23.8849 11.5715 23.4812C11.3103 23.22 11.1916 22.9114 11.5003 22.6027C11.8089 22.294 12.0939 22.4365 12.3788 22.6977C12.6637 22.9826 12.9249 23.505 13.2811 23.4812C13.6135 23.4337 13.8747 22.9589 14.1596 22.6739C14.6582 22.1516 15.1568 21.6054 15.6554 21.0593C15.8454 20.8456 16.0828 20.7744 16.344 20.8931C16.5815 20.9644 16.6527 21.1543 16.6764 21.368Z"
      fill="#FDFDFD"
    />
    <path
      fill-rule="evenodd"
      clip-rule="evenodd"
      d="M19.9352 14.1934C19.9325 14.022 19.9299 13.8496 19.9299 13.6746C19.9299 13.2235 19.9299 12.7723 19.9536 12.345V12.2262C19.9299 11.5139 19.6212 11.1578 18.9089 11.134C18.2441 11.1103 17.5555 11.1103 16.8907 11.134C16.2496 11.1578 15.8697 11.4664 15.8459 12.1075C15.8222 13.1523 15.8222 14.197 15.8459 15.2417C15.8697 15.8828 16.2496 16.2152 16.9144 16.239C17.5555 16.2627 18.2203 16.2627 18.8614 16.239C19.5262 16.2152 19.9061 15.8591 19.9299 15.218C19.9457 14.8706 19.9405 14.5338 19.9352 14.1934ZM18.9315 14.2728C18.9073 14.0938 18.8833 13.9167 18.9091 13.7707C18.9091 12.9073 18.9091 12.4923 18.7018 12.2929C18.5043 12.103 18.1187 12.1086 17.3658 12.1086C16.9146 12.1086 16.8196 12.2748 16.8434 12.6547C16.8671 13.2958 16.8909 13.9606 16.8434 14.6017C16.8196 15.0766 16.9621 15.2191 17.437 15.1716C17.5987 15.1545 17.7818 15.168 17.9633 15.1814C18.2881 15.2053 18.6076 15.2289 18.7904 15.0766C19.0177 14.9099 18.9742 14.5885 18.9315 14.2728Z"
      fill="#0A0D12"
    />
  </svg>
);

export const cashFlowReportIcon = () => (
  <svg width="37" height="36" viewBox="0 0 37 36" fill="none" xmlns="http://www.w3.org/2000/svg">
    <rect x="0.0410156" width="36" height="36" rx="8" fill="#EAECF5" />
    <path
      d="M26.8466 23.0825H9.15392C8.87898 23.0825 8.65625 22.8596 8.65625 22.5848V13.3976C8.65625 13.1226 8.87915 12.8999 9.15392 12.8999H26.8466C27.1214 12.8999 27.3441 13.1228 27.3441 13.3976V22.5848C27.3441 22.8596 27.1214 23.0825 26.8466 23.0825Z"
      fill="#EAECF5"
    />
    <path
      d="M27.3439 13.3972V22.5845C27.3439 22.8596 27.1211 23.0823 26.846 23.0823H25.8574C26.132 23.0823 26.3548 22.8596 26.3548 22.5845V13.3972C26.3548 13.1226 26.132 12.8999 25.8574 12.8999H26.846C27.1211 12.8999 27.3439 13.1226 27.3439 13.3972Z"
      fill="#EAECF5"
    />
    <path
      d="M25.5515 16.2141V19.7681C25.5515 19.8794 25.4632 19.9704 25.3521 19.9737C25.3177 19.9745 25.2827 19.9769 25.2477 19.9801C24.6585 20.034 24.1699 20.4788 24.0727 21.0624C24.0612 21.1319 24.0541 21.1997 24.0518 21.266C24.0477 21.3751 23.9554 21.46 23.8463 21.46H12.1251C12.016 21.46 11.9237 21.3751 11.9196 21.266C11.9173 21.1997 11.9102 21.1319 11.8987 21.0625C11.8016 20.4791 11.3134 20.0343 10.7244 19.9801C10.689 19.9769 10.6541 19.9745 10.6193 19.9737C10.5082 19.9704 10.4199 19.8794 10.4199 19.7681V16.2141C10.4199 16.1028 10.5082 16.0119 10.6193 16.0085C10.6541 16.0075 10.689 16.0053 10.7244 16.0021C11.3134 15.9479 11.8016 15.5031 11.8987 14.9197C11.9102 14.8504 11.9173 14.7825 11.9196 14.7162C11.9237 14.6071 12.016 14.5222 12.1251 14.5222H23.8463C23.9554 14.5222 24.0477 14.6071 24.0518 14.7162C24.0541 14.7825 24.0612 14.8504 24.0727 14.9198C24.1699 15.5034 24.6585 15.9482 25.2477 16.0021C25.2827 16.0053 25.3175 16.0075 25.3521 16.0085C25.4632 16.0119 25.5515 16.1028 25.5515 16.2141Z"
      fill="#EAECF5"
    />
    <path
      d="M25.5506 16.214V19.7682C25.5506 19.8791 25.4625 19.9703 25.3512 19.9733C25.317 19.9746 25.282 19.9768 25.2471 19.9798C24.6577 20.0337 24.1694 20.4785 24.0719 21.0623C24.0606 21.1318 24.0532 21.1996 24.0511 21.2661C24.0472 21.3748 23.9549 21.4599 23.8456 21.4599H22.8711C22.9804 21.4599 23.0727 21.375 23.0767 21.2661C23.0792 21.1996 23.0861 21.1318 23.0979 21.0623C23.1467 20.7709 23.2969 20.5029 23.5178 20.307C23.6979 20.1474 23.9084 20.02 24.0878 19.861C24.3193 19.6561 24.4376 19.4639 24.5272 19.1715C24.5999 18.9342 24.5712 18.6664 24.5991 18.4212C24.6303 18.1486 24.6023 17.8732 24.6001 17.5999C24.5957 17.0695 24.5504 16.5232 24.1727 16.1143C24.0312 15.9613 23.8866 15.9238 23.7095 15.8162C23.3876 15.621 23.1605 15.2912 23.0977 14.9196C23.0861 14.8506 23.0792 14.7823 23.0765 14.7163C23.0727 14.6071 22.9804 14.522 22.8711 14.522H23.8456C23.9549 14.522 24.0472 14.6071 24.0511 14.7163C24.0532 14.7823 24.0606 14.8506 24.0719 14.9198C24.1694 15.5034 24.6577 15.9482 25.247 16.002C25.282 16.0056 25.317 16.0078 25.351 16.0086C25.4625 16.0116 25.5506 16.1026 25.5506 16.214Z"
      fill="#EAECF5"
    />
    <path
      d="M20.2219 17.9911C20.2219 19.2184 19.2269 20.2134 17.9996 20.2134C16.7723 20.2134 15.7773 19.2184 15.7773 17.9911C15.7773 16.7638 16.7723 15.7688 17.9996 15.7688C19.2269 15.7688 20.2219 16.7638 20.2219 17.9911Z"
      fill="#EAECF5"
    />
    <path
      d="M20.2224 17.991C20.2224 19.2183 19.2272 20.2133 18.0001 20.2133C17.4893 20.2133 17.0187 20.0411 16.6436 19.7514C16.8178 19.7957 17.0006 19.8188 17.1886 19.8188C18.4158 19.8188 19.4108 18.8244 19.4108 17.5966C19.4108 16.8799 19.0718 16.2425 18.5449 15.8362C19.5089 16.0791 20.2224 16.9517 20.2224 17.991Z"
      fill="#EAECF5"
    />
    <path
      d="M21.1114 11.0845L24.5767 13.5501C24.6413 13.5961 24.7311 13.5498 24.7311 13.4704V11.9883C24.7311 11.9343 24.7749 11.8906 24.829 11.8906C25.1096 11.8906 25.3918 11.854 25.6721 11.8587C26.6733 11.8755 27.8128 11.7622 28.4489 12.7295C28.5213 12.8396 28.6925 12.7772 28.675 12.6466C28.6351 12.3477 28.5555 11.9749 28.3987 11.5971C27.9785 10.5836 26.9719 9.93739 25.8748 9.93739H24.8288C24.7748 9.93739 24.7311 9.89375 24.7311 9.8397V8.53906C24.7311 8.45966 24.6413 8.41334 24.5767 8.4595L21.1114 10.9252C21.0566 10.9641 21.0566 11.0453 21.1114 11.0845Z"
      fill="#4E5BA6"
    />
    <path
      d="M28.5221 12.7828C28.4875 12.6705 28.4469 12.556 28.3994 12.4407C28.15 11.8351 27.6719 11.3389 27.0833 11.0544C26.3824 10.7156 25.7868 10.8691 25.05 10.854C24.8761 10.8505 24.3867 10.8955 24.3187 10.69C24.2992 10.6315 24.3069 10.5678 24.3061 10.5062C24.2972 9.88287 23.7395 9.93792 23.321 10.1904C23.0508 10.3535 22.8106 10.5574 22.5541 10.7402C22.233 10.969 21.9118 11.1973 21.5907 11.4257L21.1115 11.0846C21.0566 11.0454 21.0566 10.9641 21.1115 10.9254L24.5767 8.45953C24.6411 8.41337 24.7309 8.45953 24.7309 8.53892V9.8399C24.7309 9.89378 24.7749 9.93742 24.8289 9.93742H25.8746C26.9717 9.93742 27.9786 10.5838 28.3987 11.5971C28.5553 11.9749 28.6349 12.3479 28.675 12.6466C28.6874 12.7421 28.5988 12.8012 28.5221 12.7828Z"
      fill="#4E5BA6"
    />
    <path
      d="M14.888 24.8981L11.4228 22.4324C11.3582 22.3862 11.2684 22.4326 11.2684 22.5121V23.994C11.2684 24.0481 11.2246 24.0919 11.1705 24.0919C10.8897 24.0919 10.6077 24.1285 10.3274 24.1238C9.32622 24.107 8.18671 24.2203 7.55057 23.253C7.47823 23.1429 7.30703 23.2052 7.32448 23.3359C7.36443 23.6347 7.44399 24.0075 7.60076 24.3855C8.02105 25.3987 9.02762 26.045 10.1247 26.045H11.1707C11.2247 26.045 11.2684 26.0888 11.2684 26.1428V27.4433C11.2684 27.5228 11.3582 27.569 11.4228 27.523L14.8882 25.0573C14.9429 25.0184 14.9429 24.937 14.888 24.8981Z"
      fill="#4E5BA6"
    />
    <path
      d="M7.47816 23.1997C7.51274 23.3118 7.55336 23.4263 7.60086 23.5416C7.85028 24.1472 8.32831 24.6435 8.91695 24.928C9.61771 25.2667 10.2134 25.1133 10.9502 25.1283C11.1241 25.132 11.6136 25.0868 11.6815 25.2924C11.701 25.351 11.6933 25.4145 11.6941 25.4762C11.703 26.0996 12.2608 26.0445 12.6792 25.7921C12.9495 25.629 13.1897 25.4249 13.4461 25.2422C13.7672 25.0135 14.0883 24.785 14.4096 24.5568L14.8888 24.8978C14.9436 24.9371 14.9436 25.0182 14.8888 25.0571L11.4236 27.5228C11.3591 27.5691 11.2693 27.5228 11.2693 27.4434V26.1426C11.2693 26.0885 11.2253 26.0449 11.1713 26.0449H10.1258C9.02856 26.0449 8.02182 25.3987 7.6017 24.3852C7.44493 24.0076 7.36554 23.6344 7.32542 23.3357C7.31284 23.2402 7.40129 23.1811 7.47816 23.1997Z"
      fill="#4E5BA6"
    />
    <path
      d="M24.441 14.8584L24.441 14.8585C24.5101 15.2733 24.8552 15.591 25.2813 15.6299L25.2813 15.6299C25.3097 15.6326 25.337 15.6343 25.3624 15.635L25.3627 15.635C25.6777 15.6443 25.9248 15.8988 25.9248 16.2142V19.7681C25.9248 20.0835 25.6777 20.3382 25.3626 20.3473M24.441 14.8584L25.3612 20.2974M24.441 14.8584C24.4321 14.8052 24.4267 14.7528 24.4249 14.7028L24.4249 14.7027C24.4135 14.3916 24.1588 14.1485 23.8459 14.1485H12.1246C11.8117 14.1485 11.557 14.3916 11.5456 14.7027L11.5456 14.7028C11.5438 14.7526 11.5384 14.805 11.5295 14.8583L11.5295 14.8583C11.4604 15.2732 11.1155 15.5909 10.6897 15.6299L10.6897 15.6299C10.6613 15.6326 10.6339 15.6343 10.6082 15.635L10.608 15.635C10.2929 15.6441 10.0457 15.8988 10.0457 16.2142V17.7538C10.0457 17.9604 10.2131 18.1276 10.4195 18.1276C10.626 18.1276 10.7934 17.9602 10.7934 17.7538V16.3709C11.5404 16.2866 12.1439 15.72 12.2669 14.9813L12.2669 14.9813C12.2717 14.9529 12.2758 14.9244 12.2793 14.896H23.691L24.441 14.8584ZM25.3626 20.3473L25.3612 20.2974M25.3626 20.3473C25.3627 20.3473 25.3627 20.3473 25.3628 20.3473L25.3612 20.2974M25.3626 20.3473C25.3368 20.3482 25.3095 20.3498 25.2813 20.3524L25.2813 20.3524C24.8554 20.3913 24.5102 20.709 24.441 21.1238L24.441 21.1239C24.4321 21.177 24.4268 21.2294 24.4249 21.2796M25.3612 20.2974C25.3343 20.2982 25.306 20.2999 25.2768 20.3026C24.8286 20.3435 24.4647 20.6779 24.3917 21.1156C24.3825 21.1708 24.3769 21.2254 24.3749 21.2778M24.4249 21.2796L24.3749 21.2778M24.4249 21.2796C24.4137 21.5907 24.1587 21.8338 23.8459 21.8338H12.1248C11.8118 21.8338 11.557 21.5907 11.5456 21.2796L11.5456 21.2795C11.5438 21.2296 11.5385 21.1772 11.5296 21.124L11.5296 21.124C11.4604 20.7093 11.1155 20.3916 10.6897 20.3524L10.6896 20.3523C10.6622 20.3498 10.6346 20.3482 10.6077 20.3473M24.4249 21.2796C24.4249 21.2796 24.4249 21.2796 24.4249 21.2797L24.3749 21.2778M24.3749 21.2778L12.2795 21.0863M11.579 21.1158C11.506 20.6782 11.1422 20.3439 10.6942 20.3026C10.6657 20.2999 10.637 20.2982 10.6091 20.2974M11.579 21.1158L23.7358 14.846C23.74 14.8882 23.7457 14.9306 23.7529 14.9733C23.8741 15.701 24.4765 16.2568 25.2178 16.3248C25.2209 16.3249 25.2241 16.3253 25.2271 16.3256M11.579 21.1158C11.5882 21.171 11.5937 21.2256 11.5956 21.2778L11.579 21.1158ZM10.6091 20.2974C10.3215 20.289 10.0959 20.0565 10.0959 19.7681V19.2562C10.0959 19.0775 10.2407 18.9324 10.4196 18.9324C10.5984 18.9324 10.7434 19.0775 10.7434 19.2562V19.6567M10.6091 20.2974L10.6076 20.3473C10.6077 20.3473 10.6077 20.3473 10.6077 20.3473M10.6091 20.2974L10.6077 20.3473M10.7434 19.6567L10.7535 19.6577M10.7434 19.6567H10.7536L10.7535 19.6577M10.7434 19.6567L10.7484 19.6069L10.7484 19.6069L10.7484 19.6069L10.7485 19.6069L10.7485 19.6069L10.7485 19.6069L10.7485 19.607L10.7485 19.607L10.7486 19.607L10.7486 19.607L10.7486 19.607L10.7486 19.607L10.7486 19.607L10.7487 19.607L10.7487 19.607L10.7487 19.607L10.7487 19.607L10.7487 19.607L10.7488 19.607L10.7488 19.607L10.7488 19.607L10.7488 19.607L10.7488 19.607L10.7489 19.607L10.7489 19.607L10.7489 19.607L10.7489 19.607L10.7489 19.607L10.7489 19.607L10.749 19.607L10.749 19.607L10.749 19.607L10.749 19.607L10.749 19.607L10.7491 19.607L10.7491 19.607L10.7491 19.607L10.7491 19.607L10.7491 19.607L10.7492 19.607L10.7492 19.607L10.7492 19.607L10.7492 19.607L10.7492 19.607L10.7493 19.607L10.7493 19.607L10.7493 19.607L10.7493 19.607L10.7493 19.607L10.7494 19.607L10.7494 19.607L10.7494 19.607L10.7494 19.607L10.7494 19.607L10.7495 19.607L10.7495 19.607L10.7495 19.6071L10.7495 19.6071L10.7495 19.6071L10.7496 19.6071L10.7496 19.6071L10.7496 19.6071L10.7496 19.6071L10.7496 19.6071L10.7497 19.6071L10.7497 19.6071L10.7497 19.6071L10.7497 19.6071L10.7497 19.6071L10.7498 19.6071L10.7498 19.6071L10.7498 19.6071L10.7498 19.6071L10.7498 19.6071L10.7499 19.6071L10.7499 19.6071L10.7499 19.6071L10.7499 19.6071L10.7499 19.6071L10.75 19.6071L10.75 19.6071L10.75 19.6071L10.75 19.6071L10.75 19.6071L10.7501 19.6071L10.7501 19.6071L10.7501 19.6071L10.7501 19.6071L10.7501 19.6071L10.7501 19.6071L10.7502 19.6071L10.7502 19.6071L10.7502 19.6071L10.7502 19.6071L10.7502 19.6071L10.7503 19.6071L10.7503 19.6071L10.7503 19.6071L10.7503 19.6071L10.7503 19.6071L10.7504 19.6071L10.7504 19.6071L10.7504 19.6071L10.7504 19.6071L10.7504 19.6071L10.7505 19.6071L10.7505 19.6071L10.7505 19.6072L10.7505 19.6072L10.7505 19.6072L10.7506 19.6072L10.7506 19.6072L10.7506 19.6072L10.7506 19.6072L10.7506 19.6072L10.7507 19.6072L10.7507 19.6072L10.7507 19.6072L10.7507 19.6072L10.7507 19.6072L10.7508 19.6072L10.7508 19.6072L10.7508 19.6072L10.7508 19.6072L10.7508 19.6072L10.7509 19.6072L10.7509 19.6072L10.7509 19.6072L10.7509 19.6072L10.7509 19.6072L10.751 19.6072L10.751 19.6072L10.751 19.6072L10.751 19.6072L10.751 19.6072L10.7511 19.6072L10.7511 19.6072L10.7511 19.6072L10.7511 19.6072L10.7511 19.6072L10.7512 19.6072L10.7512 19.6072L10.7512 19.6072L10.7512 19.6072L10.7512 19.6072L10.7513 19.6072L10.7513 19.6072L10.7513 19.6072L10.7513 19.6072L10.7513 19.6072L10.7513 19.6072L10.7514 19.6072L10.7514 19.6072L10.7514 19.6072L10.7514 19.6072L10.7514 19.6072L10.7515 19.6072L10.7515 19.6072L10.7515 19.6073L10.7515 19.6073L10.7515 19.6073L10.7516 19.6073L10.7516 19.6073L10.7516 19.6073L10.7516 19.6073L10.7516 19.6073L10.7517 19.6073L10.7517 19.6073L10.7517 19.6073L10.7517 19.6073L10.7517 19.6073L10.7518 19.6073L10.7518 19.6073L10.7518 19.6073L10.7518 19.6073L10.7518 19.6073L10.7519 19.6073L10.7519 19.6073L10.7519 19.6073L10.7519 19.6073L10.7519 19.6073L10.752 19.6073L10.752 19.6073L10.752 19.6073L10.752 19.6073L10.752 19.6073L10.7521 19.6073L10.7521 19.6073L10.7521 19.6073L10.7521 19.6073L10.7521 19.6073L10.7522 19.6073L10.7522 19.6073L10.7522 19.6073L10.7522 19.6073L10.7522 19.6073L10.7523 19.6073L10.7523 19.6073L10.7523 19.6073L10.7523 19.6073L10.7523 19.6073L10.7524 19.6073L10.7524 19.6073L10.7524 19.6073L10.7524 19.6073L10.7524 19.6073L10.7525 19.6073L10.7525 19.6073L10.7525 19.6073L10.7525 19.6074L10.7525 19.6074L10.7525 19.6074L10.7526 19.6074L10.7526 19.6074L10.7526 19.6074L10.7526 19.6074L10.7526 19.6074L10.7527 19.6074L10.7527 19.6074L10.7527 19.6074L10.7527 19.6074L10.7527 19.6074L10.7528 19.6074L10.7528 19.6074L10.7528 19.6074L10.7528 19.6074L10.7528 19.6074L10.7529 19.6074L10.7529 19.6074L10.7529 19.6074L10.7529 19.6074L10.7529 19.6074L10.753 19.6074L10.753 19.6074L10.753 19.6074L10.753 19.6074L10.753 19.6074L10.7531 19.6074L10.7531 19.6074L10.7531 19.6074L10.7531 19.6074L10.7531 19.6074L10.7532 19.6074L10.7532 19.6074L10.7532 19.6074L10.7532 19.6074L10.7532 19.6074L10.7533 19.6074L10.7533 19.6074L10.7533 19.6074L10.7533 19.6074L10.7533 19.6074L10.7534 19.6074L10.7534 19.6074L10.7534 19.6074L10.7534 19.6074L10.7534 19.6074L10.7535 19.6074L10.7535 19.6074L10.7535 19.6074L10.7535 19.6075L10.7535 19.6075L10.7536 19.6075L10.7536 19.6075L10.7536 19.6075L10.7536 19.6075L10.7536 19.6075L10.7537 19.6075L10.7537 19.6075L10.7537 19.6075L10.7537 19.6075L10.7537 19.6075L10.7537 19.6075L10.7538 19.6075L10.7538 19.6075L10.7538 19.6075L10.7538 19.6075L10.7538 19.6075L10.7539 19.6075L10.7539 19.6075L10.7539 19.6075L10.7539 19.6075L10.7539 19.6075L10.754 19.6075L10.754 19.6075L10.754 19.6075L10.754 19.6075L10.754 19.6075L10.7541 19.6075L10.7541 19.6075L10.7541 19.6075L10.7541 19.6075L10.7541 19.6075L10.7542 19.6075L10.7542 19.6075L10.7542 19.6075L10.7542 19.6075L10.7542 19.6075L10.7543 19.6075L10.7543 19.6075L10.7543 19.6075L10.7543 19.6075L10.7543 19.6075L10.7544 19.6075L10.7544 19.6075L10.7544 19.6075L10.7544 19.6075L10.7544 19.6075L10.7545 19.6075L10.7545 19.6075L10.7545 19.6076L10.7545 19.6076L10.7545 19.6076L10.7546 19.6076L10.7546 19.6076L10.7546 19.6076L10.7546 19.6076L10.7546 19.6076L10.7547 19.6076L10.7547 19.6076L10.7547 19.6076L10.7547 19.6076L10.7547 19.6076L10.7548 19.6076L10.7548 19.6076L10.7548 19.6076L10.7548 19.6076L10.7548 19.6076L10.7549 19.6076L10.7549 19.6076L10.7549 19.6076L10.7549 19.6076L10.7549 19.6076L10.7549 19.6076L10.755 19.6076L10.755 19.6076L10.755 19.6076L10.755 19.6076L10.755 19.6076L10.7551 19.6076L10.7551 19.6076L10.7551 19.6076L10.7551 19.6076L10.7551 19.6076L10.7552 19.6076L10.7552 19.6076L10.7552 19.6076L10.7552 19.6076L10.7552 19.6076L10.7553 19.6076L10.7553 19.6076L10.7553 19.6076L10.7553 19.6076L10.7553 19.6076L10.7554 19.6076L10.7554 19.6076L10.7554 19.6076L10.7554 19.6076L10.7554 19.6076L10.7555 19.6076L10.7555 19.6076L10.7555 19.6077L10.7555 19.6077L10.7555 19.6077L10.7556 19.6077L10.7556 19.6077L10.7556 19.6077L10.7556 19.6077L10.7556 19.6077L10.7557 19.6077L10.7557 19.6077L10.7557 19.6077L10.7557 19.6077L10.7557 19.6077L10.7558 19.6077L10.7558 19.6077L10.7558 19.6077L10.7558 19.6077L10.7558 19.6077L10.7559 19.6077L10.7559 19.6077L10.7559 19.6077L10.7559 19.6077L10.7559 19.6077L10.756 19.6077L10.756 19.6077L10.756 19.6077L10.756 19.6077L10.756 19.6077L10.7561 19.6077L10.7561 19.6077L10.7561 19.6077L10.7561 19.6077L10.7561 19.6077L10.7561 19.6077L10.7562 19.6077L10.7562 19.6077L10.7562 19.6077L10.7562 19.6077L10.7562 19.6077L10.7563 19.6077L10.7563 19.6077L10.7563 19.6077L10.7563 19.6077L10.7563 19.6077L10.7564 19.6077L10.7564 19.6077L10.7564 19.6077L10.7564 19.6077L10.7564 19.6077L10.7565 19.6077L10.7565 19.6077L10.7565 19.6078L10.7565 19.6078L10.7565 19.6078L10.7566 19.6078L10.7566 19.6078L10.7566 19.6078L10.7566 19.6078L10.7566 19.6078L10.7567 19.6078L10.7567 19.6078L10.7567 19.6078L10.7567 19.6078L10.7567 19.6078L10.7568 19.6078L10.7568 19.6078L10.7568 19.6078L10.7568 19.6078L10.7568 19.6078L10.7569 19.6078L10.7569 19.6078L10.7569 19.6078L10.7569 19.6078L10.7569 19.6078L10.757 19.6078L10.757 19.6078L10.757 19.6078L10.757 19.6078L10.757 19.6078L10.7571 19.6078L10.7571 19.6078L10.7571 19.6078L10.7571 19.6078L10.7571 19.6078L10.7572 19.6078L10.7572 19.6078L10.7572 19.6078L10.7572 19.6078L10.7572 19.6078L10.7572 19.6078L10.7573 19.6078L10.7573 19.6078L10.7573 19.6078L10.7573 19.6078L10.7573 19.6078L10.7574 19.6078L10.7574 19.6078L10.7574 19.6078L10.7574 19.6078L10.7574 19.6078L10.7575 19.6078L10.7575 19.6078L10.7575 19.6079L10.7575 19.6079L10.7575 19.6079L10.7576 19.6079L10.7576 19.6079L10.7576 19.6079L10.7576 19.6079L10.7576 19.6079L10.7577 19.6079L10.7577 19.6079L10.7577 19.6079L10.7577 19.6079L10.7577 19.6079L10.7578 19.6079L10.7578 19.6079L10.7578 19.6079L10.7578 19.6079L10.7578 19.6079L10.7579 19.6079L10.7579 19.6079L10.7579 19.6079L10.7579 19.6079L10.7579 19.6079L10.758 19.6079L10.758 19.6079L10.758 19.6079L10.758 19.6079L10.758 19.6079L10.7581 19.6079L10.7581 19.6079L10.7581 19.6079L10.7581 19.6079L10.7581 19.6079L10.7581 19.6079L10.7582 19.6079L10.7582 19.6079L10.7582 19.6079L10.7582 19.6079L10.7582 19.6079L10.7583 19.6079L10.7583 19.6079L10.7583 19.6079L10.7583 19.6079L10.7583 19.6079L10.7584 19.6079L10.7584 19.6079L10.7584 19.6079L10.7584 19.6079L10.7584 19.6079L10.7584 19.6079L10.7585 19.6079L10.7536 19.6567L10.7535 19.6577M10.7535 19.6577C11.4944 19.7257 12.0966 20.2816 12.2178 21.0094C12.2248 21.0518 12.2305 21.0943 12.2347 21.1363M12.2347 21.1363H23.7358M12.2347 21.1363L12.2845 21.1313C12.283 21.1163 12.2813 21.1013 12.2795 21.0863M12.2347 21.1363V21.0863H12.2795M23.7358 21.1363C23.7401 21.0941 23.7458 21.0517 23.7529 21.0092C23.8741 20.2812 24.4765 19.7255 25.2177 19.6575C25.2209 19.6574 25.2239 19.657 25.2271 19.6567M23.7358 21.1363V21.0863H23.6912C23.6893 21.1012 23.6876 21.1162 23.686 21.1311L23.7358 21.1363ZM25.2271 19.6567V16.3256M25.2271 19.6567L25.2218 19.607L25.2151 19.6076L25.2151 19.6076L25.2131 19.6077C25.2011 19.6088 25.1891 19.6101 25.1771 19.6114V19.6567H25.2176H25.2176H25.2271ZM25.2271 16.3256H25.2178H25.2178H25.1771V16.3709C25.1891 16.3722 25.2012 16.3734 25.2133 16.3746L25.2133 16.3746L25.2151 16.3747L25.2216 16.3753L25.2271 16.3256ZM12.2795 21.0863C12.276 21.058 12.2718 21.0296 12.2671 21.0012L12.2671 21.0012C12.1441 20.2623 11.5404 19.6957 10.7934 19.6115V19.2562C10.7934 19.0498 10.626 18.8824 10.4196 18.8824C10.2131 18.8824 10.0459 19.0499 10.0459 19.2562V19.7681C10.0459 20.0835 10.2929 20.3382 10.6077 20.3473"
      fill="#0A0D12"
      stroke="#0A0D12"
      stroke-width="0.1"
    />
    <path
      d="M12.7802 18.3648C12.9865 18.3648 13.1539 18.1974 13.1539 17.991C13.1539 17.7845 12.9865 17.6172 12.7802 17.6172H12.1783C11.9717 17.6172 11.8045 17.7847 11.8045 17.991C11.8045 18.1974 11.9717 18.3648 12.1783 18.3648H12.7802Z"
      fill="#0A0D12"
      stroke="#0A0D12"
      stroke-width="0.1"
    />
    <path
      d="M23.7881 18.3648C23.9945 18.3648 24.1619 18.1974 24.1619 17.991C24.1619 17.7845 23.9945 17.6172 23.7881 17.6172H23.1862C22.9797 17.6172 22.8123 17.7846 22.8123 17.991C22.8123 18.1974 22.9797 18.3648 23.1862 18.3648H23.7881Z"
      fill="#0A0D12"
      stroke="#0A0D12"
      stroke-width="0.1"
    />
    <path
      d="M20.5962 17.9911C20.5962 16.5597 19.4317 15.3951 18.0002 15.3951C16.5687 15.3951 15.4041 16.5597 15.4041 17.9911C15.4041 19.4226 16.5687 20.5872 18.0002 20.5872C19.4317 20.5872 20.5962 19.4226 20.5962 17.9911ZM16.1517 17.9911C16.1517 16.9721 16.9809 16.1428 18.0002 16.1428C19.0194 16.1428 19.8487 16.9721 19.8487 17.9911C19.8487 19.0104 19.0194 19.8396 18.0002 19.8396C16.9809 19.8396 16.1517 19.0104 16.1517 17.9911Z"
      fill="#0A0D12"
      stroke="#0A0D12"
      stroke-width="0.1"
    />
    <path
      d="M22.9505 9.61639L22.9505 9.61636C22.8309 9.44818 22.8703 9.2147 23.0385 9.09513L24.3599 8.15488L22.9505 9.61639ZM22.9505 9.61639C23.0703 9.78457 23.3035 9.82388 23.4719 9.70436L23.4719 9.70432M22.9505 9.61639L23.4719 9.70432M23.4719 9.70432L24.3572 9.07422V9.83966C24.3572 10.0997 24.5687 10.3113 24.8288 10.3113H25.8749C26.839 10.3113 27.6937 10.8727 28.0535 11.7402L28.0535 11.7402M23.4719 9.70432L28.0535 11.7402M28.6979 11.473L28.7441 11.4538C28.8897 11.8051 28.9911 12.1897 29.0455 12.5971M28.6979 11.473L28.996 12.6038M28.6979 11.473L28.7441 11.4538C28.2677 10.3051 27.141 9.56357 25.8749 9.56357H25.1049V8.53902C25.1049 8.36186 25.007 8.20121 24.8496 8.11995C24.6921 8.0387 24.5043 8.05202 24.3599 8.15486L28.6979 11.473ZM29.0455 12.5971L28.996 12.6038M29.0455 12.5971C29.0455 12.5971 29.0455 12.5971 29.0455 12.5971L28.996 12.6038M29.0455 12.5971C29.0765 12.8282 28.9453 13.0468 28.7273 13.1283L28.7273 13.1283C28.5083 13.21 28.2652 13.1304 28.1368 12.9348M28.996 12.6038C29.0238 12.8117 28.906 13.0081 28.7098 13.0815C28.5127 13.155 28.2942 13.0835 28.1785 12.9074L28.1368 12.9349C28.1368 12.9349 28.1368 12.9349 28.1368 12.9348M28.1368 12.9348C27.9092 12.589 27.5961 12.4149 27.2291 12.3265C26.8597 12.2375 26.439 12.2361 25.9984 12.2348C25.8867 12.2345 25.7756 12.2341 25.666 12.2325L25.6659 12.2325C25.5337 12.2302 25.3991 12.2383 25.254 12.2471L25.2482 12.2475L28.1368 12.9348ZM10.8951 26.4187H10.1251C8.859 26.4187 7.73233 25.6772 7.25593 24.5286L7.30211 24.5095L7.25593 24.5286C7.11029 24.1773 7.00886 23.7928 6.95446 23.3853L6.95445 23.3853C6.92369 23.1543 7.0546 22.9357 7.27268 22.8542C7.49168 22.7723 7.73478 22.8521 7.86337 23.0476L7.8216 23.075L7.86337 23.0476C8.0908 23.3934 8.40391 23.5675 8.77092 23.6559C9.14033 23.7449 9.56108 23.7464 10.0017 23.7476C10.1133 23.7478 10.2246 23.7481 10.3342 23.75C10.3342 23.75 10.3343 23.75 10.3343 23.75L10.8951 26.4187ZM10.8951 26.4187V27.4433C10.8951 27.6204 10.9929 27.7812 11.1504 27.8623C11.2188 27.8976 11.293 27.915 11.3669 27.915C11.4629 27.915 11.5585 27.8855 11.64 27.8276L11.6401 27.8276L15.1053 25.3619L15.1053 25.3619C15.2294 25.2734 15.3033 25.1299 15.3033 24.9776C15.3033 24.8253 15.2294 24.6817 15.1052 24.5934L13.507 23.4562H26.8463C27.3268 23.4562 27.7178 23.0652 27.7178 22.5848V13.3975C27.7178 12.9169 27.3268 12.5261 26.8463 12.5261H25.1049V12.2559C25.1533 12.2533 25.2011 12.2504 25.2481 12.2475L10.8951 26.4187ZM28.0535 11.7402C28.0727 11.7865 28.091 11.8336 28.1084 11.8815M28.0535 11.7402L28.1084 11.8815M28.1084 11.8815C27.4755 11.4913 26.6993 11.4892 26.0073 11.4873L26.0004 11.4873C25.8917 11.4869 25.7841 11.4866 25.6783 11.4849L25.6775 11.5349L25.6782 11.4849C25.5151 11.4824 25.3551 11.4919 25.2024 11.5013L25.2055 11.5512L25.2024 11.5013C25.0712 11.5094 24.9492 11.5168 24.829 11.5168C24.5688 11.5168 24.3572 11.7283 24.3572 11.9884V12.9352L21.6442 11.0047L22.2614 10.5656C22.4296 10.446 22.4689 10.2125 22.3494 10.0443L22.3493 10.0443C22.2296 9.87612 21.9963 9.8368 21.828 9.95652L21.828 9.95653L20.8947 10.6205C20.7706 10.7088 20.6965 10.8524 20.6965 11.0047C20.6965 11.1572 20.7706 11.3007 20.8947 11.389C20.8947 11.389 20.8947 11.389 20.8947 11.389L22.4928 12.5261H9.15363C8.67299 12.5261 8.28219 12.917 8.28219 13.3975V22.5848C8.28219 23.0654 8.673 23.4562 9.15363 23.4562H10.8951V23.7266C10.8468 23.7291 10.799 23.7321 10.752 23.735L10.7551 23.7849L28.1084 11.8815ZM7.89164 24.101C8.52433 24.491 9.30016 24.4931 9.99191 24.495L9.99961 24.495C10.1082 24.4953 10.2158 24.4957 10.3216 24.4975L10.3217 24.4975C10.4853 24.5001 10.6451 24.4904 10.7976 24.481M7.89164 24.101L10.7976 24.481M7.89164 24.101C7.90899 24.1488 7.92727 24.1959 7.9465 24.2422L7.90032 24.2614L7.9465 24.2422C8.30626 25.1097 9.16093 25.6711 10.1251 25.6711H11.1711C11.4312 25.6711 11.6428 25.8827 11.6428 26.1428V26.9081L14.3557 24.9776L11.6428 23.0471V23.994C11.6428 24.2541 11.4312 24.4656 11.1711 24.4656C11.0523 24.4656 10.9316 24.4729 10.802 24.4807L10.7976 24.481M7.89164 24.101L10.7976 24.481M10.7976 24.481L10.7975 24.481L10.7945 24.4311L10.7976 24.481ZM25.1049 9.61357H25.0549V9.56357L25.1049 9.61357ZM24.3599 13.8547L24.3599 13.8547C24.4415 13.9126 24.5371 13.9422 24.6331 13.9422C24.707 13.9422 24.7812 13.9247 24.8495 13.8894L24.8496 13.8894C25.007 13.8082 25.1049 13.6475 25.1049 13.4704V13.2738H26.8463H26.8528C26.9179 13.2772 26.9701 13.3314 26.9701 13.3975V22.5848C26.9701 22.653 26.9146 22.7085 26.8463 22.7085H12.4562L11.6401 22.1278C11.4956 22.0249 11.3079 22.0116 11.1504 22.0928C10.993 22.1741 10.8951 22.3348 10.8951 22.5119V22.7085H9.15363C9.08534 22.7085 9.02974 22.6529 9.02974 22.5848V13.3975C9.02974 13.3293 9.0854 13.2736 9.15363 13.2736H23.5436L24.3599 13.8547Z"
      fill="#0A0D12"
      stroke="#0A0D12"
      stroke-width="0.1"
    />
  </svg>
);

export const costReportIcon = () => (
  <svg width="37" height="36" viewBox="0 0 37 36" fill="none" xmlns="http://www.w3.org/2000/svg">
    <rect x="0.0410156" width="36" height="36" rx="8" fill="#EAECF5" />
    <path
      d="M26.8466 23.0825H9.15392C8.87898 23.0825 8.65625 22.8596 8.65625 22.5848V13.3976C8.65625 13.1226 8.87915 12.8999 9.15392 12.8999H26.8466C27.1214 12.8999 27.3441 13.1228 27.3441 13.3976V22.5848C27.3441 22.8596 27.1214 23.0825 26.8466 23.0825Z"
      fill="#EAECF5"
    />
    <path
      d="M27.3439 13.3972V22.5845C27.3439 22.8596 27.1211 23.0823 26.846 23.0823H25.8574C26.132 23.0823 26.3548 22.8596 26.3548 22.5845V13.3972C26.3548 13.1226 26.132 12.8999 25.8574 12.8999H26.846C27.1211 12.8999 27.3439 13.1226 27.3439 13.3972Z"
      fill="#EAECF5"
    />
    <path
      d="M25.5515 16.2141V19.7681C25.5515 19.8794 25.4632 19.9704 25.3521 19.9737C25.3177 19.9745 25.2827 19.9769 25.2477 19.9801C24.6585 20.034 24.1699 20.4788 24.0727 21.0624C24.0612 21.1319 24.0541 21.1997 24.0518 21.266C24.0477 21.3751 23.9554 21.46 23.8463 21.46H12.1251C12.016 21.46 11.9237 21.3751 11.9196 21.266C11.9173 21.1997 11.9102 21.1319 11.8987 21.0625C11.8016 20.4791 11.3134 20.0343 10.7244 19.9801C10.689 19.9769 10.6541 19.9745 10.6193 19.9737C10.5082 19.9704 10.4199 19.8794 10.4199 19.7681V16.2141C10.4199 16.1028 10.5082 16.0119 10.6193 16.0085C10.6541 16.0075 10.689 16.0053 10.7244 16.0021C11.3134 15.9479 11.8016 15.5031 11.8987 14.9197C11.9102 14.8504 11.9173 14.7825 11.9196 14.7162C11.9237 14.6071 12.016 14.5222 12.1251 14.5222H23.8463C23.9554 14.5222 24.0477 14.6071 24.0518 14.7162C24.0541 14.7825 24.0612 14.8504 24.0727 14.9198C24.1699 15.5034 24.6585 15.9482 25.2477 16.0021C25.2827 16.0053 25.3175 16.0075 25.3521 16.0085C25.4632 16.0119 25.5515 16.1028 25.5515 16.2141Z"
      fill="#EAECF5"
    />
    <path
      d="M25.5506 16.214V19.7682C25.5506 19.8791 25.4625 19.9703 25.3512 19.9733C25.317 19.9746 25.282 19.9768 25.2471 19.9798C24.6577 20.0337 24.1694 20.4785 24.0719 21.0623C24.0606 21.1318 24.0532 21.1996 24.0511 21.2661C24.0472 21.3748 23.9549 21.4599 23.8456 21.4599H22.8711C22.9804 21.4599 23.0727 21.375 23.0767 21.2661C23.0792 21.1996 23.0861 21.1318 23.0979 21.0623C23.1467 20.7709 23.2969 20.5029 23.5178 20.307C23.6979 20.1474 23.9084 20.02 24.0878 19.861C24.3193 19.6561 24.4376 19.4639 24.5272 19.1715C24.5999 18.9342 24.5712 18.6664 24.5991 18.4212C24.6303 18.1486 24.6023 17.8732 24.6001 17.5999C24.5957 17.0695 24.5504 16.5232 24.1727 16.1143C24.0312 15.9613 23.8866 15.9238 23.7095 15.8162C23.3876 15.621 23.1605 15.2912 23.0977 14.9196C23.0861 14.8506 23.0792 14.7823 23.0765 14.7163C23.0727 14.6071 22.9804 14.522 22.8711 14.522H23.8456C23.9549 14.522 24.0472 14.6071 24.0511 14.7163C24.0532 14.7823 24.0606 14.8506 24.0719 14.9198C24.1694 15.5034 24.6577 15.9482 25.247 16.002C25.282 16.0056 25.317 16.0078 25.351 16.0086C25.4625 16.0116 25.5506 16.1026 25.5506 16.214Z"
      fill="#EAECF5"
    />
    <path
      d="M20.2219 17.9911C20.2219 19.2184 19.2269 20.2134 17.9996 20.2134C16.7723 20.2134 15.7773 19.2184 15.7773 17.9911C15.7773 16.7638 16.7723 15.7688 17.9996 15.7688C19.2269 15.7688 20.2219 16.7638 20.2219 17.9911Z"
      fill="#EAECF5"
    />
    <path
      d="M20.2224 17.991C20.2224 19.2183 19.2272 20.2133 18.0001 20.2133C17.4893 20.2133 17.0187 20.0411 16.6436 19.7514C16.8178 19.7957 17.0006 19.8188 17.1886 19.8188C18.4158 19.8188 19.4108 18.8244 19.4108 17.5966C19.4108 16.8799 19.0718 16.2425 18.5449 15.8362C19.5089 16.0791 20.2224 16.9517 20.2224 17.991Z"
      fill="#EAECF5"
    />
    <path
      d="M21.1114 11.0845L24.5767 13.5501C24.6413 13.5961 24.7311 13.5498 24.7311 13.4704V11.9883C24.7311 11.9343 24.7749 11.8906 24.829 11.8906C25.1096 11.8906 25.3918 11.854 25.6721 11.8587C26.6733 11.8755 27.8128 11.7622 28.4489 12.7295C28.5213 12.8396 28.6925 12.7772 28.675 12.6466C28.6351 12.3477 28.5555 11.9749 28.3987 11.5971C27.9785 10.5836 26.9719 9.93739 25.8748 9.93739H24.8288C24.7748 9.93739 24.7311 9.89375 24.7311 9.8397V8.53906C24.7311 8.45966 24.6413 8.41334 24.5767 8.4595L21.1114 10.9252C21.0566 10.9641 21.0566 11.0453 21.1114 11.0845Z"
      fill="#4E5BA6"
    />
    <path
      d="M28.5221 12.7828C28.4875 12.6705 28.4469 12.556 28.3994 12.4407C28.15 11.8351 27.6719 11.3389 27.0833 11.0544C26.3824 10.7156 25.7868 10.8691 25.05 10.854C24.8761 10.8505 24.3867 10.8955 24.3187 10.69C24.2992 10.6315 24.3069 10.5678 24.3061 10.5062C24.2972 9.88287 23.7395 9.93792 23.321 10.1904C23.0508 10.3535 22.8106 10.5574 22.5541 10.7402C22.233 10.969 21.9118 11.1973 21.5907 11.4257L21.1115 11.0846C21.0566 11.0454 21.0566 10.9641 21.1115 10.9254L24.5767 8.45953C24.6411 8.41337 24.7309 8.45953 24.7309 8.53892V9.8399C24.7309 9.89378 24.7749 9.93742 24.8289 9.93742H25.8746C26.9717 9.93742 27.9786 10.5838 28.3987 11.5971C28.5553 11.9749 28.6349 12.3479 28.675 12.6466C28.6874 12.7421 28.5988 12.8012 28.5221 12.7828Z"
      fill="#4E5BA6"
    />
    <path
      d="M14.888 24.8981L11.4228 22.4324C11.3582 22.3862 11.2684 22.4326 11.2684 22.5121V23.994C11.2684 24.0481 11.2246 24.0919 11.1705 24.0919C10.8897 24.0919 10.6077 24.1285 10.3274 24.1238C9.32622 24.107 8.18671 24.2203 7.55057 23.253C7.47823 23.1429 7.30703 23.2052 7.32448 23.3359C7.36443 23.6347 7.44399 24.0075 7.60076 24.3855C8.02105 25.3987 9.02762 26.045 10.1247 26.045H11.1707C11.2247 26.045 11.2684 26.0888 11.2684 26.1428V27.4433C11.2684 27.5228 11.3582 27.569 11.4228 27.523L14.8882 25.0573C14.9429 25.0184 14.9429 24.937 14.888 24.8981Z"
      fill="#4E5BA6"
    />
    <path
      d="M7.47816 23.1997C7.51274 23.3118 7.55336 23.4263 7.60086 23.5416C7.85028 24.1472 8.32831 24.6435 8.91695 24.928C9.61771 25.2667 10.2134 25.1133 10.9502 25.1283C11.1241 25.132 11.6136 25.0868 11.6815 25.2924C11.701 25.351 11.6933 25.4145 11.6941 25.4762C11.703 26.0996 12.2608 26.0445 12.6792 25.7921C12.9495 25.629 13.1897 25.4249 13.4461 25.2422C13.7672 25.0135 14.0883 24.785 14.4096 24.5568L14.8888 24.8978C14.9436 24.9371 14.9436 25.0182 14.8888 25.0571L11.4236 27.5228C11.3591 27.5691 11.2693 27.5228 11.2693 27.4434V26.1426C11.2693 26.0885 11.2253 26.0449 11.1713 26.0449H10.1258C9.02856 26.0449 8.02182 25.3987 7.6017 24.3852C7.44493 24.0076 7.36554 23.6344 7.32542 23.3357C7.31284 23.2402 7.40129 23.1811 7.47816 23.1997Z"
      fill="#4E5BA6"
    />
    <path
      d="M24.441 14.8584L24.441 14.8585C24.5101 15.2733 24.8552 15.591 25.2813 15.6299L25.2813 15.6299C25.3097 15.6326 25.337 15.6343 25.3624 15.635L25.3627 15.635C25.6777 15.6443 25.9248 15.8988 25.9248 16.2142V19.7681C25.9248 20.0835 25.6777 20.3382 25.3626 20.3473M24.441 14.8584L25.3612 20.2974M24.441 14.8584C24.4321 14.8052 24.4267 14.7528 24.4249 14.7028L24.4249 14.7027C24.4135 14.3916 24.1588 14.1485 23.8459 14.1485H12.1246C11.8117 14.1485 11.557 14.3916 11.5456 14.7027L11.5456 14.7028C11.5438 14.7526 11.5384 14.805 11.5295 14.8583L11.5295 14.8583C11.4604 15.2732 11.1155 15.5909 10.6897 15.6299L10.6897 15.6299C10.6613 15.6326 10.6339 15.6343 10.6082 15.635L10.608 15.635C10.2929 15.6441 10.0457 15.8988 10.0457 16.2142V17.7538C10.0457 17.9604 10.2131 18.1276 10.4195 18.1276C10.626 18.1276 10.7934 17.9602 10.7934 17.7538V16.3709C11.5404 16.2866 12.1439 15.72 12.2669 14.9813L12.2669 14.9813C12.2717 14.9529 12.2758 14.9244 12.2793 14.896H23.691L24.441 14.8584ZM25.3626 20.3473L25.3612 20.2974M25.3626 20.3473C25.3627 20.3473 25.3627 20.3473 25.3628 20.3473L25.3612 20.2974M25.3626 20.3473C25.3368 20.3482 25.3095 20.3498 25.2813 20.3524L25.2813 20.3524C24.8554 20.3913 24.5102 20.709 24.441 21.1238L24.441 21.1239C24.4321 21.177 24.4268 21.2294 24.4249 21.2796M25.3612 20.2974C25.3343 20.2982 25.306 20.2999 25.2768 20.3026C24.8286 20.3435 24.4647 20.6779 24.3917 21.1156C24.3825 21.1708 24.3769 21.2254 24.3749 21.2778M24.4249 21.2796L24.3749 21.2778M24.4249 21.2796C24.4137 21.5907 24.1587 21.8338 23.8459 21.8338H12.1248C11.8118 21.8338 11.557 21.5907 11.5456 21.2796L11.5456 21.2795C11.5438 21.2296 11.5385 21.1772 11.5296 21.124L11.5296 21.124C11.4604 20.7093 11.1155 20.3916 10.6897 20.3524L10.6896 20.3523C10.6622 20.3498 10.6346 20.3482 10.6077 20.3473M24.4249 21.2796C24.4249 21.2796 24.4249 21.2796 24.4249 21.2797L24.3749 21.2778M24.3749 21.2778L12.2795 21.0863M11.579 21.1158C11.506 20.6782 11.1422 20.3439 10.6942 20.3026C10.6657 20.2999 10.637 20.2982 10.6091 20.2974M11.579 21.1158L23.7358 14.846C23.74 14.8882 23.7457 14.9306 23.7529 14.9733C23.8741 15.701 24.4765 16.2568 25.2178 16.3248C25.2209 16.3249 25.2241 16.3253 25.2271 16.3256M11.579 21.1158C11.5882 21.171 11.5937 21.2256 11.5956 21.2778L11.579 21.1158ZM10.6091 20.2974C10.3215 20.289 10.0959 20.0565 10.0959 19.7681V19.2562C10.0959 19.0775 10.2407 18.9324 10.4196 18.9324C10.5984 18.9324 10.7434 19.0775 10.7434 19.2562V19.6567M10.6091 20.2974L10.6076 20.3473C10.6077 20.3473 10.6077 20.3473 10.6077 20.3473M10.6091 20.2974L10.6077 20.3473M10.7434 19.6567L10.7535 19.6577M10.7434 19.6567H10.7536L10.7535 19.6577M10.7434 19.6567L10.7484 19.6069L10.7484 19.6069L10.7484 19.6069L10.7485 19.6069L10.7485 19.6069L10.7485 19.6069L10.7485 19.607L10.7485 19.607L10.7486 19.607L10.7486 19.607L10.7486 19.607L10.7486 19.607L10.7486 19.607L10.7487 19.607L10.7487 19.607L10.7487 19.607L10.7487 19.607L10.7487 19.607L10.7488 19.607L10.7488 19.607L10.7488 19.607L10.7488 19.607L10.7488 19.607L10.7489 19.607L10.7489 19.607L10.7489 19.607L10.7489 19.607L10.7489 19.607L10.7489 19.607L10.749 19.607L10.749 19.607L10.749 19.607L10.749 19.607L10.749 19.607L10.7491 19.607L10.7491 19.607L10.7491 19.607L10.7491 19.607L10.7491 19.607L10.7492 19.607L10.7492 19.607L10.7492 19.607L10.7492 19.607L10.7492 19.607L10.7493 19.607L10.7493 19.607L10.7493 19.607L10.7493 19.607L10.7493 19.607L10.7494 19.607L10.7494 19.607L10.7494 19.607L10.7494 19.607L10.7494 19.607L10.7495 19.607L10.7495 19.607L10.7495 19.6071L10.7495 19.6071L10.7495 19.6071L10.7496 19.6071L10.7496 19.6071L10.7496 19.6071L10.7496 19.6071L10.7496 19.6071L10.7497 19.6071L10.7497 19.6071L10.7497 19.6071L10.7497 19.6071L10.7497 19.6071L10.7498 19.6071L10.7498 19.6071L10.7498 19.6071L10.7498 19.6071L10.7498 19.6071L10.7499 19.6071L10.7499 19.6071L10.7499 19.6071L10.7499 19.6071L10.7499 19.6071L10.75 19.6071L10.75 19.6071L10.75 19.6071L10.75 19.6071L10.75 19.6071L10.7501 19.6071L10.7501 19.6071L10.7501 19.6071L10.7501 19.6071L10.7501 19.6071L10.7501 19.6071L10.7502 19.6071L10.7502 19.6071L10.7502 19.6071L10.7502 19.6071L10.7502 19.6071L10.7503 19.6071L10.7503 19.6071L10.7503 19.6071L10.7503 19.6071L10.7503 19.6071L10.7504 19.6071L10.7504 19.6071L10.7504 19.6071L10.7504 19.6071L10.7504 19.6071L10.7505 19.6071L10.7505 19.6071L10.7505 19.6072L10.7505 19.6072L10.7505 19.6072L10.7506 19.6072L10.7506 19.6072L10.7506 19.6072L10.7506 19.6072L10.7506 19.6072L10.7507 19.6072L10.7507 19.6072L10.7507 19.6072L10.7507 19.6072L10.7507 19.6072L10.7508 19.6072L10.7508 19.6072L10.7508 19.6072L10.7508 19.6072L10.7508 19.6072L10.7509 19.6072L10.7509 19.6072L10.7509 19.6072L10.7509 19.6072L10.7509 19.6072L10.751 19.6072L10.751 19.6072L10.751 19.6072L10.751 19.6072L10.751 19.6072L10.7511 19.6072L10.7511 19.6072L10.7511 19.6072L10.7511 19.6072L10.7511 19.6072L10.7512 19.6072L10.7512 19.6072L10.7512 19.6072L10.7512 19.6072L10.7512 19.6072L10.7513 19.6072L10.7513 19.6072L10.7513 19.6072L10.7513 19.6072L10.7513 19.6072L10.7513 19.6072L10.7514 19.6072L10.7514 19.6072L10.7514 19.6072L10.7514 19.6072L10.7514 19.6072L10.7515 19.6072L10.7515 19.6072L10.7515 19.6073L10.7515 19.6073L10.7515 19.6073L10.7516 19.6073L10.7516 19.6073L10.7516 19.6073L10.7516 19.6073L10.7516 19.6073L10.7517 19.6073L10.7517 19.6073L10.7517 19.6073L10.7517 19.6073L10.7517 19.6073L10.7518 19.6073L10.7518 19.6073L10.7518 19.6073L10.7518 19.6073L10.7518 19.6073L10.7519 19.6073L10.7519 19.6073L10.7519 19.6073L10.7519 19.6073L10.7519 19.6073L10.752 19.6073L10.752 19.6073L10.752 19.6073L10.752 19.6073L10.752 19.6073L10.7521 19.6073L10.7521 19.6073L10.7521 19.6073L10.7521 19.6073L10.7521 19.6073L10.7522 19.6073L10.7522 19.6073L10.7522 19.6073L10.7522 19.6073L10.7522 19.6073L10.7523 19.6073L10.7523 19.6073L10.7523 19.6073L10.7523 19.6073L10.7523 19.6073L10.7524 19.6073L10.7524 19.6073L10.7524 19.6073L10.7524 19.6073L10.7524 19.6073L10.7525 19.6073L10.7525 19.6073L10.7525 19.6073L10.7525 19.6074L10.7525 19.6074L10.7525 19.6074L10.7526 19.6074L10.7526 19.6074L10.7526 19.6074L10.7526 19.6074L10.7526 19.6074L10.7527 19.6074L10.7527 19.6074L10.7527 19.6074L10.7527 19.6074L10.7527 19.6074L10.7528 19.6074L10.7528 19.6074L10.7528 19.6074L10.7528 19.6074L10.7528 19.6074L10.7529 19.6074L10.7529 19.6074L10.7529 19.6074L10.7529 19.6074L10.7529 19.6074L10.753 19.6074L10.753 19.6074L10.753 19.6074L10.753 19.6074L10.753 19.6074L10.7531 19.6074L10.7531 19.6074L10.7531 19.6074L10.7531 19.6074L10.7531 19.6074L10.7532 19.6074L10.7532 19.6074L10.7532 19.6074L10.7532 19.6074L10.7532 19.6074L10.7533 19.6074L10.7533 19.6074L10.7533 19.6074L10.7533 19.6074L10.7533 19.6074L10.7534 19.6074L10.7534 19.6074L10.7534 19.6074L10.7534 19.6074L10.7534 19.6074L10.7535 19.6074L10.7535 19.6074L10.7535 19.6074L10.7535 19.6075L10.7535 19.6075L10.7536 19.6075L10.7536 19.6075L10.7536 19.6075L10.7536 19.6075L10.7536 19.6075L10.7537 19.6075L10.7537 19.6075L10.7537 19.6075L10.7537 19.6075L10.7537 19.6075L10.7537 19.6075L10.7538 19.6075L10.7538 19.6075L10.7538 19.6075L10.7538 19.6075L10.7538 19.6075L10.7539 19.6075L10.7539 19.6075L10.7539 19.6075L10.7539 19.6075L10.7539 19.6075L10.754 19.6075L10.754 19.6075L10.754 19.6075L10.754 19.6075L10.754 19.6075L10.7541 19.6075L10.7541 19.6075L10.7541 19.6075L10.7541 19.6075L10.7541 19.6075L10.7542 19.6075L10.7542 19.6075L10.7542 19.6075L10.7542 19.6075L10.7542 19.6075L10.7543 19.6075L10.7543 19.6075L10.7543 19.6075L10.7543 19.6075L10.7543 19.6075L10.7544 19.6075L10.7544 19.6075L10.7544 19.6075L10.7544 19.6075L10.7544 19.6075L10.7545 19.6075L10.7545 19.6075L10.7545 19.6076L10.7545 19.6076L10.7545 19.6076L10.7546 19.6076L10.7546 19.6076L10.7546 19.6076L10.7546 19.6076L10.7546 19.6076L10.7547 19.6076L10.7547 19.6076L10.7547 19.6076L10.7547 19.6076L10.7547 19.6076L10.7548 19.6076L10.7548 19.6076L10.7548 19.6076L10.7548 19.6076L10.7548 19.6076L10.7549 19.6076L10.7549 19.6076L10.7549 19.6076L10.7549 19.6076L10.7549 19.6076L10.7549 19.6076L10.755 19.6076L10.755 19.6076L10.755 19.6076L10.755 19.6076L10.755 19.6076L10.7551 19.6076L10.7551 19.6076L10.7551 19.6076L10.7551 19.6076L10.7551 19.6076L10.7552 19.6076L10.7552 19.6076L10.7552 19.6076L10.7552 19.6076L10.7552 19.6076L10.7553 19.6076L10.7553 19.6076L10.7553 19.6076L10.7553 19.6076L10.7553 19.6076L10.7554 19.6076L10.7554 19.6076L10.7554 19.6076L10.7554 19.6076L10.7554 19.6076L10.7555 19.6076L10.7555 19.6076L10.7555 19.6077L10.7555 19.6077L10.7555 19.6077L10.7556 19.6077L10.7556 19.6077L10.7556 19.6077L10.7556 19.6077L10.7556 19.6077L10.7557 19.6077L10.7557 19.6077L10.7557 19.6077L10.7557 19.6077L10.7557 19.6077L10.7558 19.6077L10.7558 19.6077L10.7558 19.6077L10.7558 19.6077L10.7558 19.6077L10.7559 19.6077L10.7559 19.6077L10.7559 19.6077L10.7559 19.6077L10.7559 19.6077L10.756 19.6077L10.756 19.6077L10.756 19.6077L10.756 19.6077L10.756 19.6077L10.7561 19.6077L10.7561 19.6077L10.7561 19.6077L10.7561 19.6077L10.7561 19.6077L10.7561 19.6077L10.7562 19.6077L10.7562 19.6077L10.7562 19.6077L10.7562 19.6077L10.7562 19.6077L10.7563 19.6077L10.7563 19.6077L10.7563 19.6077L10.7563 19.6077L10.7563 19.6077L10.7564 19.6077L10.7564 19.6077L10.7564 19.6077L10.7564 19.6077L10.7564 19.6077L10.7565 19.6077L10.7565 19.6077L10.7565 19.6078L10.7565 19.6078L10.7565 19.6078L10.7566 19.6078L10.7566 19.6078L10.7566 19.6078L10.7566 19.6078L10.7566 19.6078L10.7567 19.6078L10.7567 19.6078L10.7567 19.6078L10.7567 19.6078L10.7567 19.6078L10.7568 19.6078L10.7568 19.6078L10.7568 19.6078L10.7568 19.6078L10.7568 19.6078L10.7569 19.6078L10.7569 19.6078L10.7569 19.6078L10.7569 19.6078L10.7569 19.6078L10.757 19.6078L10.757 19.6078L10.757 19.6078L10.757 19.6078L10.757 19.6078L10.7571 19.6078L10.7571 19.6078L10.7571 19.6078L10.7571 19.6078L10.7571 19.6078L10.7572 19.6078L10.7572 19.6078L10.7572 19.6078L10.7572 19.6078L10.7572 19.6078L10.7572 19.6078L10.7573 19.6078L10.7573 19.6078L10.7573 19.6078L10.7573 19.6078L10.7573 19.6078L10.7574 19.6078L10.7574 19.6078L10.7574 19.6078L10.7574 19.6078L10.7574 19.6078L10.7575 19.6078L10.7575 19.6078L10.7575 19.6079L10.7575 19.6079L10.7575 19.6079L10.7576 19.6079L10.7576 19.6079L10.7576 19.6079L10.7576 19.6079L10.7576 19.6079L10.7577 19.6079L10.7577 19.6079L10.7577 19.6079L10.7577 19.6079L10.7577 19.6079L10.7578 19.6079L10.7578 19.6079L10.7578 19.6079L10.7578 19.6079L10.7578 19.6079L10.7579 19.6079L10.7579 19.6079L10.7579 19.6079L10.7579 19.6079L10.7579 19.6079L10.758 19.6079L10.758 19.6079L10.758 19.6079L10.758 19.6079L10.758 19.6079L10.7581 19.6079L10.7581 19.6079L10.7581 19.6079L10.7581 19.6079L10.7581 19.6079L10.7581 19.6079L10.7582 19.6079L10.7582 19.6079L10.7582 19.6079L10.7582 19.6079L10.7582 19.6079L10.7583 19.6079L10.7583 19.6079L10.7583 19.6079L10.7583 19.6079L10.7583 19.6079L10.7584 19.6079L10.7584 19.6079L10.7584 19.6079L10.7584 19.6079L10.7584 19.6079L10.7584 19.6079L10.7585 19.6079L10.7536 19.6567L10.7535 19.6577M10.7535 19.6577C11.4944 19.7257 12.0966 20.2816 12.2178 21.0094C12.2248 21.0518 12.2305 21.0943 12.2347 21.1363M12.2347 21.1363H23.7358M12.2347 21.1363L12.2845 21.1313C12.283 21.1163 12.2813 21.1013 12.2795 21.0863M12.2347 21.1363V21.0863H12.2795M23.7358 21.1363C23.7401 21.0941 23.7458 21.0517 23.7529 21.0092C23.8741 20.2812 24.4765 19.7255 25.2177 19.6575C25.2209 19.6574 25.2239 19.657 25.2271 19.6567M23.7358 21.1363V21.0863H23.6912C23.6893 21.1012 23.6876 21.1162 23.686 21.1311L23.7358 21.1363ZM25.2271 19.6567V16.3256M25.2271 19.6567L25.2218 19.607L25.2151 19.6076L25.2151 19.6076L25.2131 19.6077C25.2011 19.6088 25.1891 19.6101 25.1771 19.6114V19.6567H25.2176H25.2176H25.2271ZM25.2271 16.3256H25.2178H25.2178H25.1771V16.3709C25.1891 16.3722 25.2012 16.3734 25.2133 16.3746L25.2133 16.3746L25.2151 16.3747L25.2216 16.3753L25.2271 16.3256ZM12.2795 21.0863C12.276 21.058 12.2718 21.0296 12.2671 21.0012L12.2671 21.0012C12.1441 20.2623 11.5404 19.6957 10.7934 19.6115V19.2562C10.7934 19.0498 10.626 18.8824 10.4196 18.8824C10.2131 18.8824 10.0459 19.0499 10.0459 19.2562V19.7681C10.0459 20.0835 10.2929 20.3382 10.6077 20.3473"
      fill="#0A0D12"
      stroke="#0A0D12"
      stroke-width="0.1"
    />
    <path
      d="M12.7802 18.3648C12.9865 18.3648 13.1539 18.1974 13.1539 17.991C13.1539 17.7845 12.9865 17.6172 12.7802 17.6172H12.1783C11.9717 17.6172 11.8045 17.7847 11.8045 17.991C11.8045 18.1974 11.9717 18.3648 12.1783 18.3648H12.7802Z"
      fill="#0A0D12"
      stroke="#0A0D12"
      stroke-width="0.1"
    />
    <path
      d="M23.7881 18.3648C23.9945 18.3648 24.1619 18.1974 24.1619 17.991C24.1619 17.7845 23.9945 17.6172 23.7881 17.6172H23.1862C22.9797 17.6172 22.8123 17.7846 22.8123 17.991C22.8123 18.1974 22.9797 18.3648 23.1862 18.3648H23.7881Z"
      fill="#0A0D12"
      stroke="#0A0D12"
      stroke-width="0.1"
    />
    <path
      d="M20.5962 17.9911C20.5962 16.5597 19.4317 15.3951 18.0002 15.3951C16.5687 15.3951 15.4041 16.5597 15.4041 17.9911C15.4041 19.4226 16.5687 20.5872 18.0002 20.5872C19.4317 20.5872 20.5962 19.4226 20.5962 17.9911ZM16.1517 17.9911C16.1517 16.9721 16.9809 16.1428 18.0002 16.1428C19.0194 16.1428 19.8487 16.9721 19.8487 17.9911C19.8487 19.0104 19.0194 19.8396 18.0002 19.8396C16.9809 19.8396 16.1517 19.0104 16.1517 17.9911Z"
      fill="#0A0D12"
      stroke="#0A0D12"
      stroke-width="0.1"
    />
    <path
      d="M22.9505 9.61639L22.9505 9.61636C22.8309 9.44818 22.8703 9.2147 23.0385 9.09513L24.3599 8.15488L22.9505 9.61639ZM22.9505 9.61639C23.0703 9.78457 23.3035 9.82388 23.4719 9.70436L23.4719 9.70432M22.9505 9.61639L23.4719 9.70432M23.4719 9.70432L24.3572 9.07422V9.83966C24.3572 10.0997 24.5687 10.3113 24.8288 10.3113H25.8749C26.839 10.3113 27.6937 10.8727 28.0535 11.7402L28.0535 11.7402M23.4719 9.70432L28.0535 11.7402M28.6979 11.473L28.7441 11.4538C28.8897 11.8051 28.9911 12.1897 29.0455 12.5971M28.6979 11.473L28.996 12.6038M28.6979 11.473L28.7441 11.4538C28.2677 10.3051 27.141 9.56357 25.8749 9.56357H25.1049V8.53902C25.1049 8.36186 25.007 8.20121 24.8496 8.11995C24.6921 8.0387 24.5043 8.05202 24.3599 8.15486L28.6979 11.473ZM29.0455 12.5971L28.996 12.6038M29.0455 12.5971C29.0455 12.5971 29.0455 12.5971 29.0455 12.5971L28.996 12.6038M29.0455 12.5971C29.0765 12.8282 28.9453 13.0468 28.7273 13.1283L28.7273 13.1283C28.5083 13.21 28.2652 13.1304 28.1368 12.9348M28.996 12.6038C29.0238 12.8117 28.906 13.0081 28.7098 13.0815C28.5127 13.155 28.2942 13.0835 28.1785 12.9074L28.1368 12.9349C28.1368 12.9349 28.1368 12.9349 28.1368 12.9348M28.1368 12.9348C27.9092 12.589 27.5961 12.4149 27.2291 12.3265C26.8597 12.2375 26.439 12.2361 25.9984 12.2348C25.8867 12.2345 25.7756 12.2341 25.666 12.2325L25.6659 12.2325C25.5337 12.2302 25.3991 12.2383 25.254 12.2471L25.2482 12.2475L28.1368 12.9348ZM10.8951 26.4187H10.1251C8.859 26.4187 7.73233 25.6772 7.25593 24.5286L7.30211 24.5095L7.25593 24.5286C7.11029 24.1773 7.00886 23.7928 6.95446 23.3853L6.95445 23.3853C6.92369 23.1543 7.0546 22.9357 7.27268 22.8542C7.49168 22.7723 7.73478 22.8521 7.86337 23.0476L7.8216 23.075L7.86337 23.0476C8.0908 23.3934 8.40391 23.5675 8.77092 23.6559C9.14033 23.7449 9.56108 23.7464 10.0017 23.7476C10.1133 23.7478 10.2246 23.7481 10.3342 23.75C10.3342 23.75 10.3343 23.75 10.3343 23.75L10.8951 26.4187ZM10.8951 26.4187V27.4433C10.8951 27.6204 10.9929 27.7812 11.1504 27.8623C11.2188 27.8976 11.293 27.915 11.3669 27.915C11.4629 27.915 11.5585 27.8855 11.64 27.8276L11.6401 27.8276L15.1053 25.3619L15.1053 25.3619C15.2294 25.2734 15.3033 25.1299 15.3033 24.9776C15.3033 24.8253 15.2294 24.6817 15.1052 24.5934L13.507 23.4562H26.8463C27.3268 23.4562 27.7178 23.0652 27.7178 22.5848V13.3975C27.7178 12.9169 27.3268 12.5261 26.8463 12.5261H25.1049V12.2559C25.1533 12.2533 25.2011 12.2504 25.2481 12.2475L10.8951 26.4187ZM28.0535 11.7402C28.0727 11.7865 28.091 11.8336 28.1084 11.8815M28.0535 11.7402L28.1084 11.8815M28.1084 11.8815C27.4755 11.4913 26.6993 11.4892 26.0073 11.4873L26.0004 11.4873C25.8917 11.4869 25.7841 11.4866 25.6783 11.4849L25.6775 11.5349L25.6782 11.4849C25.5151 11.4824 25.3551 11.4919 25.2024 11.5013L25.2055 11.5512L25.2024 11.5013C25.0712 11.5094 24.9492 11.5168 24.829 11.5168C24.5688 11.5168 24.3572 11.7283 24.3572 11.9884V12.9352L21.6442 11.0047L22.2614 10.5656C22.4296 10.446 22.4689 10.2125 22.3494 10.0443L22.3493 10.0443C22.2296 9.87612 21.9963 9.8368 21.828 9.95652L21.828 9.95653L20.8947 10.6205C20.7706 10.7088 20.6965 10.8524 20.6965 11.0047C20.6965 11.1572 20.7706 11.3007 20.8947 11.389C20.8947 11.389 20.8947 11.389 20.8947 11.389L22.4928 12.5261H9.15363C8.67299 12.5261 8.28219 12.917 8.28219 13.3975V22.5848C8.28219 23.0654 8.673 23.4562 9.15363 23.4562H10.8951V23.7266C10.8468 23.7291 10.799 23.7321 10.752 23.735L10.7551 23.7849L28.1084 11.8815ZM7.89164 24.101C8.52433 24.491 9.30016 24.4931 9.99191 24.495L9.99961 24.495C10.1082 24.4953 10.2158 24.4957 10.3216 24.4975L10.3217 24.4975C10.4853 24.5001 10.6451 24.4904 10.7976 24.481M7.89164 24.101L10.7976 24.481M7.89164 24.101C7.90899 24.1488 7.92727 24.1959 7.9465 24.2422L7.90032 24.2614L7.9465 24.2422C8.30626 25.1097 9.16093 25.6711 10.1251 25.6711H11.1711C11.4312 25.6711 11.6428 25.8827 11.6428 26.1428V26.9081L14.3557 24.9776L11.6428 23.0471V23.994C11.6428 24.2541 11.4312 24.4656 11.1711 24.4656C11.0523 24.4656 10.9316 24.4729 10.802 24.4807L10.7976 24.481M7.89164 24.101L10.7976 24.481M10.7976 24.481L10.7975 24.481L10.7945 24.4311L10.7976 24.481ZM25.1049 9.61357H25.0549V9.56357L25.1049 9.61357ZM24.3599 13.8547L24.3599 13.8547C24.4415 13.9126 24.5371 13.9422 24.6331 13.9422C24.707 13.9422 24.7812 13.9247 24.8495 13.8894L24.8496 13.8894C25.007 13.8082 25.1049 13.6475 25.1049 13.4704V13.2738H26.8463H26.8528C26.9179 13.2772 26.9701 13.3314 26.9701 13.3975V22.5848C26.9701 22.653 26.9146 22.7085 26.8463 22.7085H12.4562L11.6401 22.1278C11.4956 22.0249 11.3079 22.0116 11.1504 22.0928C10.993 22.1741 10.8951 22.3348 10.8951 22.5119V22.7085H9.15363C9.08534 22.7085 9.02974 22.6529 9.02974 22.5848V13.3975C9.02974 13.3293 9.0854 13.2736 9.15363 13.2736H23.5436L24.3599 13.8547Z"
      fill="#0A0D12"
      stroke="#0A0D12"
      stroke-width="0.1"
    />
  </svg>
);
export const accountPayableIcon = () => (
  <svg width="37" height="36" viewBox="0 0 37 36" fill="none" xmlns="http://www.w3.org/2000/svg">
    <rect x="0.0410156" width="36" height="36" rx="8" fill="#EAECF5" />
    <path
      d="M29.5742 14.4101L18.8657 25.1186C18.7619 25.2223 18.5937 25.2223 18.49 25.1186L12.4948 19.1234C12.3911 19.0197 12.3911 18.8515 12.4948 18.7478L23.2033 8.03923C23.3071 7.93549 23.4753 7.93549 23.579 8.03923L29.5742 14.0344C29.6779 14.1381 29.6779 14.3063 29.5742 14.4101Z"
      fill="#4E5BA6"
    />
    <path
      d="M29.5741 14.0344L27.9592 12.4194L16.875 23.5036L18.4899 25.1186C18.5937 25.2223 18.7619 25.2223 18.8656 25.1186L29.5741 14.41C29.6778 14.3063 29.6778 14.1381 29.5741 14.0344Z"
      fill="#4E5BA6"
    />
    <path
      d="M14.7967 19.6309C15.0304 19.1986 15.0304 18.6727 14.7967 18.2403C14.7654 18.1823 14.7744 18.1109 14.8211 18.0642L22.5205 10.3648C22.5672 10.3182 22.6386 10.3091 22.6966 10.3405C23.1288 10.5742 23.6549 10.5741 24.0872 10.3404C24.1453 10.3091 24.2167 10.3181 24.2633 10.3648L27.2494 13.3508C27.2961 13.3975 27.3051 13.4689 27.2737 13.527C27.04 13.9593 27.0401 14.4852 27.2737 14.9176C27.3051 14.9756 27.2961 15.047 27.2494 15.0937L19.5499 22.7932C19.5033 22.8398 19.4318 22.8488 19.3738 22.8175C18.9415 22.5838 18.4155 22.5839 17.9833 22.8175C17.9252 22.8488 17.8538 22.8398 17.8071 22.7932L14.8211 19.8071C14.7744 19.7603 14.7654 19.6889 14.7967 19.6309Z"
      fill="#EAECF5"
    />
    <path
      d="M27.2738 14.9174C27.0401 14.4851 27.0401 13.9592 27.2738 13.5268C27.3052 13.4688 27.2961 13.3974 27.2495 13.3507L25.5301 11.6313C25.2212 12.111 25.2212 12.7331 25.5301 13.2127L17.6692 21.0737C17.1895 20.7648 16.5675 20.7648 16.0879 21.0737L17.8073 22.793C17.8539 22.8397 17.9253 22.8487 17.9834 22.8174C18.4156 22.5837 18.9416 22.5837 19.3739 22.8174C19.432 22.8488 19.5034 22.8397 19.55 22.7931L27.2495 15.0936C27.2961 15.0469 27.3052 14.9755 27.2738 14.9174Z"
      fill="#EAECF5"
    />
    <path
      d="M22.578 18.1231C23.4309 17.2702 23.4309 15.8875 22.578 15.0346C21.7252 14.1818 20.3424 14.1818 19.4896 15.0346C18.6367 15.8875 18.6367 17.2702 19.4896 18.1231C20.3424 18.976 21.7252 18.976 22.578 18.1231Z"
      fill="#4E5BA6"
    />
    <path
      d="M18.4449 21.7648L21.7398 18.4699C22.1195 18.0901 22.281 17.636 21.9012 17.2563C21.3028 16.5131 19.1182 17.1091 18.0819 18.1454C17.5795 18.6478 17.0089 19.2185 16.6944 19.5331C16.5405 19.6871 16.3319 19.7736 16.1116 19.7736H16.1003C15.7367 19.7736 15.3109 19.4215 15.2172 19.0703L15.217 16.0257L15.7607 15.482L16.3581 14.8846L18.7306 12.5121C17.7545 12.4027 16.7839 12.3435 16.4074 12.2984C15.9889 12.2483 15.6318 12.4358 15.307 12.7045L12.2697 15.1008C11.8411 15.4554 11.5321 15.9332 11.3841 16.4695C11.0525 17.6706 10.3541 19.7952 9.91076 20.6401L7.69629 22.8546L11.5301 26.6884L12.5964 25.6222C12.7968 25.4218 13.0446 25.2753 13.3168 25.1964L15.5437 24.4465C16.2433 24.2065 16.8591 23.7704 17.3179 23.1903L18.4449 21.7648Z"
      fill="#EAECF5"
    />
    <path
      d="M17.3178 23.1904L18.445 21.7649L21.7399 18.47C22.1197 18.0903 22.2811 17.6361 21.9014 17.2564C21.6814 16.9832 21.247 16.891 20.7342 16.9351C19.9831 16.9997 19.2832 17.343 18.75 17.8761L16.6531 19.9731L15.526 21.3986C15.0673 21.9787 14.4514 22.4148 13.7519 22.6548L11.525 23.4046C11.2528 23.4836 11.0049 23.6301 10.8045 23.8304L9.73828 24.8967L11.5301 26.6886L12.5964 25.6223C12.7968 25.4219 13.0446 25.2754 13.3168 25.1965L15.5437 24.4466C16.2433 24.2066 16.8591 23.7705 17.3178 23.1904Z"
      fill="#EAECF5"
    />
    <path
      d="M10.9084 27.9153L6.47098 23.4779C6.30655 23.3135 6.30655 23.0469 6.47098 22.8824L7.25183 22.1016C7.41627 21.9372 7.68289 21.9372 7.84733 22.1016L12.2847 26.539C12.4491 26.7034 12.4491 26.97 12.2847 27.1345L11.5039 27.9153C11.3394 28.0798 11.0728 28.0798 10.9084 27.9153Z"
      fill="#EAECF5"
    />
    <path
      d="M12.2847 26.539L10.7797 25.0339L9.40332 26.4103L10.9083 27.9153C11.0728 28.0798 11.3394 28.0798 11.5038 27.9153L12.2847 27.1345C12.4492 26.97 12.4492 26.7034 12.2847 26.539Z"
      fill="#EAECF5"
    />
    <path
      d="M21.6102 18.3673C21.1729 17.93 20.464 17.93 20.0267 18.3673L19.5292 18.8648C19.3041 19.0899 19.3041 19.4549 19.5292 19.68L20.0297 20.1805L21.7266 18.4837L21.6102 18.3673Z"
      fill="#EAECF5"
    />
    <path
      d="M29.8204 13.7886L23.8252 7.79339C23.7094 7.67757 23.5553 7.61377 23.3915 7.61377C23.2276 7.61377 23.0737 7.67761 22.9578 7.79339L19.6608 11.0903C19.5251 11.2261 19.5251 11.4462 19.6608 11.5821C19.7966 11.7177 20.0167 11.7178 20.1525 11.5821L23.3915 8.3431L29.2707 14.2223L18.6781 24.8148L17.4439 23.5806C17.4941 23.5237 17.5433 23.4658 17.5906 23.4061L17.7802 23.1664C17.9002 23.1992 18.0314 23.1864 18.1482 23.1233C18.4751 22.9466 18.8813 22.9466 19.2081 23.1233C19.2839 23.1643 19.3658 23.1842 19.4467 23.1842C19.5744 23.1842 19.6999 23.1345 19.7953 23.039L20.6018 22.2325C20.7376 22.0967 20.7376 21.8766 20.6019 21.7408C20.4661 21.6051 20.246 21.605 20.1102 21.7408L19.4048 22.4461C19.1043 22.3141 18.7782 22.2681 18.4596 22.307L18.7052 21.9964L20.2749 20.4268C20.275 20.4267 20.2751 20.4267 20.2751 20.4266L21.6736 19.0281C22.109 18.915 22.5053 18.6887 22.8249 18.3691C23.8119 17.3821 23.8119 15.7759 22.8249 14.7889C21.8379 13.8019 20.2318 13.8019 19.2447 14.7889C18.5864 15.4473 18.3492 16.4253 18.6105 17.3051C18.3187 17.4816 18.0535 17.6825 17.8363 17.8996L16.4488 19.2875C16.3595 19.3768 16.2398 19.426 16.1119 19.426H16.1005C15.9118 19.426 15.6419 19.2151 15.5651 19.0166L15.565 17.8117L22.6648 10.7118C23.1289 10.9159 23.6543 10.9159 24.1183 10.7118L26.902 13.4956C26.698 13.9597 26.698 14.485 26.902 14.9491L21.2729 20.578C21.1372 20.7138 21.1372 20.9339 21.2729 21.0698C21.3408 21.1376 21.4298 21.1716 21.5188 21.1716C21.6077 21.1716 21.6967 21.1376 21.7646 21.0698L27.495 15.3394C27.6508 15.1833 27.6847 14.9474 27.5791 14.7522C27.4025 14.4254 27.4025 14.0192 27.579 13.6925C27.6848 13.4971 27.651 13.261 27.4949 13.105L24.5088 10.119C24.3527 9.963 24.1169 9.92911 23.9215 10.0346C23.5946 10.2113 23.1885 10.2114 22.8616 10.0347C22.6665 9.92921 22.4305 9.963 22.2743 10.119L15.5649 16.8283L15.5648 16.1698L18.9766 12.758C19.071 12.6636 19.103 12.5233 19.0589 12.3972C19.0147 12.2711 18.9023 12.1815 18.7695 12.1667C18.1892 12.1017 17.6185 12.0548 17.16 12.0173C16.8455 11.9915 16.5971 11.9711 16.4489 11.9533C15.9923 11.8987 15.5471 12.0559 15.0887 12.4341L12.0545 14.8279C12.0524 14.8296 12.0503 14.8313 12.0482 14.833C11.5625 15.2349 11.217 15.7688 11.049 16.3771C10.7345 17.5164 10.0548 19.5885 9.62667 20.4326L8.14842 21.9109L8.0932 21.8557C7.94798 21.7105 7.75491 21.6306 7.54959 21.6306C7.34428 21.6306 7.1512 21.7105 7.00603 21.8557L6.22514 22.6366C6.07997 22.7818 6 22.9748 6 23.1802C6 23.3855 6.07997 23.5786 6.22514 23.7238L10.6625 28.1611C10.8077 28.3063 11.0008 28.3863 11.2061 28.3863C11.4114 28.3863 11.6045 28.3063 11.7497 28.1611L12.5305 27.3803C12.6757 27.2351 12.7557 27.042 12.7557 26.8367C12.7557 26.6314 12.6757 26.4383 12.5306 26.2931L12.474 26.2365L12.8423 25.8681C13.0009 25.7096 13.1984 25.5928 13.4137 25.5303C13.4184 25.529 13.4232 25.5275 13.4278 25.5259L15.6566 24.7754C16.1271 24.6139 16.5612 24.3721 16.9425 24.0625L18.2445 25.3644C18.3603 25.4803 18.5144 25.5441 18.6781 25.5441C18.842 25.5441 18.996 25.4802 19.1118 25.3644L29.8204 14.656C29.9362 14.5402 30 14.3861 30 14.2223C30 14.0585 29.9362 13.9044 29.8204 13.7886ZM20.0293 19.689L19.7745 19.4342C19.6853 19.345 19.6853 19.1999 19.7745 19.1107L20.272 18.6132C20.4225 18.4627 20.6202 18.3874 20.8179 18.3874C20.9571 18.3874 21.0963 18.4247 21.219 18.4992L20.0293 19.689ZM19.7364 15.2805C20.4524 14.5645 21.6173 14.5646 22.3333 15.2805C23.0019 15.9491 23.0459 17.0092 22.4657 17.7296C22.4648 17.4711 22.3613 17.2301 22.16 17.0234C21.9382 16.7586 21.5764 16.6055 21.1123 16.5806C20.5501 16.5501 19.873 16.7049 19.2436 16.9786C19.1108 16.3749 19.2904 15.7266 19.7364 15.2805ZM12.0389 26.8886L11.258 27.6695C11.2403 27.6873 11.22 27.691 11.2061 27.691C11.1923 27.691 11.172 27.6873 11.1542 27.6695L6.71681 23.2321C6.699 23.2143 6.69525 23.1941 6.69525 23.1802C6.69525 23.1663 6.699 23.146 6.71681 23.1283L7.4977 22.3473C7.51547 22.3296 7.53572 22.3258 7.54959 22.3258C7.56347 22.3258 7.58377 22.3296 7.60153 22.3474L12.0389 26.7848C12.0567 26.8025 12.0605 26.8228 12.0605 26.8367C12.0605 26.8506 12.0567 26.8709 12.0389 26.8886ZM17.4389 22.4769L17.0452 22.9748C16.9434 23.1036 16.8332 23.2243 16.7156 23.3366C16.7144 23.3378 16.713 23.3389 16.7117 23.3401C16.7108 23.341 16.71 23.342 16.7092 23.3429C16.3453 23.6888 15.9111 23.953 15.4328 24.1171L13.2126 24.8648C12.8879 24.9603 12.59 25.1371 12.3507 25.3764L11.9823 25.7448L8.64009 22.4026L10.1566 20.886C10.1814 20.8612 10.2023 20.8328 10.2187 20.8017C10.6733 19.9352 11.3823 17.7828 11.7192 16.5622C11.8487 16.0932 12.1147 15.6815 12.4884 15.3713L15.5224 12.9777C15.5244 12.976 15.5266 12.9743 15.5286 12.9726C15.8421 12.7133 16.1005 12.6119 16.3661 12.6437C16.5273 12.6631 16.7813 12.6839 17.103 12.7103C17.3616 12.7315 17.6561 12.7557 17.967 12.7843L14.9713 15.7801C14.9061 15.8453 14.8695 15.9337 14.8695 16.0259C14.8695 16.0256 14.8695 16.0261 14.8695 16.0259L14.8697 19.0705C14.8697 19.1008 14.8736 19.1309 14.8815 19.1602C15.0182 19.6722 15.5878 20.1214 16.1005 20.1214H16.1118C16.4256 20.1214 16.7199 19.9998 16.9405 19.7791L18.3279 18.3914C18.5553 18.164 18.8503 17.9554 19.1741 17.7816C19.1753 17.7811 19.1767 17.7807 19.178 17.7801C19.1927 17.7736 19.2066 17.7659 19.2199 17.7576C19.8277 17.44 20.5291 17.245 21.0749 17.275C21.3412 17.2893 21.5386 17.3602 21.6307 17.4745C21.6385 17.4842 21.6468 17.4935 21.6556 17.5023C21.7786 17.6253 21.7782 17.7245 21.7642 17.7971C21.7533 17.8532 21.7275 17.9147 21.6896 17.9787C21.1156 17.5544 20.3001 17.6019 19.7804 18.1216L19.2829 18.619C19.1084 18.7936 19.0122 19.0257 19.0122 19.2725C19.0122 19.5193 19.1084 19.7514 19.2829 19.9259L19.5376 20.1807L18.1993 21.5191C18.1898 21.5286 18.1808 21.5387 18.1724 21.5493L17.4399 22.4757C17.4396 22.4761 17.4392 22.4765 17.4389 22.4769Z"
      fill="#0A0D12"
    />
  </svg>
);

export const ledgerReportIcon = () => (
  <svg width="37" height="36" viewBox="0 0 37 36" fill="none" xmlns="http://www.w3.org/2000/svg">
    <rect x="0.0410156" width="36" height="36" rx="8" fill="#EAECF5" />
    <path
      d="M28.7428 22.7233C28.7173 25.7372 26.2397 28.2148 23.2513 28.1893C20.2118 28.1637 17.7598 25.6861 17.7598 22.6466C17.7598 19.6582 20.2629 17.2062 23.3279 17.2062C26.3419 17.2062 28.7684 19.6838 28.7428 22.7233Z"
      fill="#0A0D12"
    />
    <path
      d="M13.8011 18.2283C15.1293 18.2283 16.483 18.2283 17.8112 18.2283C18.2709 18.2283 18.4497 18.2794 18.1177 18.8158C17.6835 19.5565 17.1471 19.7353 16.3297 19.7097C14.3885 19.6331 12.4218 19.6842 10.4551 19.6842C9.45892 19.6842 9.12688 20.1184 9.35676 21.089C9.71435 22.5194 11.017 23.49 12.8049 23.6177C14.0565 23.6943 15.308 23.7198 16.483 23.1579C16.5596 23.1068 16.764 23.1068 16.764 23.1324C16.8661 23.5666 16.9683 24.0008 16.9938 24.435C16.9938 24.716 16.6618 24.716 16.4574 24.7671C14.2097 25.3801 12.0131 25.4056 9.94422 24.2307C8.69266 23.5155 8.02857 22.3406 7.82424 20.9358C7.59436 19.4032 8.61604 18.2283 10.1741 18.2028C11.3746 18.1772 12.6006 18.2028 13.8011 18.2028C13.8011 18.2028 13.8011 18.2028 13.8011 18.2283Z"
      fill="#0A0D12"
    />
    <path
      d="M19.1396 22.6469C19.1652 20.3225 21.0042 18.4835 23.3285 18.5091C25.6018 18.5091 27.4919 20.4758 27.4663 22.7235C27.4408 24.9967 25.5507 26.8358 23.2264 26.8102C20.902 26.8102 19.1396 24.9712 19.1396 22.6469Z"
      fill="#4E5BA6"
    />
    <path
      fill-rule="evenodd"
      clip-rule="evenodd"
      d="M13.8254 16.2099C15.9965 16.2099 17.7844 14.422 17.7844 12.2509C17.7844 10.0032 16.0475 8.24081 13.8509 8.18972C11.6799 8.16418 9.86636 9.95212 9.84082 12.1232C9.84082 14.422 11.5777 16.2099 13.8254 16.2099ZM11.3225 12.2512C11.348 13.605 12.4463 14.6777 13.8256 14.6777C15.1793 14.6777 16.2777 13.605 16.3032 12.2512C16.3287 10.8975 15.1538 9.72256 13.8001 9.72256C12.4208 9.72256 11.297 10.872 11.3225 12.2512Z"
      fill="#0A0D12"
    />
    <path
      fill-rule="evenodd"
      clip-rule="evenodd"
      d="M22.7666 10.207C21.1064 10.207 19.8037 11.5608 19.8037 13.221C19.8037 14.8812 21.1575 16.2094 22.8177 16.1839C24.4524 16.1839 25.8316 14.7791 25.8061 13.1699C25.7805 11.5352 24.4013 10.207 22.7666 10.207ZM21.3103 13.1953C21.2848 14.0382 21.9233 14.6767 22.7662 14.6767C23.6091 14.6767 24.2732 14.0382 24.2732 13.1953C24.2732 12.378 23.6346 11.7139 22.7917 11.7139C21.9489 11.7139 21.3103 12.3524 21.3103 13.1953Z"
      fill="#0A0D12"
    />
    <path
      d="M23.2517 25.5072C22.8941 25.4306 22.7664 25.2007 22.7408 24.8687C22.7408 24.4345 22.7153 24.0258 22.7408 23.5916C22.7664 23.2595 22.6387 23.1574 22.3066 23.1574C21.8724 23.1829 21.4637 23.1574 21.0295 23.1574C20.6975 23.1574 20.4165 23.0041 20.442 22.6465C20.442 22.2634 20.723 22.1868 21.0551 22.1612C21.5914 22.1357 22.3322 22.3911 22.6387 22.0335C22.9196 21.727 22.7408 21.0374 22.7408 20.501C22.7408 20.1434 22.8685 19.8369 23.2517 19.8369C23.6603 19.8369 23.737 20.1434 23.7625 20.501C23.7881 21.0374 23.5071 21.7526 23.8902 22.0591C24.2223 22.3145 24.9119 22.1357 25.4227 22.1357C25.7548 22.1357 26.0358 22.2123 26.0613 22.5955C26.0868 23.0041 25.8059 23.1063 25.4227 23.1318C24.8864 23.1574 24.1712 22.902 23.8391 23.2595C23.5071 23.6171 23.7625 24.3068 23.737 24.8687C23.7625 25.2518 23.6093 25.4562 23.2517 25.5072Z"
      fill="#FDFDFD"
    />
  </svg>
);

export const accountReceivableIcon = () => (
  <svg width="37" height="36" viewBox="0 0 37 36" fill="none" xmlns="http://www.w3.org/2000/svg">
    <rect x="0.0410156" width="36" height="36" rx="8" fill="#EAECF5" />
    <path
      d="M15.0748 9.54373C14.9998 9.61873 14.9248 9.67498 14.8311 9.73123C14.6248 9.86248 14.3998 9.95623 14.1373 9.99373C13.6311 10.0687 13.1248 9.89998 12.7686 9.52498L13.9123 8.38123L15.0748 9.54373Z"
      fill="#EAECF5"
    />
    <path
      d="M21.5057 15.975L20.362 17.1375C19.762 16.5375 19.7245 15.5812 20.2682 14.9437L20.3245 14.9625C20.4182 15 20.4932 15.0375 20.587 15.0562L21.5057 15.975Z"
      fill="#EAECF5"
    />
    <path
      d="M19.1627 18.3187L18.0189 19.4812L16.8564 18.3187C17.4939 17.6812 18.5252 17.6812 19.1627 18.3187Z"
      fill="#EAECF5"
    />
    <path d="M8.40039 21.15H10.9504V26.6813H8.40039V21.15Z" fill="#EAECF5" />
    <path d="M10.9316 21.7875H12.2816V26.0437H10.9316V21.7875Z" fill="#EAECF5" />
    <path
      d="M24.4123 23.1749C24.1311 22.4062 23.2873 21.9937 22.4998 22.2562L19.4998 23.2499C19.4811 22.7624 19.1811 22.3312 18.7311 22.1437L15.6373 20.8687C14.4373 20.3812 13.0686 20.7562 12.2998 21.8062V26.0624L16.1061 27.2812C16.7436 27.4874 17.4373 27.4687 18.0748 27.2249L23.5311 25.1437C24.2998 24.8437 24.6936 23.9624 24.4123 23.1749Z"
      fill="#EAECF5"
    />
    <path d="M25.0127 9.13123H27.5627V14.6625H25.0127V9.13123Z" fill="#EAECF5" />
    <path d="M23.6621 9.7688H25.0121V14.025H23.6621V9.7688Z" fill="#EAECF5" />
    <path
      d="M23.6627 9.76875V14.025C22.9689 14.9812 21.7314 15.3937 20.5877 15.0562C20.4939 15.0375 20.4189 15 20.3252 14.9625L20.2502 14.9438L17.2127 13.6875C17.0814 13.6312 16.9502 13.5563 16.8377 13.4438C16.7439 13.3688 16.6689 13.275 16.6127 13.1625C16.3314 12.6938 16.3877 12.0938 16.7439 11.7C16.8189 11.625 16.8939 11.55 16.9877 11.4937L16.7814 11.25L15.1689 9.6375L17.8877 8.60625C18.5252 8.3625 19.2002 8.34375 19.8564 8.55L23.6627 9.76875Z"
      fill="#EAECF5"
    />
    <path
      d="M16.6871 14.6438C16.2934 15.0375 15.6559 15.0375 15.2621 14.6438C14.8684 14.25 14.8684 13.6125 15.2621 13.2188C15.6371 12.8438 16.2371 12.825 16.6309 13.1625C16.6871 13.275 16.7621 13.3688 16.8559 13.4438C17.0809 13.8375 17.0059 14.325 16.6871 14.6438Z"
      fill="#EAECF5"
    />
    <path
      d="M11.6809 12.9375C11.6434 12.975 11.6246 13.0125 11.5871 13.05L10.4434 11.9062L11.5871 10.7625C11.6809 10.8562 11.7746 10.9687 11.8496 11.1C12.1871 11.6625 12.1309 12.4125 11.6809 12.9375Z"
      fill="#EAECF5"
    />
    <path
      d="M20.3621 17.1375L19.1809 18.3187C18.5434 17.6812 17.5121 17.6812 16.8746 18.3187L11.5871 13.05C11.6246 13.0125 11.6621 12.975 11.6809 12.9375C12.1121 12.4125 12.1871 11.6625 11.8309 11.0625C11.7559 10.95 11.6809 10.8375 11.5684 10.725L12.7684 9.5437C13.1246 9.89995 13.6309 10.0687 14.1371 10.0125C14.3809 9.97495 14.6246 9.89995 14.8309 9.74995C14.9246 9.6937 14.9996 9.6187 15.0746 9.56245L15.1496 9.63745L16.7621 11.2687L16.9871 11.4937C16.8934 11.55 16.8184 11.625 16.7434 11.7C16.3871 12.1125 16.3309 12.6937 16.6121 13.1625C16.1809 12.8062 15.5434 12.8437 15.1871 13.275C14.8309 13.7062 14.8684 14.3437 15.2996 14.7C15.7309 15.0562 16.3684 15.0187 16.7246 14.5875C17.0059 14.2687 17.0434 13.8 16.8371 13.4437C16.9496 13.5375 17.0621 13.6312 17.2121 13.6875L20.2496 14.9437C19.7246 15.5812 19.7621 16.5375 20.3621 17.1375Z"
      fill="#4E5BA6"
    />
    <mask
      id="path-14-outside-1_5_9519"
      maskUnits="userSpaceOnUse"
      x="7.0625"
      y="7.0459"
      width="22"
      height="21"
      fill="black"
    >
      <rect fill="white" x="7.0625" y="7.0459" width="22" height="21" />
      <path d="M22.3875 21.9562L19.7438 22.8187C19.6125 22.3687 19.275 22.0124 18.8438 21.8249L15.75 20.5499C14.475 20.0249 12.9938 20.3812 12.1313 21.4687H11.2688V21.1499C11.2688 20.9624 11.1187 20.8124 10.9312 20.8124H8.4C8.2125 20.8124 8.08125 20.9624 8.0625 21.1499V26.6812C8.0625 26.8687 8.2125 26.9999 8.4 27.0187H10.95C11.1375 27.0187 11.2875 26.8687 11.2875 26.6812V26.3624H12.2625L16.0125 27.5812C16.725 27.8062 17.4937 27.7874 18.1875 27.5249L21.6937 26.1937C21.8625 26.1374 21.9563 25.9499 21.9 25.7812C21.8438 25.6124 21.6375 25.5187 21.4688 25.5937L17.9625 26.9249C17.4 27.1312 16.7812 27.1499 16.2188 26.9624L12.6375 25.7999V21.8999C13.3313 21.0374 14.4937 20.7374 15.525 21.1499L18.6188 22.4249C19.0875 22.6124 19.2937 23.1374 19.1437 23.5874C18.975 24.0749 18.4125 24.3187 17.925 24.1312L15.1875 23.0249C15.0188 22.9687 14.8313 23.0437 14.7563 23.2124C14.7 23.3812 14.775 23.5687 14.9438 23.6249L17.6812 24.7312C18.4875 25.0499 19.4063 24.6749 19.7438 23.8687C19.8 23.7374 19.8375 23.6062 19.8375 23.4749L22.6312 22.5562C23.2687 22.3499 23.9438 22.6874 24.15 23.3249C24.3563 23.9249 24.0375 24.5999 23.4375 24.8249L22.8563 25.0499C22.6875 25.1062 22.5938 25.3124 22.6688 25.4812C22.725 25.6499 22.9312 25.7437 23.1 25.6687L23.6813 25.4437C24.6375 25.0874 25.1437 24.0187 24.7875 23.0624C24.3937 22.1249 23.3438 21.6187 22.3875 21.9562ZM10.6125 26.3624H8.71875V21.4874H10.6125V26.3624ZM11.2688 22.1249H11.9625V25.7249H11.2688V22.1249ZM27.5438 8.79369H24.9938C24.8063 8.79369 24.6562 8.94369 24.6562 9.11244V9.43119H23.6813L19.9313 8.21244C19.2188 7.98744 18.45 8.00619 17.7563 8.26869L15.225 9.22494L14.1375 8.13744C14.0063 8.00619 13.8 8.02494 13.6688 8.13744L10.2 11.6624C10.0687 11.7937 10.0687 11.9999 10.2 12.1312C10.2 12.1312 11.9062 13.8187 12.4688 14.3999C12.6562 14.5687 12.9 14.4374 12.9375 14.3999C13.05 14.2687 13.05 14.0624 12.9188 13.9312C12.825 13.8374 12.2625 13.2749 12.0187 13.0499C12.5062 12.3749 12.5062 11.4562 12.0187 10.7812L12.7875 10.0124C13.4438 10.4812 14.3062 10.5374 15.0562 10.0124L16.5187 11.4749C16.2375 11.7937 16.0875 12.2062 16.1063 12.6187C15.375 12.5437 14.7 13.0687 14.625 13.7999C14.55 14.5312 15.075 15.2062 15.8062 15.2812C16.5562 15.3749 17.2125 14.8312 17.2875 14.0812L19.7438 15.0937C19.4063 15.7499 19.4625 16.5187 19.8937 17.1187L19.125 17.8874C18.45 17.3999 17.5312 17.3999 16.8563 17.8874C16.8188 17.8499 16.05 17.0812 16.0312 17.0624C15.9187 16.9312 15.6937 16.9124 15.5625 17.0437C15.4125 17.1749 15.4125 17.3999 15.5438 17.5312C18.5625 20.5499 17.5125 19.4999 17.7375 19.7249C17.8687 19.8562 18.075 19.8562 18.2062 19.7249C18.5625 19.3687 21.6375 16.2937 21.6937 16.2374C21.825 16.1062 21.825 15.8999 21.6937 15.7687L21.45 15.5249C22.35 15.4874 23.1938 15.0749 23.775 14.3812H24.6375V14.6999C24.6375 14.8874 24.7875 15.0374 24.975 15.0374H27.525C27.7125 15.0374 27.8625 14.8874 27.8625 14.6999V9.13119C27.8813 8.94369 27.7313 8.79369 27.5438 8.79369ZM11.55 11.2312C11.775 11.6437 11.775 12.1312 11.55 12.5437L10.8938 11.8874L11.55 11.2312ZM18.0187 19.0124L17.3625 18.3562C17.775 18.1124 18.2812 18.1124 18.675 18.3562L18.0187 19.0124ZM13.275 9.52494L13.9312 8.86869L14.5875 9.52494C14.175 9.76869 13.6687 9.76869 13.275 9.52494ZM16.4625 14.4187C16.2 14.6812 15.7687 14.6812 15.4875 14.4187C15.225 14.1562 15.225 13.7249 15.4875 13.4437C15.8062 13.1249 16.35 13.1999 16.575 13.5937C16.725 13.8749 16.6687 14.2124 16.4625 14.4187ZM20.3812 16.6499C20.1375 16.2562 20.1375 15.7499 20.3812 15.3374C20.3812 15.3374 20.4 15.3374 20.4 15.3562L21.0375 15.9937L20.3812 16.6499ZM20.3812 14.6437L17.3438 13.3874C16.9125 13.1999 16.6687 12.6937 16.8188 12.2249C16.9875 11.7374 17.55 11.4937 18.0375 11.6812L20.775 12.7874C20.9813 12.8812 21.225 12.7312 21.225 12.4874C21.225 12.3562 21.15 12.2249 21.0187 12.1687L18.2812 11.0624C17.8875 10.9124 17.4563 10.9124 17.0625 11.0812L15.75 9.76869L18 8.90619C18.5625 8.69994 19.1813 8.68119 19.7438 8.84994L23.325 9.99369V13.8937C22.6313 14.7937 21.4125 15.0937 20.3812 14.6437ZM23.9813 13.7062V10.1062H24.675V13.7062H23.9813ZM27.225 14.3437H25.3312V9.46869H27.225V14.3437ZM13.5 15.4312C13.3688 15.2999 13.3688 15.0937 13.5 14.9624C13.6312 14.8312 13.8562 14.8312 13.9875 14.9812L15 15.9937C15.1312 16.1249 15.1312 16.3312 15.0187 16.4624C14.8875 16.5937 14.6812 16.5937 14.55 16.4812C14.4562 16.3874 14.85 16.7812 13.5 15.4312Z" />
    </mask>
    <path
      d="M22.3875 21.9562L19.7438 22.8187C19.6125 22.3687 19.275 22.0124 18.8438 21.8249L15.75 20.5499C14.475 20.0249 12.9938 20.3812 12.1313 21.4687H11.2688V21.1499C11.2688 20.9624 11.1187 20.8124 10.9312 20.8124H8.4C8.2125 20.8124 8.08125 20.9624 8.0625 21.1499V26.6812C8.0625 26.8687 8.2125 26.9999 8.4 27.0187H10.95C11.1375 27.0187 11.2875 26.8687 11.2875 26.6812V26.3624H12.2625L16.0125 27.5812C16.725 27.8062 17.4937 27.7874 18.1875 27.5249L21.6937 26.1937C21.8625 26.1374 21.9563 25.9499 21.9 25.7812C21.8438 25.6124 21.6375 25.5187 21.4688 25.5937L17.9625 26.9249C17.4 27.1312 16.7812 27.1499 16.2188 26.9624L12.6375 25.7999V21.8999C13.3313 21.0374 14.4937 20.7374 15.525 21.1499L18.6188 22.4249C19.0875 22.6124 19.2937 23.1374 19.1437 23.5874C18.975 24.0749 18.4125 24.3187 17.925 24.1312L15.1875 23.0249C15.0188 22.9687 14.8313 23.0437 14.7563 23.2124C14.7 23.3812 14.775 23.5687 14.9438 23.6249L17.6812 24.7312C18.4875 25.0499 19.4063 24.6749 19.7438 23.8687C19.8 23.7374 19.8375 23.6062 19.8375 23.4749L22.6312 22.5562C23.2687 22.3499 23.9438 22.6874 24.15 23.3249C24.3563 23.9249 24.0375 24.5999 23.4375 24.8249L22.8563 25.0499C22.6875 25.1062 22.5938 25.3124 22.6688 25.4812C22.725 25.6499 22.9312 25.7437 23.1 25.6687L23.6813 25.4437C24.6375 25.0874 25.1437 24.0187 24.7875 23.0624C24.3937 22.1249 23.3438 21.6187 22.3875 21.9562ZM10.6125 26.3624H8.71875V21.4874H10.6125V26.3624ZM11.2688 22.1249H11.9625V25.7249H11.2688V22.1249ZM27.5438 8.79369H24.9938C24.8063 8.79369 24.6562 8.94369 24.6562 9.11244V9.43119H23.6813L19.9313 8.21244C19.2188 7.98744 18.45 8.00619 17.7563 8.26869L15.225 9.22494L14.1375 8.13744C14.0063 8.00619 13.8 8.02494 13.6688 8.13744L10.2 11.6624C10.0687 11.7937 10.0687 11.9999 10.2 12.1312C10.2 12.1312 11.9062 13.8187 12.4688 14.3999C12.6562 14.5687 12.9 14.4374 12.9375 14.3999C13.05 14.2687 13.05 14.0624 12.9188 13.9312C12.825 13.8374 12.2625 13.2749 12.0187 13.0499C12.5062 12.3749 12.5062 11.4562 12.0187 10.7812L12.7875 10.0124C13.4438 10.4812 14.3062 10.5374 15.0562 10.0124L16.5187 11.4749C16.2375 11.7937 16.0875 12.2062 16.1063 12.6187C15.375 12.5437 14.7 13.0687 14.625 13.7999C14.55 14.5312 15.075 15.2062 15.8062 15.2812C16.5562 15.3749 17.2125 14.8312 17.2875 14.0812L19.7438 15.0937C19.4063 15.7499 19.4625 16.5187 19.8937 17.1187L19.125 17.8874C18.45 17.3999 17.5312 17.3999 16.8563 17.8874C16.8188 17.8499 16.05 17.0812 16.0312 17.0624C15.9187 16.9312 15.6937 16.9124 15.5625 17.0437C15.4125 17.1749 15.4125 17.3999 15.5438 17.5312C18.5625 20.5499 17.5125 19.4999 17.7375 19.7249C17.8687 19.8562 18.075 19.8562 18.2062 19.7249C18.5625 19.3687 21.6375 16.2937 21.6937 16.2374C21.825 16.1062 21.825 15.8999 21.6937 15.7687L21.45 15.5249C22.35 15.4874 23.1938 15.0749 23.775 14.3812H24.6375V14.6999C24.6375 14.8874 24.7875 15.0374 24.975 15.0374H27.525C27.7125 15.0374 27.8625 14.8874 27.8625 14.6999V9.13119C27.8813 8.94369 27.7313 8.79369 27.5438 8.79369ZM11.55 11.2312C11.775 11.6437 11.775 12.1312 11.55 12.5437L10.8938 11.8874L11.55 11.2312ZM18.0187 19.0124L17.3625 18.3562C17.775 18.1124 18.2812 18.1124 18.675 18.3562L18.0187 19.0124ZM13.275 9.52494L13.9312 8.86869L14.5875 9.52494C14.175 9.76869 13.6687 9.76869 13.275 9.52494ZM16.4625 14.4187C16.2 14.6812 15.7687 14.6812 15.4875 14.4187C15.225 14.1562 15.225 13.7249 15.4875 13.4437C15.8062 13.1249 16.35 13.1999 16.575 13.5937C16.725 13.8749 16.6687 14.2124 16.4625 14.4187ZM20.3812 16.6499C20.1375 16.2562 20.1375 15.7499 20.3812 15.3374C20.3812 15.3374 20.4 15.3374 20.4 15.3562L21.0375 15.9937L20.3812 16.6499ZM20.3812 14.6437L17.3438 13.3874C16.9125 13.1999 16.6687 12.6937 16.8188 12.2249C16.9875 11.7374 17.55 11.4937 18.0375 11.6812L20.775 12.7874C20.9813 12.8812 21.225 12.7312 21.225 12.4874C21.225 12.3562 21.15 12.2249 21.0187 12.1687L18.2812 11.0624C17.8875 10.9124 17.4563 10.9124 17.0625 11.0812L15.75 9.76869L18 8.90619C18.5625 8.69994 19.1813 8.68119 19.7438 8.84994L23.325 9.99369V13.8937C22.6313 14.7937 21.4125 15.0937 20.3812 14.6437ZM23.9813 13.7062V10.1062H24.675V13.7062H23.9813ZM27.225 14.3437H25.3312V9.46869H27.225V14.3437ZM13.5 15.4312C13.3688 15.2999 13.3688 15.0937 13.5 14.9624C13.6312 14.8312 13.8562 14.8312 13.9875 14.9812L15 15.9937C15.1312 16.1249 15.1312 16.3312 15.0187 16.4624C14.8875 16.5937 14.6812 16.5937 14.55 16.4812C14.4562 16.3874 14.85 16.7812 13.5 15.4312Z"
      fill="#0A0D12"
    />
    <path
      d="M22.3875 21.9562L19.7438 22.8187C19.6125 22.3687 19.275 22.0124 18.8438 21.8249L15.75 20.5499C14.475 20.0249 12.9938 20.3812 12.1313 21.4687H11.2688V21.1499C11.2688 20.9624 11.1187 20.8124 10.9312 20.8124H8.4C8.2125 20.8124 8.08125 20.9624 8.0625 21.1499V26.6812C8.0625 26.8687 8.2125 26.9999 8.4 27.0187H10.95C11.1375 27.0187 11.2875 26.8687 11.2875 26.6812V26.3624H12.2625L16.0125 27.5812C16.725 27.8062 17.4937 27.7874 18.1875 27.5249L21.6937 26.1937C21.8625 26.1374 21.9563 25.9499 21.9 25.7812C21.8438 25.6124 21.6375 25.5187 21.4688 25.5937L17.9625 26.9249C17.4 27.1312 16.7812 27.1499 16.2188 26.9624L12.6375 25.7999V21.8999C13.3313 21.0374 14.4937 20.7374 15.525 21.1499L18.6188 22.4249C19.0875 22.6124 19.2937 23.1374 19.1437 23.5874C18.975 24.0749 18.4125 24.3187 17.925 24.1312L15.1875 23.0249C15.0188 22.9687 14.8313 23.0437 14.7563 23.2124C14.7 23.3812 14.775 23.5687 14.9438 23.6249L17.6812 24.7312C18.4875 25.0499 19.4063 24.6749 19.7438 23.8687C19.8 23.7374 19.8375 23.6062 19.8375 23.4749L22.6312 22.5562C23.2687 22.3499 23.9438 22.6874 24.15 23.3249C24.3563 23.9249 24.0375 24.5999 23.4375 24.8249L22.8563 25.0499C22.6875 25.1062 22.5938 25.3124 22.6688 25.4812C22.725 25.6499 22.9312 25.7437 23.1 25.6687L23.6813 25.4437C24.6375 25.0874 25.1437 24.0187 24.7875 23.0624C24.3937 22.1249 23.3438 21.6187 22.3875 21.9562ZM10.6125 26.3624H8.71875V21.4874H10.6125V26.3624ZM11.2688 22.1249H11.9625V25.7249H11.2688V22.1249ZM27.5438 8.79369H24.9938C24.8063 8.79369 24.6562 8.94369 24.6562 9.11244V9.43119H23.6813L19.9313 8.21244C19.2188 7.98744 18.45 8.00619 17.7563 8.26869L15.225 9.22494L14.1375 8.13744C14.0063 8.00619 13.8 8.02494 13.6688 8.13744L10.2 11.6624C10.0687 11.7937 10.0687 11.9999 10.2 12.1312C10.2 12.1312 11.9062 13.8187 12.4688 14.3999C12.6562 14.5687 12.9 14.4374 12.9375 14.3999C13.05 14.2687 13.05 14.0624 12.9188 13.9312C12.825 13.8374 12.2625 13.2749 12.0187 13.0499C12.5062 12.3749 12.5062 11.4562 12.0187 10.7812L12.7875 10.0124C13.4438 10.4812 14.3062 10.5374 15.0562 10.0124L16.5187 11.4749C16.2375 11.7937 16.0875 12.2062 16.1063 12.6187C15.375 12.5437 14.7 13.0687 14.625 13.7999C14.55 14.5312 15.075 15.2062 15.8062 15.2812C16.5562 15.3749 17.2125 14.8312 17.2875 14.0812L19.7438 15.0937C19.4063 15.7499 19.4625 16.5187 19.8937 17.1187L19.125 17.8874C18.45 17.3999 17.5312 17.3999 16.8563 17.8874C16.8188 17.8499 16.05 17.0812 16.0312 17.0624C15.9187 16.9312 15.6937 16.9124 15.5625 17.0437C15.4125 17.1749 15.4125 17.3999 15.5438 17.5312C18.5625 20.5499 17.5125 19.4999 17.7375 19.7249C17.8687 19.8562 18.075 19.8562 18.2062 19.7249C18.5625 19.3687 21.6375 16.2937 21.6937 16.2374C21.825 16.1062 21.825 15.8999 21.6937 15.7687L21.45 15.5249C22.35 15.4874 23.1938 15.0749 23.775 14.3812H24.6375V14.6999C24.6375 14.8874 24.7875 15.0374 24.975 15.0374H27.525C27.7125 15.0374 27.8625 14.8874 27.8625 14.6999V9.13119C27.8813 8.94369 27.7313 8.79369 27.5438 8.79369ZM11.55 11.2312C11.775 11.6437 11.775 12.1312 11.55 12.5437L10.8938 11.8874L11.55 11.2312ZM18.0187 19.0124L17.3625 18.3562C17.775 18.1124 18.2812 18.1124 18.675 18.3562L18.0187 19.0124ZM13.275 9.52494L13.9312 8.86869L14.5875 9.52494C14.175 9.76869 13.6687 9.76869 13.275 9.52494ZM16.4625 14.4187C16.2 14.6812 15.7687 14.6812 15.4875 14.4187C15.225 14.1562 15.225 13.7249 15.4875 13.4437C15.8062 13.1249 16.35 13.1999 16.575 13.5937C16.725 13.8749 16.6687 14.2124 16.4625 14.4187ZM20.3812 16.6499C20.1375 16.2562 20.1375 15.7499 20.3812 15.3374C20.3812 15.3374 20.4 15.3374 20.4 15.3562L21.0375 15.9937L20.3812 16.6499ZM20.3812 14.6437L17.3438 13.3874C16.9125 13.1999 16.6687 12.6937 16.8188 12.2249C16.9875 11.7374 17.55 11.4937 18.0375 11.6812L20.775 12.7874C20.9813 12.8812 21.225 12.7312 21.225 12.4874C21.225 12.3562 21.15 12.2249 21.0187 12.1687L18.2812 11.0624C17.8875 10.9124 17.4563 10.9124 17.0625 11.0812L15.75 9.76869L18 8.90619C18.5625 8.69994 19.1813 8.68119 19.7438 8.84994L23.325 9.99369V13.8937C22.6313 14.7937 21.4125 15.0937 20.3812 14.6437ZM23.9813 13.7062V10.1062H24.675V13.7062H23.9813ZM27.225 14.3437H25.3312V9.46869H27.225V14.3437ZM13.5 15.4312C13.3688 15.2999 13.3688 15.0937 13.5 14.9624C13.6312 14.8312 13.8562 14.8312 13.9875 14.9812L15 15.9937C15.1312 16.1249 15.1312 16.3312 15.0187 16.4624C14.8875 16.5937 14.6812 16.5937 14.55 16.4812C14.4562 16.3874 14.85 16.7812 13.5 15.4312Z"
      stroke="#0A0D12"
      stroke-width="0.4"
      stroke-linejoin="round"
      mask="url(#path-14-outside-1_5_9519)"
    />
  </svg>
);

export const inventoryReportIcon = () => (
  <svg width="37" height="36" viewBox="0 0 37 36" fill="none" xmlns="http://www.w3.org/2000/svg">
    <rect x="0.0410156" width="36" height="36" rx="8" fill="#EAECF5" />
    <path
      d="M19.8447 21.4349C19.8447 19.2247 21.6317 17.4377 23.8185 17.4612C26.0288 17.4847 27.7688 19.2482 27.7452 21.482C27.7452 23.6687 25.9582 25.4087 23.7715 25.4087C21.6082 25.4087 19.8682 23.6452 19.8447 21.4349Z"
      fill="#0A0D12"
    />
    <path
      fill-rule="evenodd"
      clip-rule="evenodd"
      d="M22.3135 15.7919C23.16 15.3922 24.0065 14.969 24.9 14.5222C24.9772 15.0161 24.9935 15.4898 25.009 15.9365V15.9366C25.0171 16.1704 25.0249 16.3968 25.0411 16.6149C25.0646 17.0146 25.2762 17.2262 25.6759 17.2498C26.0521 17.2498 26.2403 17.0381 26.3108 16.6854C26.734 14.4987 25.9816 12.7117 24.03 11.6065C22.7465 10.8834 21.4352 10.216 20.1282 9.55063L20.1282 9.55062L20.1282 9.55059C19.7351 9.35052 19.3425 9.15064 18.9511 8.94951C18.0106 8.47924 16.9995 8.36168 16.0119 8.80843C13.9427 9.74897 11.9206 10.76 10.016 11.9827C9.1695 12.5236 8.62869 13.2995 8.4641 14.3106C8.18194 16.2152 8.25248 18.0962 8.34653 20.0008C8.41707 21.5057 9.09896 22.6108 10.3922 23.3868C11.8778 24.2357 13.3826 25.008 14.9066 25.7901L14.9067 25.7902L15.4006 26.0438C16.1765 26.4435 17.0465 26.7022 17.94 26.4905C18.843 26.2923 19.6428 25.8466 20.4361 25.4044L20.4362 25.4044L20.597 25.3149C20.9262 25.1268 21.0438 24.8211 20.8322 24.4684C20.6441 24.1157 20.3384 24.0216 19.9857 24.1862C19.9034 24.2215 19.8152 24.2685 19.727 24.3155L19.727 24.3156C19.6388 24.3626 19.5507 24.4096 19.4684 24.4449C19.3303 24.5106 19.1941 24.5837 19.0566 24.6575L19.0566 24.6575L19.0565 24.6576C18.7023 24.8476 18.3399 25.0421 17.9165 25.1268V18.3314C17.9165 18.0962 17.9635 17.9552 18.1987 17.8376L20.7851 16.5444C20.7851 16.622 20.7923 16.6854 20.7987 16.7424V16.7424L20.7987 16.7424C20.8039 16.789 20.8087 16.8313 20.8087 16.8735C20.8087 17.3203 21.0673 17.6025 21.4905 17.5789C21.8903 17.5789 22.1019 17.2968 22.1019 16.8735C22.1019 16.7952 22.1045 16.7168 22.1071 16.6385L22.1071 16.6384C22.1123 16.4817 22.1176 16.3249 22.1019 16.1681C22.1019 16.0035 22.1489 15.8625 22.3135 15.7919ZM16.6234 24.7513V21.5299V21.5292C16.6234 20.4948 16.6234 19.4605 16.5998 18.4026C16.6234 18.0735 16.5058 17.9089 16.2236 17.7678L16.2236 17.7678C14.1779 16.7567 12.1323 15.7456 10.1101 14.711C9.73392 14.5229 9.61635 14.5464 9.59284 14.9932C9.49878 16.6626 9.49878 18.3086 9.59284 19.978C9.66338 20.9656 10.0396 21.718 10.8861 22.2118C12.6261 23.2229 14.4131 24.1399 16.2001 25.0334C16.5763 25.2215 16.6234 25.0805 16.6234 24.7513ZM14.4834 11.0195L14.6694 10.9201L14.6694 10.9201C15.3092 10.5778 15.9329 10.2442 16.5761 10.0084C17.0228 9.84379 17.4931 9.82028 17.9399 10.0084C20.0091 10.9489 22.0547 11.9835 23.9593 13.2062C24.0029 13.2353 24.0375 13.2643 24.0854 13.3045C24.1149 13.3293 24.1495 13.3584 24.1944 13.3943C23.2775 13.8646 22.431 14.2878 21.5846 14.711L21.5845 14.7111C21.4434 14.7816 21.3258 14.7346 21.2082 14.664C19.7063 13.8811 18.2043 13.0873 16.7097 12.2974L16.7096 12.2974C16.0048 11.9249 15.3017 11.5533 14.601 11.1841C14.5775 11.137 14.5539 11.09 14.4834 11.0195ZM18.2872 16.3566C18.8406 16.0773 19.4152 15.7873 20.0323 15.4866C18.5234 14.6778 17.0352 13.8998 15.5539 13.1254C14.796 12.7292 14.0399 12.334 13.2839 11.9361C13.1428 11.8656 13.0017 11.795 12.8136 11.8891C12.6582 11.9735 12.501 12.0562 12.3431 12.1393C11.6653 12.4961 10.9741 12.86 10.3447 13.3939C12.6255 14.5226 14.8828 15.6512 17.1166 16.7799C17.2342 16.8504 17.3517 16.8269 17.4928 16.7564C17.7543 16.6256 18.0183 16.4924 18.2872 16.3566Z"
      fill="#0A0D12"
    />
    <path
      d="M26.9461 21.4351C26.9461 23.1986 25.6058 24.5859 23.8658 24.5859C22.0788 24.5859 20.6915 23.2457 20.668 21.4586C20.668 19.6716 22.0317 18.2843 23.7953 18.2373C25.5352 18.2373 26.9461 19.6481 26.9461 21.4351Z"
      fill="#4E5BA6"
    />
    <path
      d="M24.9698 20.706C24.6641 20.3768 24.4525 20.1417 24.2408 19.9065C24.1468 19.789 24.0527 19.6479 24.1938 19.4833C24.3584 19.3187 24.523 19.3657 24.6641 19.5068C25.0638 19.93 25.44 20.3298 25.8162 20.7765C26.0279 21.0352 25.9808 21.2938 25.6046 21.2938C24.4525 21.3173 23.3003 21.2938 22.1481 21.2938C21.9365 21.2938 21.7954 21.1762 21.819 20.9646C21.8425 20.7295 22.0071 20.706 22.1952 20.706C23.0652 20.706 23.9587 20.706 24.9698 20.706Z"
      fill="#FDFDFD"
    />
    <path
      d="M22.7366 22.1881C23.0423 22.5408 23.2539 22.7524 23.4655 22.9875C23.6066 23.1286 23.6301 23.3167 23.442 23.4578C23.2774 23.5753 23.1363 23.5048 23.0423 23.3872C22.6661 22.964 22.2663 22.5643 21.8901 22.141C21.655 21.8589 21.749 21.6237 22.1017 21.6237C23.2774 21.6002 24.4531 21.6237 25.6052 21.6237C25.7933 21.6237 25.9109 21.7178 25.9109 21.9059C25.9109 22.1175 25.7228 22.1881 25.5347 22.1881C25.0409 22.1881 24.5707 22.1881 24.0769 22.1881C23.6771 22.1881 23.2774 22.1881 22.7366 22.1881Z"
      fill="#FDFDFD"
    />
  </svg>
);

export const totalInventoryIcon = () => (
  <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path
      d="M13.8037 15.4347C13.8037 13.2244 15.5907 11.4374 17.7775 11.4609C19.9877 11.4844 21.7277 13.2479 21.7042 15.4817C21.7042 17.6685 19.9172 19.4085 17.7305 19.4085C15.5672 19.4085 13.8272 17.645 13.8037 15.4347Z"
      fill="#0A0D12"
    />
    <path
      fill-rule="evenodd"
      clip-rule="evenodd"
      d="M16.2725 9.79192C17.119 9.39219 17.9655 8.96895 18.859 8.5222C18.9362 9.01612 18.9525 9.48978 18.968 9.93652V9.93657C18.9761 10.1704 18.9839 10.3968 19.0001 10.6149C19.0236 11.0146 19.2352 11.2262 19.6349 11.2498C20.0111 11.2498 20.1992 11.0381 20.2698 10.6854C20.693 8.49868 19.9406 6.71166 17.989 5.60653C16.7055 4.88345 15.3942 4.21595 14.0872 3.55063L14.0872 3.55062L14.0871 3.55059C13.6941 3.35052 13.3015 3.15064 12.9101 2.94951C11.9695 2.47924 10.9585 2.36168 9.9709 2.80843C7.90171 3.74897 5.87956 4.76005 3.97497 5.98275C3.12849 6.52356 2.58768 7.2995 2.42308 8.31058C2.14092 10.2152 2.21146 12.0962 2.30551 14.0008C2.37606 15.5057 3.05794 16.6108 4.35118 17.3868C5.83681 18.2357 7.34159 19.008 8.86554 19.7901L8.86564 19.7902L9.35955 20.0438C10.1355 20.4435 11.0055 20.7022 11.899 20.4905C12.802 20.2923 13.6018 19.8466 14.3951 19.4044L14.3951 19.4044L14.556 19.3149C14.8852 19.1268 15.0028 18.8211 14.7912 18.4684C14.603 18.1157 14.2974 18.0216 13.9447 18.1862C13.8624 18.2215 13.7742 18.2685 13.686 18.3155L13.686 18.3156C13.5978 18.3626 13.5097 18.4096 13.4274 18.4449C13.2893 18.5106 13.153 18.5837 13.0156 18.6575L13.0155 18.6575L13.0155 18.6576C12.6613 18.8476 12.2989 19.0421 11.8755 19.1268V12.3314C11.8755 12.0962 11.9225 11.9552 12.1576 11.8376L14.7441 10.5444C14.7441 10.622 14.7512 10.6854 14.7577 10.7424V10.7424L14.7577 10.7424C14.7629 10.789 14.7676 10.8313 14.7676 10.8735C14.7676 11.3203 15.0263 11.6025 15.4495 11.5789C15.8493 11.5789 16.0609 11.2968 16.0609 10.8735C16.0609 10.7952 16.0635 10.7168 16.0661 10.6385L16.0661 10.6384C16.0713 10.4817 16.0766 10.3249 16.0609 10.1681C16.0609 10.0035 16.1079 9.86246 16.2725 9.79192ZM10.5823 18.7513V15.5299V15.5292C10.5823 14.4948 10.5823 13.4605 10.5588 12.4026C10.5823 12.0735 10.4648 11.9089 10.1826 11.7678L10.1826 11.7678C8.13693 10.7567 6.09127 9.74562 4.06912 8.71103C3.6929 8.52292 3.57533 8.54643 3.55182 8.99319C3.45777 10.6626 3.45777 12.3086 3.55182 13.978C3.62236 14.9656 3.99858 15.718 4.84506 16.2118C6.58505 17.2229 8.37208 18.1399 10.1591 19.0334C10.5353 19.2215 10.5823 19.0805 10.5823 18.7513ZM8.44238 5.01946L8.62837 4.92008L8.62838 4.92007C9.26816 4.57785 9.89184 4.24424 10.5351 4.00839C10.9818 3.84379 11.4521 3.82028 11.8989 4.00839C13.968 4.94892 16.0137 5.98352 17.9183 7.20621C17.9619 7.23528 17.9965 7.26434 18.0443 7.30451C18.0739 7.32933 18.1085 7.3584 18.1534 7.39432C17.2364 7.86457 16.39 8.2878 15.5435 8.71102L15.5434 8.71107C15.4024 8.78161 15.2848 8.73459 15.1672 8.66405C13.6652 7.8811 12.1633 7.0873 10.6687 6.2974L10.6686 6.29736C9.96382 5.92487 9.26068 5.55326 8.55995 5.18406C8.53644 5.13703 8.51292 5.09 8.44238 5.01946ZM12.2462 10.3566C12.7996 10.0773 13.3741 9.78728 13.9912 9.48664C12.4824 8.67777 10.9941 7.89978 9.51284 7.12543C8.75498 6.72925 7.99893 6.33403 7.24289 5.93611C7.10181 5.86557 6.96073 5.79503 6.77262 5.88908C6.61714 5.97349 6.45998 6.05621 6.30209 6.13932C5.62427 6.49611 4.93306 6.85995 4.30371 7.39394C6.58451 8.52259 8.8418 9.65123 11.0756 10.7799C11.1931 10.8504 11.3107 10.8269 11.4518 10.7564C11.7133 10.6256 11.9773 10.4924 12.2462 10.3566Z"
      fill="#0A0D12"
    />
    <path
      d="M20.905 15.4351C20.905 17.1986 19.5648 18.5859 17.8248 18.5859C16.0378 18.5859 14.6505 17.2457 14.627 15.4586C14.627 13.6716 15.9907 12.2843 17.7542 12.2373C19.4942 12.2373 20.905 13.6481 20.905 15.4351Z"
      fill="#4E5BA6"
    />
    <path
      d="M18.9287 14.7059C18.6231 14.3767 18.4115 14.1416 18.1998 13.9065C18.1058 13.7889 18.0117 13.6478 18.1528 13.4832C18.3174 13.3186 18.482 13.3657 18.6231 13.5067C19.0228 13.93 19.399 14.3297 19.7752 14.7765C19.9869 15.0351 19.9398 15.2938 19.5636 15.2938C18.4115 15.3173 17.2593 15.2938 16.1071 15.2938C15.8955 15.2938 15.7544 15.1762 15.7779 14.9646C15.8015 14.7294 15.9661 14.7059 16.1542 14.7059C17.0242 14.7059 17.9177 14.7059 18.9287 14.7059Z"
      fill="#FDFDFD"
    />
    <path
      d="M16.6956 16.1881C17.0013 16.5408 17.2129 16.7524 17.4245 16.9875C17.5656 17.1286 17.5891 17.3167 17.401 17.4578C17.2364 17.5753 17.0953 17.5048 17.0013 17.3872C16.625 16.964 16.2253 16.5643 15.8491 16.141C15.614 15.8589 15.708 15.6237 16.0607 15.6237C17.2364 15.6002 18.4121 15.6237 19.5642 15.6237C19.7523 15.6237 19.8699 15.7178 19.8699 15.9059C19.8699 16.1175 19.6818 16.1881 19.4937 16.1881C18.9999 16.1881 18.5296 16.1881 18.0359 16.1881C17.6361 16.1881 17.2364 16.1881 16.6956 16.1881Z"
      fill="#FDFDFD"
    />
  </svg>
);
export const arrowSort = () => (
  <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path
      fill-rule="evenodd"
      clip-rule="evenodd"
      d="M4.31311 2.97945C4.50838 2.78419 4.82496 2.78419 5.02022 2.97945L7.68689 5.64612C7.88215 5.84138 7.88215 6.15797 7.68689 6.35323C7.49162 6.54849 7.17504 6.54849 6.97978 6.35323L5.16667 4.54011V12.6663C5.16667 12.9425 4.94281 13.1663 4.66667 13.1663C4.39052 13.1663 4.16667 12.9425 4.16667 12.6663V4.54011L2.35355 6.35323C2.15829 6.54849 1.84171 6.54849 1.64645 6.35323C1.45118 6.15797 1.45118 5.84138 1.64645 5.64612L4.31311 2.97945ZM11.3333 2.83301C11.6095 2.83301 11.8333 3.05687 11.8333 3.33301V11.4592L13.6464 9.64612C13.8417 9.45086 14.1583 9.45086 14.3536 9.64612C14.5488 9.84138 14.5488 10.158 14.3536 10.3532L11.6869 13.0199C11.4916 13.2152 11.175 13.2152 10.9798 13.0199L8.31311 10.3532C8.11785 10.158 8.11785 9.84138 8.31311 9.64612C8.50838 9.45086 8.82496 9.45086 9.02022 9.64612L10.8333 11.4592V3.33301C10.8333 3.05687 11.0572 2.83301 11.3333 2.83301Z"
      fill="#181D27"
    />
  </svg>
);
export const closeIconCircle = () => (
  <svg width="40" height="40" viewBox="0 0 40 40" fill="none" xmlns="http://www.w3.org/2000/svg">
    <rect width="40" height="40" rx="20" fill="#FDFDFD" />
    <path
      fill-rule="evenodd"
      clip-rule="evenodd"
      d="M13.4697 13.4697C13.7626 13.1768 14.2374 13.1768 14.5303 13.4697L20 18.9393L25.4697 13.4697C25.7626 13.1768 26.2374 13.1768 26.5303 13.4697C26.8232 13.7626 26.8232 14.2374 26.5303 14.5303L21.0607 20L26.5303 25.4697C26.8232 25.7626 26.8232 26.2374 26.5303 26.5303C26.2374 26.8232 25.7626 26.8232 25.4697 26.5303L20 21.0607L14.5303 26.5303C14.2374 26.8232 13.7626 26.8232 13.4697 26.5303C13.1768 26.2374 13.1768 25.7626 13.4697 25.4697L18.9393 20L13.4697 14.5303C13.1768 14.2374 13.1768 13.7626 13.4697 13.4697Z"
      fill="#0A0D12"
    />
  </svg>
);

export const searchIcon = () => (
  <svg width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path
      fill-rule="evenodd"
      clip-rule="evenodd"
      d="M5.27565 2.12995C5.98086 1.83784 6.73669 1.6875 7.5 1.6875C8.26331 1.6875 9.01914 1.83784 9.72435 2.12995C10.4296 2.42206 11.0703 2.8502 11.6101 3.38994C12.1498 3.92968 12.5779 4.57045 12.8701 5.27565C13.1622 5.98086 13.3125 6.73669 13.3125 7.5C13.3125 8.26331 13.1622 9.01914 12.8701 9.72435C12.65 10.2557 12.3526 10.7505 11.9885 11.193L16.1477 15.3523C16.3674 15.5719 16.3674 15.9281 16.1477 16.1477C15.9281 16.3674 15.5719 16.3674 15.3523 16.1477L11.193 11.9885C10.7505 12.3526 10.2557 12.65 9.72435 12.8701C9.01914 13.1622 8.26331 13.3125 7.5 13.3125C6.73669 13.3125 5.98086 13.1622 5.27565 12.8701C4.57045 12.5779 3.92968 12.1498 3.38994 11.6101C2.8502 11.0703 2.42206 10.4296 2.12995 9.72435C1.83784 9.01914 1.6875 8.26331 1.6875 7.5C1.6875 6.73669 1.83784 5.98086 2.12995 5.27565C2.42206 4.57045 2.8502 3.92968 3.38994 3.38994C3.92968 2.8502 4.57045 2.42206 5.27565 2.12995ZM7.5 2.8125C6.88443 2.8125 6.27489 2.93375 5.70617 3.16931C5.13746 3.40488 4.62071 3.75016 4.18544 4.18544C3.75016 4.62071 3.40488 5.13746 3.16931 5.70617C2.93375 6.27489 2.8125 6.88443 2.8125 7.5C2.8125 8.11557 2.93375 8.72511 3.16931 9.29383C3.40488 9.86254 3.75016 10.3793 4.18544 10.8146C4.62071 11.2498 5.13746 11.5951 5.70617 11.8307C6.27489 12.0663 6.88443 12.1875 7.5 12.1875C8.11557 12.1875 8.72512 12.0663 9.29383 11.8307C9.86254 11.5951 10.3793 11.2498 10.8146 10.8146C11.2498 10.3793 11.5951 9.86254 11.8307 9.29383C12.0663 8.72512 12.1875 8.11557 12.1875 7.5C12.1875 6.88443 12.0663 6.27489 11.8307 5.70617C11.5951 5.13746 11.2498 4.62071 10.8146 4.18544C10.3793 3.75016 9.86254 3.40488 9.29383 3.16931C8.72511 2.93375 8.11557 2.8125 7.5 2.8125Z"
      fill="#181D27"
    />
  </svg>
);

export const sortIcon = () => (
  <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path
      fill-rule="evenodd"
      clip-rule="evenodd"
      d="M4.31311 2.9797C4.50838 2.78444 4.82496 2.78444 5.02022 2.9797L7.68689 5.64637C7.88215 5.84163 7.88215 6.15821 7.68689 6.35347C7.49162 6.54873 7.17504 6.54873 6.97978 6.35347L5.16667 4.54036V12.6666C5.16667 12.9427 4.94281 13.1666 4.66667 13.1666C4.39052 13.1666 4.16667 12.9427 4.16667 12.6666V4.54036L2.35355 6.35347C2.15829 6.54873 1.84171 6.54873 1.64645 6.35347C1.45118 6.15821 1.45118 5.84163 1.64645 5.64637L4.31311 2.9797ZM11.3333 2.83325C11.6095 2.83325 11.8333 3.05711 11.8333 3.33325V11.4595L13.6464 9.64637C13.8417 9.4511 14.1583 9.4511 14.3536 9.64637C14.5488 9.84163 14.5488 10.1582 14.3536 10.3535L11.6869 13.0201C11.4916 13.2154 11.175 13.2154 10.9798 13.0201L8.31311 10.3535C8.11785 10.1582 8.11785 9.84163 8.31311 9.64637C8.50838 9.4511 8.82496 9.4511 9.02022 9.64637L10.8333 11.4595V3.33325C10.8333 3.05711 11.0572 2.83325 11.3333 2.83325Z"
      fill="#181D27"
    />
  </svg>
);

export const helpIcon = () => (
  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path
      fill-rule="evenodd"
      clip-rule="evenodd"
      d="M4.44194 4.44194C3.5157 5.36819 3.125 6.99553 3.125 10C3.125 13.0045 3.5157 14.6318 4.44194 15.5581C5.36819 16.4843 6.99553 16.875 10 16.875C13.0045 16.875 14.6318 16.4843 15.5581 15.5581C16.4843 14.6318 16.875 13.0045 16.875 10C16.875 6.99553 16.4843 5.36819 15.5581 4.44194C14.6318 3.5157 13.0045 3.125 10 3.125C6.99553 3.125 5.36819 3.5157 4.44194 4.44194ZM3.55806 3.55806C4.88181 2.2343 7.00447 1.875 10 1.875C12.9955 1.875 15.1182 2.2343 16.4419 3.55806C17.7657 4.88181 18.125 7.00447 18.125 10C18.125 12.9955 17.7657 15.1182 16.4419 16.4419C15.1182 17.7657 12.9955 18.125 10 18.125C7.00447 18.125 4.88181 17.7657 3.55806 16.4419C2.2343 15.1182 1.875 12.9955 1.875 10C1.875 7.00447 2.2343 4.88181 3.55806 3.55806ZM9.56305 6.91942C10.0664 6.8187 10.5891 6.8912 11.046 7.1251C11.5049 7.35909 11.8716 7.74086 12.0869 8.20882C12.3023 8.67699 12.3536 9.20409 12.2325 9.70501C12.1115 10.2059 11.8251 10.6514 11.4197 10.9696C11.0143 11.2877 10.5135 11.4599 9.99812 11.4583C9.65295 11.4573 9.37397 11.1766 9.375 10.8315C9.37604 10.4863 9.6567 10.2073 10.0019 10.2083C10.2361 10.209 10.4638 10.1308 10.6481 9.98617C10.8323 9.84157 10.9625 9.63907 11.0175 9.41138C11.0726 9.18369 11.0492 8.94409 10.9513 8.73129C10.8534 8.51849 10.6866 8.3449 10.4779 8.23855L10.4767 8.23794C10.2708 8.13244 10.0352 8.09973 9.8083 8.14513C9.5814 8.19052 9.37654 8.31136 9.22704 8.48797C9.00403 8.75144 8.60966 8.78423 8.3462 8.56122C8.08274 8.3382 8.04994 7.94384 8.27296 7.68037C8.60477 7.28837 9.05946 7.02018 9.56305 6.91942ZM10 12.7083C10.3452 12.7083 10.625 12.9882 10.625 13.3333V13.3417C10.625 13.6868 10.3452 13.9667 10 13.9667C9.65482 13.9667 9.375 13.6868 9.375 13.3417V13.3333C9.375 12.9882 9.65482 12.7083 10 12.7083Z"
      fill="#717680"
    />
  </svg>
);

export const audioIcon = () => (
  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path
      fill-rule="evenodd"
      clip-rule="evenodd"
      d="M9.99967 3.95837C8.39732 3.95837 6.8606 4.59491 5.72757 5.72794C4.59454 6.86097 3.95801 8.39769 3.95801 10V10.4588C4.27763 10.2957 4.63415 10.2084 4.99967 10.2084H5.83301C6.4408 10.2084 7.02369 10.4498 7.45346 10.8796C7.88323 11.3094 8.12467 11.8923 8.12467 12.5V15C8.12467 15.6078 7.88323 16.1907 7.45346 16.6205C7.02369 17.0503 6.4408 17.2917 5.83301 17.2917H4.99967C4.39189 17.2917 3.80899 17.0503 3.37922 16.6205C2.94945 16.1907 2.70801 15.6078 2.70801 15V10C2.70801 8.06617 3.47623 6.21151 4.84369 4.84405C6.21114 3.4766 8.0658 2.70837 9.99967 2.70837C11.9335 2.70837 13.7882 3.4766 15.1557 4.84405C16.5231 6.21151 17.2913 8.06617 17.2913 10V15C17.2913 15.6078 17.0499 16.1907 16.6201 16.6205C16.1904 17.0503 15.6075 17.2917 14.9997 17.2917H14.1663C13.5586 17.2917 12.9757 17.0503 12.5459 16.6205C12.1161 16.1907 11.8747 15.6078 11.8747 15V12.5C11.8747 11.8923 12.1161 11.3094 12.5459 10.8796C12.9757 10.4498 13.5586 10.2084 14.1663 10.2084H14.9997C15.3652 10.2084 15.7217 10.2957 16.0413 10.4588V10C16.0413 8.39769 15.4048 6.86097 14.2718 5.72794C13.1387 4.59491 11.602 3.95837 9.99967 3.95837ZM16.0413 12.5C16.0413 12.2238 15.9316 11.9588 15.7362 11.7635C15.5409 11.5681 15.2759 11.4584 14.9997 11.4584H14.1663C13.8901 11.4584 13.6251 11.5681 13.4298 11.7635C13.2344 11.9588 13.1247 12.2238 13.1247 12.5V15C13.1247 15.2763 13.2344 15.5413 13.4298 15.7366C13.6251 15.932 13.8901 16.0417 14.1663 16.0417H14.9997C15.2759 16.0417 15.5409 15.932 15.7362 15.7366C15.9316 15.5413 16.0413 15.2763 16.0413 15V12.5ZM3.95801 12.5V15C3.95801 15.2763 4.06775 15.5413 4.2631 15.7366C4.45846 15.932 4.72341 16.0417 4.99967 16.0417H5.83301C6.10927 16.0417 6.37423 15.932 6.56958 15.7366C6.76493 15.5413 6.87467 15.2763 6.87467 15V12.5C6.87467 12.2238 6.76493 11.9588 6.56958 11.7635C6.37423 11.5681 6.10927 11.4584 5.83301 11.4584H4.99967C4.72341 11.4584 4.45846 11.5681 4.2631 11.7635C4.06775 11.9588 3.95801 12.2238 3.95801 12.5Z"
      fill="#717680"
    />
  </svg>
);

export const checkBoxIcon = () => (
  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <rect x="2.6" y="2.6" width="14.8" height="14.8" rx="1.4" stroke="#4E5BA6" stroke-width="1.2" />
    <path
      d="M7 9.39407L9.39407 11.7881L14.1822 7"
      stroke="#FAFAFA"
      stroke-width="1.19703"
      stroke-linecap="round"
      stroke-linejoin="round"
    />
  </svg>
);

export const buildingIcon = () => (
  <svg width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path
      fill-rule="evenodd"
      clip-rule="evenodd"
      d="M3.26126 3.26126C3.78871 2.73382 4.50408 2.4375 5.25 2.4375H8.25C8.99592 2.4375 9.71129 2.73382 10.2387 3.26126C10.7662 3.78871 11.0625 4.50408 11.0625 5.25V15.1875H13.6875V13.2343C13.3518 13.1392 13.0427 12.9595 12.7916 12.7084C12.4048 12.3216 12.1875 11.797 12.1875 11.25V9.75C12.1875 9.20299 12.4048 8.67839 12.7916 8.29159C13.1784 7.9048 13.703 7.6875 14.25 7.6875C14.797 7.6875 15.3216 7.9048 15.7084 8.29159C16.0952 8.67839 16.3125 9.20299 16.3125 9.75V11.25C16.3125 11.797 16.0952 12.3216 15.7084 12.7084C15.4573 12.9595 15.1482 13.1392 14.8125 13.2343V15.1875H15.75C16.0607 15.1875 16.3125 15.4393 16.3125 15.75C16.3125 16.0607 16.0607 16.3125 15.75 16.3125H2.25C1.93934 16.3125 1.6875 16.0607 1.6875 15.75C1.6875 15.4393 1.93934 15.1875 2.25 15.1875H2.4375V5.25C2.4375 4.50408 2.73382 3.78871 3.26126 3.26126ZM3.5625 15.1875H6.1875V12.75C6.1875 12.4393 6.43934 12.1875 6.75 12.1875C7.06066 12.1875 7.3125 12.4393 7.3125 12.75V15.1875H9.9375V5.25C9.9375 4.80245 9.75971 4.37322 9.44324 4.05676C9.12678 3.74029 8.69755 3.5625 8.25 3.5625H5.25C4.80245 3.5625 4.37322 3.74029 4.05676 4.05676C3.74029 4.37323 3.5625 4.80245 3.5625 5.25V15.1875ZM5.4375 6.75C5.4375 6.43934 5.68934 6.1875 6 6.1875H7.5C7.81066 6.1875 8.0625 6.43934 8.0625 6.75C8.0625 7.06066 7.81066 7.3125 7.5 7.3125H6C5.68934 7.3125 5.4375 7.06066 5.4375 6.75ZM14.25 8.8125C14.0014 8.8125 13.7629 8.91127 13.5871 9.08709C13.4113 9.2629 13.3125 9.50136 13.3125 9.75V11.25C13.3125 11.4986 13.4113 11.7371 13.5871 11.9129C13.7629 12.0887 14.0014 12.1875 14.25 12.1875C14.4986 12.1875 14.7371 12.0887 14.9129 11.9129C15.0887 11.7371 15.1875 11.4986 15.1875 11.25V9.75C15.1875 9.50136 15.0887 9.2629 14.9129 9.08709C14.7371 8.91127 14.4986 8.8125 14.25 8.8125ZM5.4375 9.75C5.4375 9.43934 5.68934 9.1875 6 9.1875H7.5C7.81066 9.1875 8.0625 9.43934 8.0625 9.75C8.0625 10.0607 7.81066 10.3125 7.5 10.3125H6C5.68934 10.3125 5.4375 10.0607 5.4375 9.75Z"
      fill="#252B37"
    />
  </svg>
);

export const upArrow = () => (
  <svg width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path
      fill-rule="evenodd"
      clip-rule="evenodd"
      d="M5.4375 5.25C5.4375 4.93934 5.68934 4.6875 6 4.6875H12.75C13.0607 4.6875 13.3125 4.93934 13.3125 5.25V12C13.3125 12.3107 13.0607 12.5625 12.75 12.5625C12.4393 12.5625 12.1875 12.3107 12.1875 12V6.60799L5.64775 13.1477C5.42808 13.3674 5.07192 13.3674 4.85225 13.1477C4.63258 12.9281 4.63258 12.5719 4.85225 12.3523L11.392 5.8125H6C5.68934 5.8125 5.4375 5.56066 5.4375 5.25Z"
      fill="#2E7A31"
    />
  </svg>
);

export const downArrow = () => (
  <svg width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path
      fill-rule="evenodd"
      clip-rule="evenodd"
      d="M12.5625 12.75C12.5625 13.0607 12.3107 13.3125 12 13.3125L5.25 13.3125C4.93934 13.3125 4.6875 13.0607 4.6875 12.75L4.6875 6C4.6875 5.68934 4.93934 5.4375 5.25 5.4375C5.56066 5.4375 5.8125 5.68934 5.8125 6L5.8125 11.392L12.3523 4.85225C12.5719 4.63258 12.9281 4.63258 13.1477 4.85225C13.3674 5.07192 13.3674 5.42808 13.1477 5.64775L6.60799 12.1875L12 12.1875C12.3107 12.1875 12.5625 12.4393 12.5625 12.75Z"
      fill="#FF3B30"
    />
  </svg>
);

export const receiveMoney = () => (
  <svg width="36" height="31" viewBox="0 0 36 31" fill="none" xmlns="http://www.w3.org/2000/svg">
    <rect x="0.253906" width="30.0493" height="30.0493" rx="15.0247" fill="#4AC2FF20" />
    <path
      fill-rule="evenodd"
      clip-rule="evenodd"
      d="M16.2354 10.4226C16.2354 9.9616 16.6091 9.58789 17.0701 9.58789H23.5015C23.9625 9.58789 24.3362 9.9616 24.3362 10.4226C24.3362 10.8836 23.9625 11.2573 23.5015 11.2573H21.6325C21.8704 11.6625 22.0292 12.1092 22.1153 12.5657H23.5015C23.9625 12.5657 24.3362 12.9394 24.3362 13.4004C24.3362 13.8614 23.9625 14.2351 23.5015 14.2351H22.1153C21.9978 14.8581 21.7447 15.4632 21.3399 15.969C20.7973 16.6473 20.0055 17.1138 19.0099 17.1988L22.2289 20.5648C22.5476 20.8979 22.5358 21.4263 22.2026 21.7449C21.8694 22.0635 21.3411 22.0518 21.0224 21.7186L16.4668 16.955C16.2358 16.7135 16.1712 16.3574 16.3025 16.0501C16.4339 15.7428 16.7359 15.5434 17.0701 15.5434H18.6779C19.3124 15.5434 19.7447 15.2907 20.0364 14.9261C20.1916 14.7321 20.3139 14.4964 20.3971 14.2351H17.0701C16.6091 14.2351 16.2354 13.8614 16.2354 13.4004C16.2354 12.9394 16.6091 12.5657 17.0701 12.5657H20.3971C20.3139 12.3043 20.1916 12.0686 20.0364 11.8746C19.7447 11.51 19.3124 11.2573 18.6779 11.2573H17.0701C16.6091 11.2573 16.2354 10.8836 16.2354 10.4226Z"
      fill="#4AC2FF"
    />
    <path
      fill-rule="evenodd"
      clip-rule="evenodd"
      d="M20.2218 2.30518L21.5379 2.30518C21.9989 2.30518 22.3726 2.67889 22.3726 3.13988C22.3726 3.60087 21.9989 3.97458 21.5379 3.97458H20.2859C17.4589 3.97458 15.4224 3.97635 13.8712 4.18484C12.344 4.39012 11.413 4.7819 10.7232 5.47171C10.0333 6.16155 9.64157 7.09226 9.43631 8.61941C9.22782 10.1706 9.22605 12.2075 9.22605 15.0357C9.22605 17.862 9.22782 19.8982 9.43631 21.4492C9.64158 22.9763 10.0334 23.9073 10.7232 24.5971C11.413 25.2869 12.3437 25.6787 13.8707 25.884C15.4217 26.0924 17.4583 26.0942 20.2859 26.0942C23.1128 26.0942 25.1494 26.0924 26.7005 25.884C28.2278 25.6787 29.1587 25.2869 29.8486 24.5971C30.5384 23.9072 30.9302 22.9765 31.1354 21.4495C31.3439 19.8985 31.3457 17.862 31.3457 15.0344V13.7823C31.3457 13.3214 31.7194 12.9476 32.1804 12.9476C32.6414 12.9476 33.0151 13.3214 33.0151 13.7823V15.0985C33.0151 17.8482 33.0151 19.9969 32.7899 21.6719C32.5598 23.384 32.0808 24.7258 31.029 25.7775C29.9772 26.8293 28.6351 27.3083 26.9229 27.5385C25.2477 27.7636 23.099 27.7636 20.3499 27.7636H20.2217C17.4721 27.7636 15.3234 27.7636 13.6483 27.5385C11.9363 27.3083 10.5945 26.8293 9.54273 25.7775C8.49094 24.7257 8.01191 23.3836 7.78178 21.6716C7.55662 19.9966 7.55663 17.8482 7.55664 15.0998V14.9715C7.55663 12.2213 7.55662 10.0722 7.78178 8.39703C8.01192 6.6848 8.49096 5.34303 9.54273 4.29126C10.5945 3.23947 11.9366 2.76045 13.6488 2.53032C15.324 2.30515 17.4727 2.30516 20.2218 2.30518Z"
      fill="#4AC2FF"
    />
    <path
      fill-rule="evenodd"
      clip-rule="evenodd"
      d="M32.7554 2.52998C33.0814 2.85588 33.0816 3.38438 32.7557 3.71043L28.0853 8.38299L31.5409 8.54559C32.0014 8.56726 32.3572 8.95812 32.3355 9.4186C32.3138 9.87909 31.923 10.2348 31.4625 10.2132L27.5511 10.0291C27.5442 10.0288 27.5373 10.0284 27.5304 10.0279C26.8904 9.98181 26.2935 9.7666 25.8545 9.31913C25.4143 8.8705 25.2229 8.28006 25.2194 7.6601L25.0709 3.80478C25.0532 3.34413 25.4123 2.95632 25.8729 2.93858C26.3336 2.92085 26.7214 3.2799 26.7391 3.74056L26.8736 7.23385L31.5749 2.53026C31.9008 2.20421 32.4293 2.20409 32.7554 2.52998Z"
      fill="#4AC2FF"
    />
  </svg>
);

export const sendMoney = () => (
  <svg width="36" height="31" viewBox="0 0 36 31" fill="none" xmlns="http://www.w3.org/2000/svg">
    <rect x="0.253906" y="0.0493164" width="30.0493" height="30.0493" rx="15.0247" fill="#4AC2FF20" />
    <path
      fill-rule="evenodd"
      clip-rule="evenodd"
      d="M16.2354 10.4744C16.2354 10.0134 16.6091 9.63965 17.0701 9.63965H23.5015C23.9625 9.63965 24.3362 10.0134 24.3362 10.4744C24.3362 10.9353 23.9625 11.3091 23.5015 11.3091H21.6325C21.8704 11.7142 22.0292 12.161 22.1153 12.6174H23.5015C23.9625 12.6174 24.3362 12.9911 24.3362 13.4521C24.3362 13.9131 23.9625 14.2868 23.5015 14.2868H22.1153C21.9978 14.9099 21.7447 15.5149 21.3399 16.0208C20.7973 16.6991 20.0055 17.1656 19.0099 17.2506L22.2289 20.6165C22.5476 20.9497 22.5358 21.4781 22.2026 21.7967C21.8694 22.1153 21.3411 22.1035 21.0224 21.7704L16.4668 17.0068C16.2358 16.7652 16.1712 16.4092 16.3025 16.1019C16.4339 15.7945 16.7359 15.5952 17.0701 15.5952H18.6779C19.3124 15.5952 19.7447 15.3425 20.0364 14.9779C20.1916 14.7839 20.3139 14.5481 20.3971 14.2868H17.0701C16.6091 14.2868 16.2354 13.9131 16.2354 13.4521C16.2354 12.9911 16.6091 12.6174 17.0701 12.6174H20.3971C20.3139 12.3561 20.1916 12.1204 20.0364 11.9264C19.7447 11.5617 19.3124 11.3091 18.6779 11.3091H17.0701C16.6091 11.3091 16.2354 10.9353 16.2354 10.4744Z"
      fill="#4AC2FF"
    />
    <path
      fill-rule="evenodd"
      clip-rule="evenodd"
      d="M20.2218 2.35498L21.5379 2.35498C21.9989 2.35498 22.3726 2.72869 22.3726 3.18968C22.3726 3.65068 21.9989 4.02439 21.5379 4.02439H20.2859C17.4589 4.02439 15.4224 4.02616 13.8712 4.23465C12.344 4.43992 11.413 4.83171 10.7232 5.52152C10.0333 6.21136 9.64157 7.14206 9.43631 8.66922C9.22782 10.2204 9.22605 12.2573 9.22605 15.0855C9.22605 17.9118 9.22782 19.948 9.43631 21.499C9.64158 23.0261 10.0334 23.9571 10.7232 24.6469C11.413 25.3367 12.3437 25.7285 13.8707 25.9338C15.4217 26.1422 17.4583 26.144 20.2859 26.144C23.1128 26.144 25.1494 26.1422 26.7005 25.9338C28.2278 25.7285 29.1587 25.3367 29.8485 24.6469C30.5384 23.957 30.9302 23.0263 31.1354 21.4993C31.3439 19.9483 31.3457 17.9118 31.3457 15.0842V13.8321C31.3457 13.3712 31.7194 12.9974 32.1804 12.9974C32.6414 12.9974 33.0151 13.3712 33.0151 13.8321V15.1483C33.0151 17.898 33.0151 20.0467 32.7899 21.7217C32.5598 23.4338 32.0808 24.7756 31.029 25.8273C29.9772 26.8791 28.6351 27.3582 26.9229 27.5883C25.2477 27.8134 23.099 27.8134 20.3499 27.8134H20.2217C17.4721 27.8134 15.3234 27.8134 13.6483 27.5883C11.9363 27.3581 10.5945 26.8791 9.54273 25.8273C8.49094 24.7755 8.01191 23.4334 7.78178 21.7214C7.55662 20.0464 7.55663 17.898 7.55664 15.1496V15.0213C7.55663 12.2711 7.55662 10.1221 7.78178 8.44683C8.01192 6.73461 8.49096 5.39283 9.54273 4.34107C10.5945 3.28927 11.9366 2.81025 13.6488 2.58012C15.324 2.35496 17.4727 2.35497 20.2218 2.35498Z"
      fill="#4AC2FF"
    />
    <path
      fill-rule="evenodd"
      clip-rule="evenodd"
      d="M25.3149 10.0154C24.9889 9.68953 24.9888 9.16103 25.3146 8.83498L29.985 4.16242L26.5294 3.99982C26.0689 3.97815 25.7132 3.58729 25.7348 3.12681C25.7565 2.66632 26.1474 2.31059 26.6078 2.33226L30.5193 2.51631C30.5262 2.51664 30.5331 2.51705 30.5399 2.51754C31.1799 2.5636 31.7768 2.77881 32.2158 3.22628C32.656 3.67491 32.8475 4.26535 32.8509 4.88531L32.9994 8.74063C33.0171 9.20128 32.658 9.58909 32.1974 9.60683C31.7367 9.62456 31.3489 9.26551 31.3312 8.80485L31.1967 5.31156L26.4954 10.0151C26.1695 10.3412 25.641 10.3413 25.3149 10.0154Z"
      fill="#4AC2FF"
    />
  </svg>
);

export const totalCost = () => (
  <svg width="36" height="31" viewBox="0 0 36 31" fill="none" xmlns="http://www.w3.org/2000/svg">
    <rect x="0.253906" y="0.0986328" width="30.0493" height="30.0493" rx="15.0247" fill="#4AC2FF20" />
    <g clip-path="url(#clip0_233_10138)">
      <path
        fill-rule="evenodd"
        clip-rule="evenodd"
        d="M8.71387 23.7043C8.71387 23.2433 9.08758 22.8696 9.54857 22.8696H14.9145C15.3755 22.8696 15.7492 23.2433 15.7492 23.7043C15.7492 24.1653 15.3755 24.539 14.9145 24.539H10.3833V29.0703C10.3833 29.5313 10.0096 29.905 9.54857 29.905C9.08758 29.905 8.71387 29.5313 8.71387 29.0703V23.7043Z"
        fill="#4AC2FF"
      />
      <path
        fill-rule="evenodd"
        clip-rule="evenodd"
        d="M23.1423 2.31645C20.3063 1.68397 17.3406 2.01107 14.7106 3.2464C12.0806 4.48173 9.93528 6.55536 8.61127 9.14184C7.28726 11.7283 6.85955 14.6812 7.39527 17.5371C7.48026 17.9901 7.18186 18.4263 6.72877 18.5113C6.27568 18.5963 5.83947 18.2979 5.75448 17.8448C5.15058 14.6255 5.63272 11.2968 7.12524 8.38115C8.61777 5.46548 11.0362 3.12793 14.0009 1.73538C16.9656 0.342822 20.3088 -0.0259026 23.5057 0.687076C26.7026 1.40005 29.5723 3.15438 31.6644 5.67469C31.9588 6.0294 31.91 6.55564 31.5553 6.85008C31.2006 7.14452 30.6743 7.09566 30.3799 6.74095C28.524 4.50519 25.9783 2.94893 23.1423 2.31645ZM33.8431 11.7364C34.2962 11.6514 34.7324 11.9498 34.8174 12.4029C35.4213 15.6222 34.9392 18.951 33.4466 21.8666C31.9541 24.7823 29.5357 27.1198 26.571 28.5124C23.6063 29.9049 20.2631 30.2737 17.0662 29.5607C13.8693 28.8477 10.9996 27.0934 8.90749 24.5731C8.61305 24.2184 8.66191 23.6921 9.01662 23.3977C9.37134 23.1032 9.89758 23.1521 10.192 23.5068C12.0479 25.7426 14.5936 27.2988 17.4296 27.9313C20.2656 28.5638 23.2313 28.2367 25.8613 27.0014C28.4913 25.766 30.6366 23.6924 31.9606 21.1059C33.2846 18.5194 33.7123 15.5666 33.1766 12.7107C33.0916 12.2576 33.39 11.8214 33.8431 11.7364Z"
        fill="#4AC2FF"
      />
      <path
        fill-rule="evenodd"
        clip-rule="evenodd"
        d="M31.0219 0.338867C31.4829 0.338867 31.8566 0.712577 31.8566 1.17357V6.53952C31.8566 7.00051 31.4829 7.37422 31.0219 7.37422H25.656C25.195 7.37422 24.8213 7.00051 24.8213 6.53952C24.8213 6.07852 25.195 5.70482 25.656 5.70482H30.1872V1.17357C30.1872 0.712577 30.5609 0.338867 31.0219 0.338867Z"
        fill="#4AC2FF"
      />
      <path
        fill-rule="evenodd"
        clip-rule="evenodd"
        d="M15.9307 10.1125C15.9307 9.65154 16.3044 9.27783 16.7654 9.27783H23.8082C24.2692 9.27783 24.6429 9.65154 24.6429 10.1125C24.6429 10.5735 24.2692 10.9472 23.8082 10.9472H21.62C21.9282 11.4319 22.1264 11.9791 22.224 12.5387H23.8082C24.2692 12.5387 24.6429 12.9124 24.6429 13.3734C24.6429 13.8343 24.2692 14.2081 23.8082 14.2081H22.224C22.103 14.9018 21.8273 15.5766 21.3792 16.1366C20.7681 16.9004 19.8624 17.414 18.7147 17.4647L22.3573 21.2736C22.6759 21.6068 22.6641 22.1352 22.331 22.4538C21.9978 22.7724 21.4694 22.7606 21.1508 22.4275L16.1621 17.2111C15.9311 16.9695 15.8665 16.6135 15.9978 16.3062C16.1292 15.9988 16.4312 15.7995 16.7654 15.7995H18.5261C19.2455 15.7995 19.7414 15.5114 20.0756 15.0937C20.2726 14.8475 20.4219 14.5437 20.5151 14.2081H16.7654C16.3044 14.2081 15.9307 13.8343 15.9307 13.3734C15.9307 12.9124 16.3044 12.5387 16.7654 12.5387H20.5151C20.4219 12.203 20.2726 11.8992 20.0756 11.653C19.7414 11.2353 19.2455 10.9472 18.5261 10.9472H16.7654C16.3044 10.9472 15.9307 10.5735 15.9307 10.1125Z"
        fill="#4AC2FF"
      />
    </g>
    <defs>
      <clipPath id="clip0_233_10138">
        <rect width="30.0493" height="30.0493" fill="white" transform="translate(5.26172 0.0986328)" />
      </clipPath>
    </defs>
  </svg>
);

export const cashFlow = () => (
  <svg width="43" height="36" viewBox="0 0 43 36" fill="none" xmlns="http://www.w3.org/2000/svg">
    <rect x="0.253906" width="36" height="36" rx="18" fill="#4AC2FF20" />
    <g clip-path="url(#clip0_233_10166)">
      <path
        fill-rule="evenodd"
        clip-rule="evenodd"
        d="M10.3906 28.2803C10.3906 27.728 10.8383 27.2803 11.3906 27.2803H17.8192C18.3715 27.2803 18.8192 27.728 18.8192 28.2803C18.8192 28.8326 18.3715 29.2803 17.8192 29.2803H12.3906V34.7088C12.3906 35.2611 11.9429 35.7088 11.3906 35.7088C10.8383 35.7088 10.3906 35.2611 10.3906 34.7088V28.2803Z"
        fill="#4AC2FF"
      />
      <path
        fill-rule="evenodd"
        clip-rule="evenodd"
        d="M27.6759 2.65709C24.2783 1.89936 20.7253 2.29122 17.5745 3.77119C14.4237 5.25115 11.8534 7.73543 10.2672 10.8341C8.68103 13.9328 8.16862 17.4704 8.81043 20.8918C8.91226 21.4347 8.55476 21.9572 8.01195 22.0591C7.46913 22.1609 6.94654 21.8034 6.84472 21.2606C6.12123 17.4037 6.69885 13.4158 8.48694 9.92277C10.275 6.42972 13.1723 3.62926 16.7242 1.96094C20.276 0.292616 24.2812 -0.149127 28.1112 0.705043C31.9413 1.55921 35.3792 3.66094 37.8856 6.68036C38.2383 7.10531 38.1798 7.73577 37.7549 8.08851C37.3299 8.44126 36.6994 8.38272 36.3467 7.95777C34.1233 5.27926 31.0735 3.41482 27.6759 2.65709ZM40.4958 13.9425C41.0386 13.8407 41.5612 14.1982 41.663 14.741C42.3865 18.5978 41.8089 22.5857 40.0208 26.0788C38.2327 29.5718 35.3354 32.3723 31.7836 34.0406C28.2317 35.7089 24.2265 36.1507 20.3965 35.2965C16.5665 34.4423 13.1285 32.3406 10.6221 29.3212C10.2694 28.8962 10.3279 28.2658 10.7529 27.913C11.1778 27.5603 11.8083 27.6188 12.161 28.0438C14.3844 30.7223 17.4342 32.5867 20.8318 33.3445C24.2294 34.1022 27.7825 33.7103 30.9333 32.2304C34.0841 30.7504 36.6543 28.2661 38.2405 25.1674C39.8267 22.0688 40.3391 18.5311 39.6973 15.1097C39.5955 14.5669 39.953 14.0443 40.4958 13.9425Z"
        fill="#4AC2FF"
      />
      <path
        fill-rule="evenodd"
        clip-rule="evenodd"
        d="M37.1161 0.288086C37.6684 0.288086 38.1161 0.735801 38.1161 1.28809V7.71666C38.1161 8.26894 37.6684 8.71666 37.1161 8.71666H30.6875C30.1352 8.71666 29.6875 8.26894 29.6875 7.71666C29.6875 7.16437 30.1352 6.71666 30.6875 6.71666H36.1161V1.28809C36.1161 0.735801 36.5638 0.288086 37.1161 0.288086Z"
        fill="#4AC2FF"
      />
      <path
        fill-rule="evenodd"
        clip-rule="evenodd"
        d="M19.0352 11.9971C19.0352 11.4448 19.4829 10.9971 20.0352 10.9971H28.4727C29.025 10.9971 29.4727 11.4448 29.4727 11.9971C29.4727 12.5494 29.025 12.9971 28.4727 12.9971H25.8511C26.2203 13.5777 26.4579 14.2333 26.5747 14.9036H28.4727C29.025 14.9036 29.4727 15.3513 29.4727 15.9036C29.4727 16.4559 29.025 16.9036 28.4727 16.9036H26.5747C26.4298 17.7348 26.0995 18.5432 25.5626 19.2141C24.8306 20.1291 23.7455 20.7444 22.3705 20.8052L26.7344 25.3684C27.1162 25.7676 27.102 26.4006 26.7029 26.7823C26.3038 27.164 25.6708 27.1499 25.289 26.7507L19.3125 20.5014C19.0357 20.212 18.9583 19.7854 19.1156 19.4172C19.273 19.049 19.6348 18.8102 20.0352 18.8102H22.1446C23.0064 18.8102 23.6006 18.4651 24.001 17.9647C24.237 17.6697 24.4158 17.3058 24.5274 16.9036H20.0352C19.4829 16.9036 19.0352 16.4559 19.0352 15.9036C19.0352 15.3513 19.4829 14.9036 20.0352 14.9036H24.5274C24.4158 14.5015 24.237 14.1376 24.001 13.8426C23.6006 13.3422 23.0064 12.9971 22.1446 12.9971H20.0352C19.4829 12.9971 19.0352 12.5494 19.0352 11.9971Z"
        fill="#4AC2FF"
      />
    </g>
    <defs>
      <clipPath id="clip0_233_10166">
        <rect width="36" height="36" fill="white" transform="translate(6.25391)" />
      </clipPath>
    </defs>
  </svg>
);

//environment variables
export const protocol = process.env.REACT_APP_PROTOCOL;
export const domain = process.env.REACT_APP_WS_URL;
export const port = process.env.REACT_APP_PORT;
export const reactport = process.env.REACT_APP_REACTPORT;
export const reactcontext = process.env.REACT_APP_REACTCONTEXT;
export const apicontext = process.env.REACT_APP_APICONTEXT;
export const cookiedomain = process.env.REACT_APP_COOKIEDOMAIN;
export const restbaseurl = process.env.REACT_APP_RESTBASEURL;
export const cdnurl = process.env.REACT_APP_CDNURL;
export const uploadsContext = process.env.REACT_APP_UPLOADSCONTEXT;
export const contexPath = process.env.REACT_APP_CONTEXPATH;
export const timerRefreshInterval = 120000;
export const CREATION_BUSINESS_ID = process.env.REACT_APP_CREATION_BUSINESS_ID;
/**
 * Dropdown Options
 */
export const timelineDropdownOptions = [
  {
    text: 'Last 7 days',
    value: 'last7days',
  },
  {
    text: 'Current Month',
    value: 'monthToDate',
  },
  {
    text: 'Last 30 days',
    value: 'last30days',
  },
  {
    text: 'Current Quarter',
    value: 'quarterToDate',
  },
  {
    text: 'Current Year',
    value: 'yearToDate',
  },
  {
    text: 'Custom Range',
    value: 'customDate',
  },
];

/**
 * Sorting Options
 */
export const SortOptions = [
  {
    title: 'Due Date',
    options: [
      { text: 'Oldest to Latest', value: 'earliestToLatest' },
      { text: 'Latest to Oldest', value: 'latestToEarliest' },
    ],
  },
  {
    title: 'Amount',
    options: [
      { text: 'Low to High', value: 'lowToHigh' },
      { text: 'High to Low', value: 'highToLow' },
    ],
  },
];

/**
 * Month Names List
 */
export const monthNames = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];

export const INDIAN_STATES = [
  { code: 'AN', name: 'Andaman and Nicobar Islands', code_num: '35' },
  { code: 'AP', name: 'Andhra Pradesh', code_num: '37' },
  { code: 'AR', name: 'Arunachal Pradesh', code_num: '12' },
  { code: 'AS', name: 'Assam', code_num: '18' },
  { code: 'BR', name: 'Bihar', code_num: '10' },
  { code: 'CH', name: 'Chandigarh', code_num: '04' },
  { code: 'CG', name: 'Chhattisgarh', code_num: '22' },
  { code: 'DN', name: 'Dadra and Nagar Haveli and Daman and Diu', code_num: '26' },
  { code: 'DL', name: 'Delhi', code_num: '07' },
  { code: 'GA', name: 'Goa', code_num: '30' },
  { code: 'GJ', name: 'Gujarat', code_num: '24' },
  { code: 'HR', name: 'Haryana', code_num: '06' },
  { code: 'HP', name: 'Himachal Pradesh', code_num: '02' },
  { code: 'JK', name: 'Jammu and Kashmir', code_num: '01' },
  { code: 'JH', name: 'Jharkhand', code_num: '20' },
  { code: 'KA', name: 'Karnataka', code_num: '29' },
  { code: 'KL', name: 'Kerala', code_num: '32' },
  { code: 'LD', name: 'Lakshadweep', code_num: '31' },
  { code: 'MP', name: 'Madhya Pradesh', code_num: '23' },
  { code: 'MH', name: 'Maharashtra', code_num: '27' },
  { code: 'MN', name: 'Manipur', code_num: '14' },
  { code: 'ML', name: 'Meghalaya', code_num: '17' },
  { code: 'MZ', name: 'Mizoram', code_num: '15' },
  { code: 'NL', name: 'Nagaland', code_num: '13' },
  { code: 'OR', name: 'Odisha', code_num: '21' },
  { code: 'PY', name: 'Puducherry', code_num: '34' },
  { code: 'PB', name: 'Punjab', code_num: '03' },
  { code: 'RJ', name: 'Rajasthan', code_num: '08' },
  { code: 'SK', name: 'Sikkim', code_num: '11' },
  { code: 'TN', name: 'Tamil Nadu', code_num: '33' },
  { code: 'TS', name: 'Telangana', code_num: '36' },
  { code: 'TR', name: 'Tripura', code_num: '16' },
  { code: 'UP', name: 'Uttar Pradesh', code_num: '09' },
  { code: 'UK', name: 'Uttarakhand', code_num: '05' },
  { code: 'WB', name: 'West Bengal', code_num: '19' },
];

export const defaultToastOptions = {
  autoClose: 2000,
  hideProgressBar: true,
  closeButton: true,
  isLoading: false,
};

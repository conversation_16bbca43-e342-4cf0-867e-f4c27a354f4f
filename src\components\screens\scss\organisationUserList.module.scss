@import '../../../assets//scss/main.scss';

.bussinessListScreen {
  height: 89vh;
  padding: 2.5em 2em;
  display: flex;
  gap: 2em;
  @include for_media(mobileScreen) {
    padding: 1em;
  }
  .navigationWrapper {
    width: 20%;
    @include for_media(mobileScreen) {
      display: none;
    }
  }
  .rightContentWrapper {
    width: 80%;
    background-color: $white;
    border-radius: 10px;
    box-shadow: 0 0 1px 1px $borderColor;
    @include for_media(mobileScreen) {
      width: 100%;
      padding: 0;
    }
  }

  .tableWrapper {
    padding: 1em 0;
    table {
      // border-spacing: 0 15px !important;
      thead {
        background-color: #f5f5f5 !important;
        height: 5em !important;
      }
      th {
        color: #717680 !important;
        // text-align: center !important;
        padding: 1em !important;
        font-size: 1.2rem !important;
      }
      td {
        padding: 1em !important;
        font-size: 1.2rem;
        // text-align: center !important;
        color: #717680;
      }
      .subjectheaderRow {
        text-align: start !important;
        padding: 1em !important;
      }
      .subjectRow {
        text-align: start !important;
        display: flex;
        align-items: center;
        gap: 1em;
        padding: 1em !important;
        font-weight: 900;
        font-size: 1rem !important;
        .logo {
          border: 2px solid $borderColor;
          border-radius: 10px;
          width: 45px;
          height: 45px;
          display: flex;
          justify-content: center;
          align-items: center;
          background-color: #f5f5f5;
          svg {
            width: 25px;
            height: 25px;
          }
          img {
            width: 100%;
            height: 100%;
            border-radius: 10px;
          }
          svg,
          path {
            fill: #0a0d12;
          }
        }
      }
      tbody {
        tr {
          background-color: $white;
          border-radius: 20px !important;
        }
      }
    }
  }
}

.ticketList {
  display: flex;
  flex-wrap: wrap;
  gap: 2em;
  // justify-content: center;
  padding-bottom: 5em;
  @include for_media(mobileScreen) {
    width: 100%;
    flex-direction: column;
    justify-content: center;
    align-items: center;
  }
  .ticketCard {
    margin: 0;
    background-color: #fdfdfd;
    width: 30em;
    border-radius: 15px;
    // height: 18em;
    @include for_media(mobileScreen) {
      // height: 19em;
    }
    overflow: hidden;
    .leftContent {
      width: 20%;
      svg,
      img {
        width: 45px;
        height: 45px;
      }
    }
    .rightContent {
      width: 90%;
      height: 12em;
      @include for_media(mobileScreen) {
        height: auto;
      }
    }
    p {
      white-space: normal;
      overflow: hidden;
      text-overflow: ellipsis;
      width: 100%;
      @include for_media(mobileScreen) {
        width: 97%;
      }
    }
    .ticketContent {
      display: flex;
    }
  }
}

.desc {
  color: #717680;
}

.headerPart {
  padding: 2em;
  display: flex;
  justify-content: space-between;
  .btnWrapper {
    display: flex;
    gap: 1em;
    .downloadBtn {
      display: flex;
      gap: 1em;
      align-items: center;
      width: 10em;
      height: 2.5em;
      border-radius: 35px;
      border: 1px solid #d5d7da;
      justify-content: center;
      color: #293056;
      font-size: 1.4em;
      svg {
        width: 25px;
        height: 25px;
      }
    }
    .addBtn {
      display: flex;
      gap: 1em;
      cursor: pointer;
      align-items: center;
      width: 10em;
      height: 2.5em;
      border-radius: 35px;
      border: 1px solid #d5d7da;
      justify-content: center;
      color: $white;
      background-color: #4e5ba6;
      font-size: 1.4em;
      svg {
        width: 25px;
        height: 25px;
      }
    }
  }
}

.searchWrapper {
  padding: 1em 2em;
  display: flex;
  gap: 2em;
}

.searchInput {
  width: 35% !important;
  height: 3.5em !important;
  // border: 1px solid #E9EAEB;
  border-radius: 10px;
  box-shadow: 0 0 1px 1px #e9eaeb;
  input {
    background-color: #fdfdfd !important;
    color: #717680 !important;
    // border: 1px solid #E9EAEB;
    border-radius: 10px !important;
  }
}
.statusDropdown {
  height: 3.1em !important;
  border-radius: 10px;
  min-width: 150px;
  box-shadow: 0 0 1px 1px #e9eaeb;
  background-color: #fafafa !important;
  font-size: 1.1rem !important;
  padding: 0 1em !important;
  svg {
    path {
      fill: #0a0d12;
    }
  }
  display: flex !important;
  align-items: center;
  justify-content: space-evenly;
}

.roleDropdown {
  justify-content: space-between;
}

.checkbox {
  label::before {
    height: 25px !important;
    width: 25px !important;
  }
  label::after {
    top: 4px !important;
    width: 25px !important;
    height: 19px !important;
    color: $white !important;
  }
}

.contactInfo {
  display: flex;
  align-items: center;
  gap: 2em;
  p {
    margin: 0;
  }
  .iconWrapper {
    display: flex;
    gap: 1em;
    span {
      width: 40px;
      height: 40px;
      background-color: #eaecf5;
      border-radius: 100%;
      // padding: 1em;
      display: flex;
      justify-content: center;
      align-items: center;
      svg {
        width: 27px !important;
        height: 27px !important;
      }
      cursor: pointer;
    }
  }
}

.businessName {
  p {
    font-size: 1.2rem;
    font-weight: 400 !important;
    color: #0a0d12;
  }
}

.modalContent {
  min-height: 62vh;
  background-color: $white;
  border-radius: 25px !important;
  h5 {
    font-size: 1.3em !important;
    padding: 1em 1.6em;
    color: black;
    margin: 0;
  }

  .formContainer {
    margin: 0 0 2em 0;
    margin: 0 2em 2em 2em;
    height: 500px;
    overflow-y: auto;
    .formField {
      margin: 2em 0;
    }
    input,
    textarea {
      background-color: #f5f5f5 !important;
      color: $black !important;
      box-shadow: 0 0 1px 1px #e9eaeb;
      border-radius: 13px !important;
      border: none !important;
    }
    input {
      height: 3.5em;
    }
    textarea {
      height: 7em;
    }
    label {
      font-size: 1.1em !important;
      margin: 0.5em 0 !important;
      font-weight: 400 !important;
    }
  }
}

.formWrapper {
  min-height: 54vh;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.buttonWrapper {
  display: flex;
  width: 100%;
  justify-content: center;
  padding: 2em;
  gap: 2em;
  border-top: 1px solid #717680;
  .nextBtn {
    background-color: #4e5ba6;
    color: $white !important;
    padding: 0.9em 2em !important;
    border-radius: 35px !important;
    font-size: 1.2em !important;
  }
  .cancel {
    background-color: #eaecf5 !important;
    border: 1px solid #9ea5d1;
    border-radius: 35px !important;
    font-size: 1.2em !important;
    padding: 0.9em 2em !important;
  }
}
.userName {
  font-size: 1.2em !important;
  color: $black;
}
.userInfo {
  span {
    padding: 0.35em 1em;
    border-radius: 20px;
  }
  .activeStatus {
    background-color: #e3f8d9 !important;
    color: #2e7a31;
  }
  .inActive {
    background-color: #f5f5f5 !important;
    color: #717680;
  }
}

.dotContainer {
  display: flex;
  justify-content: flex-end;
  width: 100%;
  svg {
    cursor: pointer;
  }
}

.popup {
  border-radius: 15px !important;
  padding: 1em 2em !important;
  .popupList {
    .popupItem {
      display: flex;
      gap: 1em;
      margin: 1em 0;
      cursor: pointer;
    }
  }
}

.sendPasswordBtn {
  padding: 1em 2em;
  p {
    margin: 0;
    background-color: #4e5ba6;
    height: 2.5em;
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 35px;
    font-weight: 900;
  }
}

.autoHeight {
  min-height: auto !important;
  .formWrapper {
    min-height: auto !important;
  }
}

.orline {
  padding: 1em 2em;
  svg {
    width: 100%;
  }
}

.imgWrapper {
  padding: 1em 2em;
  display: flex;
  justify-content: center;
}
.descContent {
  padding: 0 2em;
  text-align: center;
  margin: 1em 0;
  h5 {
    margin: 0.5em !important;
    padding: 0 !important ;
  }
  p {
    color: #717680;
  }
}

.grteenBtn {
  background-color: #2e7a31 !important;
}

.redBtn {
  background-color: #f04438 !important;
  background-color: #f04438 !important;
  border-radius: 35px !important;
  color: #ffff !important;
}

.negativeText {
  color: #f04438 !important;
  -webkit-text-stroke: 0.3px;
  text-decoration: underline;
}

.activeStatus {
  background-color: #e3f8d9 !important;
  color: #2e7a31;
  padding: 0.35em 1em;
  border-radius: 20px;
}
.inActive {
  background-color: #f5f5f5 !important;
  color: #717680;
  padding: 0.35em 1em;
  border-radius: 20px;
}

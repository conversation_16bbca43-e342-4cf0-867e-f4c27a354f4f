import React, { useState, useEffect } from 'react';
import GlobalService from '../../../../services/GlobalServices';
import { resturls } from '../../../../utils/apiurls';
import style from './scss/cashFlowContent.module.scss';
import CashFlowChart from '../../../../global/Charts/CashFlowChart';
import { cashFlow, receiveMoney, sendMoney, totalCost } from '../../../../global/Icons';
import { Loader } from 'semantic-ui-react';
import { useAuth } from '../../../../../contexts/AuthContext';
import ls from 'local-storage';
import { formatAmount } from '../../../../utils/dateUtils';

const CashFlowContent = () => {
  const { isMobileScreen } = useAuth();
  const [cashFlowInfo, setCashFlowInfo] = useState({});
  const [isLoading, setIsloading] = useState(false);
  const [overViewList, setOverViewList] = useState([
    {
      text: "Today's Income",
      amount: '0',
      icon: receiveMoney(),
    },
    {
      text: "Today's Expenses",
      amount: '0',
      icon: sendMoney(),
    },
    {
      text: 'Total Balance',
      amount: '0',
      icon: totalCost(),
      progressBar: null,
      cash: '0',
      bank: '0',
    },
  ]);
  const [overviewLoading, setOverviewLoading] = useState({
    income: false,
    expense: false,
    total: false,
  });
  const business_id = ls.get('selectedBusiness')?.business_id;

  const fetchCashFlowData = async () => {
    const cachedData = ls.get('cashFlowData');

    if (cachedData) {
      updateCashFlowState(cachedData);
    } else {
      setIsloading(true);
      setOverviewLoading((prevState) => ({ ...prevState, total: true }));

      GlobalService.generalSelect(
        async (response) => {
          if (response) {
            ls.set('cashFlowData', response);
            updateCashFlowState(response);
          }
        },
        `${resturls.getProjectedCashFlow}?business_id=${business_id}`,
        {},
        'GET'
      );
    }
  };

  const updateCashFlowState = (response) => {
    const { current_cash_balance, current_bank_balance } = response;
    const totalBalanceAmount = (parseFloat(current_cash_balance) + parseFloat(current_bank_balance)).toFixed(2);
    const totalBalance = parseFloat(totalBalanceAmount);
    const cashBalance = parseFloat(current_cash_balance);
    const progressBarValue = totalBalance === 0 ? 0 : ((cashBalance / totalBalance) * 100).toFixed(2);

    setOverViewList((prevState) =>
      prevState?.map((item) =>
        item.text === 'Total Balance'
          ? {
              ...item,
              amount: totalBalanceAmount,
              cash: current_cash_balance,
              bank: current_bank_balance,
              progressBar: progressBarValue,
            }
          : item
      )
    );
    setCashFlowInfo(response);
    setIsloading(false);
    setOverviewLoading((prevState) => ({ ...prevState, total: false }));
  };

  const fetchTotalIncome = async () => {
    const cachedIncome = ls.get('totalIncome');

    if (cachedIncome) {
      updateIncomeState(cachedIncome);
    } else {
      fetchAndStoreTotalIncome();
    }
  };

  const fetchAndStoreTotalIncome = () => {
    setOverviewLoading((prevState) => ({ ...prevState, income: true }));

    const queryParams = new URLSearchParams();
    queryParams.append('granularity', 'day');
    queryParams.append('business_id', business_id);

    GlobalService.generalSelect(
      async (response) => {
        if (response?.data) {
          ls.set('totalIncome', response.data);
          updateIncomeState(response.data);
        }
      },
      `${resturls.obtainRevenueReportDetails}?${queryParams}`,
      {},
      'GET'
    );
  };

  const updateIncomeState = (response) => {
    const updatedIncome = response[0]?.amount;
    setOverViewList((prevState) =>
      prevState?.map((item) => (item.text === "Today's Income" ? { ...item, amount: updatedIncome } : item))
    );
    setOverviewLoading((prevState) => ({ ...prevState, income: false }));
  };

  const fetchTotalExpense = async () => {
    const cachedExpense = ls.get('totalExpense');

    if (cachedExpense) {
      updateExpenseState(cachedExpense);
    } else {
      fetchAndStoreTotalExpense();
    }
  };

  const fetchAndStoreTotalExpense = () => {
    setOverviewLoading((prevState) => ({ ...prevState, expense: true }));

    const queryParams = new URLSearchParams();
    queryParams.append('granularity', 'day');
    queryParams.append('business_id', business_id);

    GlobalService.generalSelect(
      async (response) => {
        if (response?.data) {
          ls.set('totalExpense', response.data);
          updateExpenseState(response.data);
        }
      },
      `${resturls.getCostReportData}?${queryParams}`,
      {},
      'GET'
    );
  };

  const updateExpenseState = (response) => {
    const updatedExpense = response[0]?.amount;
    setOverViewList((prevState) =>
      prevState?.map((item) => (item.text === "Today's Expenses" ? { ...item, amount: updatedExpense } : item))
    );
    setOverviewLoading((prevState) => ({ ...prevState, expense: false }));
  };

  // Function to clear all local storage values when needed

  useEffect(() => {
    fetchCashFlowData();
    fetchTotalIncome();
    fetchTotalExpense();
  }, []);

  const today = new Date();
  const sevenDaysAgo = new Date();
  sevenDaysAgo.setDate(today.getDate() - 7);

  // Filter last 7 days data
  let last7DaysData = cashFlowInfo?.data?.filter((item) => {
    const itemDate = new Date(item.label);
    return !isNaN(itemDate) && itemDate >= sevenDaysAgo && itemDate <= today;
  });

  // If not enough data, get the last 7 records instead
  if (last7DaysData?.length < 7) {
    last7DaysData = cashFlowInfo?.data?.slice(-7);
  }

  // Extract filtered data
  const receivables = last7DaysData?.map((item) => item?.account_receivable);
  const payables = last7DaysData?.map((item) => item?.account_payable);
  const closingBalance = last7DaysData?.map((item) => item?.closing_balance);
  const categories = last7DaysData?.map((item) => item?.label);

  const OverViewCard = ({ info }) => (
    <div className={style.OverViewCard}>
      <div className={style.titleContent}>
        <div className={style.leftContent}>
          {info?.icon}
          <p>{info?.text}</p>
        </div>
        <p className={style.amount}>{formatAmount(info?.amount || 0)}</p>
      </div>
      {info?.progressBar !== undefined && (
        <>
          {info?.progressBar === null ? (
            <div className={style.progressBarLoader}>
              <Loader active />
            </div>
          ) : (
            <>
              <div className={style.progressContainer}>
                <div className={style.progressBar} style={{ width: `${info.progressBar}%` }}></div>
              </div>
              <div className={style.cashInfoContainer}>
                <div className={style.cashInfo}>
                  <div>
                    <span />
                    <p>Cash</p>
                  </div>
                  <p> {formatAmount(info?.cash || 0)}</p>
                </div>
                <div className={style.cashInfo}>
                  <div className={style.bankFlow}>
                    <span />
                    <p>Bank</p>
                  </div>
                  <p> {formatAmount(info?.bank || 0)}</p>
                </div>
              </div>
            </>
          )}
        </>
      )}
    </div>
  );

  const allFalse = Object.values(overviewLoading).every((value) => value === false);

  return (
    <>
      <h5 className={style.subTitle}>Overview</h5>
      <div className={`${style.cashFlowContainer} ${style.dashBoardView}`}>
        <div className={style.leftCardContent}>
          {allFalse ? (
            overViewList?.map((list) => {
              if (isMobileScreen && list?.text === 'Total Balance') return;
              return <OverViewCard info={list} />;
            })
          ) : (
            <div className={style.loaderWrapper2}>
              <Loader active />
            </div>
          )}
        </div>
        <div className={`${style.cashFlowContent} ${style.rightCardContent}`}>
          <div className={style.cashFlowHeader}>
            <div className={style.cashFlowTitleSection}>
              {cashFlow()}
              <div>
                <p>Cash Flow</p>
                <span>Last 7 Days</span>
              </div>
            </div>
            <p>{formatAmount(overViewList[2]?.amount || 0)}</p>
          </div>
          {isLoading ? (
            <div className={style.loaderWrapper}>
              <Loader active={true} />
            </div>
          ) : (
            <CashFlowChart
              receivables={receivables || []}
              payables={payables || []}
              closingBalance={closingBalance || []}
              categories={categories || []}
              isMobileScreen={isMobileScreen}
            />
          )}

          {isMobileScreen && (
            <div className={style.balanceInfo}>
              <div>
                <p>Current Bank Balance</p>
                <p className={style.amount}>{formatAmount(overViewList[2]?.bank || 0)}</p>
              </div>
              <div>
                <p>Current Cash Balance</p>
                <p className={style.amount}>{formatAmount(overViewList[2]?.cash || 0)}</p>
              </div>
            </div>
          )}
        </div>
      </div>
    </>
  );
};

export default CashFlowContent;

import React, { useEffect, useState, useRef, useCallback } from 'react';
import style from './scss/bussinessDetailed.module.scss';
import Header from '../global/Header';
import {
  backIcon,
  copyIcon,
  deleteIcon,
  DropdownIcon,
  editIcon,
  orgIcon,
  phoneIcon,
  mailIcon,
  threeDotIcon,
  closeIcon,
  removeUser,
} from '../global/Icons';
import { Image, Dropdown, Table, Pagination, Popup, Button, Modal, Form, Icon, Loader } from 'semantic-ui-react';
import sampleImg from '../../assets/Images/sampleImg.png';
import { useFormik } from 'formik';
import * as Yup from 'yup';
import GlobalService from '../services/GlobalServices';
import { resturls } from '../utils/apiurls';
import { useParams, useNavigate } from 'react-router-dom';
import infoIcon from '../../assets/Images/infoIcon.png';
import { AiOutlineEye, AiOutlineEyeInvisible } from 'react-icons/ai';

const BusinessDetailed = () => {
  const [activeMenu, setActiveMenu] = useState('detail');
  const [activeModal, setActiveModal] = useState();
  const [selectedUsers, setSelectedUsers] = useState([]);
  const [gstCopied, setGstCopied] = useState(false);
  const [emailCopied, setEmailCopied] = useState(false);
  const [isSmtpEmailCopied, setIsSmtpEmailCopied] = useState(false);
  const [filesList, setFilesList] = useState([]);
  const [memberList, setmemberList] = useState([]);
  const [memberCount, setMemberCount] = useState();
  const [activePopup, setActivePopup] = useState(false);
  const [FetchUserList, setFetchUserList] = useState([]);
  const [page, setPage] = useState(1);
  const [hasMore, setHasMore] = useState(true);
  const [isLoading, setIsLoading] = useState(false);
  const [businessData, setBusinessData] = useState({});
  const [paginationInfo, setPaginationInfo] = useState();
  const [activePage, setActivePage] = useState(1);
  const [step, setStep] = useState({
    step1: true,
    step2: false,
    active: 'step1',
  });
  const [BuisnessSuperUserList, setBuisnessSuperUserList] = useState([]);
  const observer = useRef();
  const [dropdownItems, setDropdownItems] = useState([]);
  const [nextPageUrl, setNextPageUrl] = useState(null);
  const [isFetching, setIsFetching] = useState(false);
  const dropdownRef = useRef(null);
  const [selectEmailConf, setSelectedEmailConf] = useState([]);
  const [emailConfOptionsAll, setEmailConfOptionsAll] = useState([]);

  const [showPassword, setShowPassword] = useState(false);

  const togglePasswordVisibility = () => {
    setShowPassword(!showPassword);
  };

  const [showPasswordInt, setShowPasswordInt] = useState(false);

  const togglePasswordVisibilityInt = () => {
    setShowPasswordInt((prevShowPassword) => !prevShowPassword);
  };

  //buisnesstype dropdown_code
  const fetchBusinessTypes = useCallback(
    (url) => {
      if (isFetching) {
        return;
      }

      setIsFetching(true);

      GlobalService.generalSelect(
        (respData) => {
          const { results, next } = respData;

          if (!results || results.length === 0) {
            console.error('Invalid or empty response data', { respData });
            setIsFetching(false);
            return;
          }

          const newOptions = results.map((item) => ({
            key: item.id,
            value: item.id,
            text: item.name,
          }));

          setDropdownItems((prevItems) => {
            const existingKeys = new Set(prevItems.map((item) => item.key));
            const uniqueNewOptions = newOptions.filter((item) => !existingKeys.has(item.key));

            return [...prevItems, ...uniqueNewOptions];
          });
          setNextPageUrl(next);
          setIsFetching(false);

          console.log('Fetch Complete', {
            itemsLoaded: newOptions.length,
            nextPageAvailable: !!next,
          });
        },
        url,
        {},
        'GET'
      );
    },
    [isFetching]
  );

  useEffect(() => {
    if (dropdownItems.length === 0) {
      fetchBusinessTypes(resturls.fetchBusinessType);
    }
  }, [dropdownItems, fetchBusinessTypes, resturls.fetchBusinessType]);

  const loadMoreItems = () => {
    if (nextPageUrl && !isFetching) {
      fetchBusinessTypes(nextPageUrl);
    }
  };

  const dropdownOptions = [
    ...dropdownItems,
    ...(nextPageUrl && !isFetching
      ? [
          {
            key: 'load_more',
            text: 'Load More',
            value: 'load_more',
            disabled: isFetching,
            className: 'load-more-option',
          },
        ]
      : []),
  ];

  const handleDropdownChange = (e, { value }) => {
    if (value === 'load_more') {
      loadMoreItems();
      if (dropdownRef.current) {
        dropdownRef.current.setState({ open: true });
      }
      return;
    }
    editFormik.setFieldValue('business_type', value);
  };

  const BuisnessId = useParams();

  const fetchEmailConf = () => {
    GlobalService.generalSelect(
      (respdata) => {
        const { results } = respdata;
        setSelectedEmailConf(results);

        formik.setFieldValue('emailConfig', results[0].id);
      },
      `${resturls.emailserver}?business_id=${BuisnessId.BuisnessId}&is_active=true`,
      {},
      'GET'
    );
  };

  const fetchEmailConfAll = () => {
    GlobalService.generalSelect(
      (respdata) => {
        const { results } = respdata;
        const options = results.map((conf) => ({
          key: conf.id,
          text: conf.server_name,
          value: conf.id,
        }));
        // Explicitly set Formik values using results[0]
        formik.setFieldValue('server_name', results[0].server_name);
        formik.setFieldValue('email_host', results[0].email_host);
        formik.setFieldValue('smtp_port', results[0].smtp_port);
        formik.setFieldValue('imap_host', results[0].imap_host);
        formik.setFieldValue('imap_port', results[0].imap_port);
        formik.setFieldValue('email_host_user', results[0].email_host_user);
        formik.setFieldValue('email_use_tls', results[0].email_use_tls);
        formik.setFieldValue('email_use_ssl', results[0].email_use_ssl);
        formik.setFieldValue('email_host_password', results[0].email_host_password);
        formik.setFieldValue('business', results[0].business.id);
        formik.setFieldValue('is_active', results[0].is_active);
        const encryption = results[0].email_use_ssl ? 'ssl' : results[0].email_use_tls ? 'tls' : 'none';
        formik.setFieldValue('email_encryption', encryption);

        setEmailConfOptionsAll(options);
      },
      `${resturls.emailserver}?business_id=${BuisnessId.BuisnessId}`,
      {},
      'GET'
    );
  };
  useEffect(() => {
    fetchEmailConf();
    fetchEmailConfAll();
  }, []);

  console.log(`${resturls.emailserver}?search=${BuisnessId}`, selectEmailConf, 'email_conf');

  useEffect(() => {
    GlobalService.generalSelect(
      (respdata) => {
        setBuisnessSuperUserList(respdata.data);
      },
      `${resturls.obtainCategoryWiseUser}?user_type=business_superusers`,
      {},
      'GET'
    );
  }, []);

  const Superuseroptions =
    BuisnessSuperUserList?.length > 0 &&
    BuisnessSuperUserList?.map((user) => ({
      key: user.user_id,
      value: Number(user.user_id),
      text: user.full_name,
    }));

  const copyToClipboard = (text, setCopiedState) => {
    // Check if the Clipboard API is available
    if (navigator.clipboard && window.isSecureContext) {
      navigator.clipboard
        .writeText(text)
        .then(() => {
          setCopiedState(true);
          setTimeout(() => setCopiedState(false), 2000);
        })
        .catch((err) => {
          console.error('Failed to copy via Clipboard API:', err);
        });
    } else {
      // Fallback for HTTP or older browsers
      const textArea = document.createElement('textarea');
      textArea.value = text;
      textArea.style.position = 'fixed'; // Prevent scrolling
      textArea.style.left = '-9999px'; // Hide element
      document.body.appendChild(textArea);
      textArea.focus();
      textArea.select();
      try {
        document.execCommand('copy');
        setCopiedState(true);
        setTimeout(() => setCopiedState(false), 2000);
      } catch (err) {
        console.error('Fallback: Unable to copy', err);
      }
      document.body.removeChild(textArea);
    }
  };

  const tickIcon = () => (
    <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        d="M13.3333 4L6 11.3333L2.66667 8"
        stroke="#10B981"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );

  const formatDate = (isoDateString) => {
    const date = new Date(isoDateString);
    return new Intl.DateTimeFormat('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    }).format(date);
  };

  const FetchFilesSpecificBuisness = () => {
    GlobalService.generalSelect(
      (respdata) => {
        setFilesList(respdata.results);
      },
      `${resturls.file}?business_id=${BuisnessId.BuisnessId}`,
      'GET'
    );

    setmemberList([]);
  };

  const fetchallbuisnessUsers = () => {
    GlobalService.generalSelect(
      (respdata) => {
        setFetchUserList(respdata.data);
        // setMemberCount(respdata.count);
      },
      `${resturls.obtainCategoryWiseUser}?user_type=business_users`,
      {},
      'GET'
    );
    // setFilesList([]);
  };

  const GetMembers = (page = 1) => {
    if (BuisnessId.BuisnessId) {
      if (isLoading) return; // Prevent multiple API calls at the same time
      setIsLoading(true);

      GlobalService.generalSelect(
        (respdata) => {
          setMemberCount(respdata.count);
          setmemberList((prev) => {
            const uniqueResults = respdata.results.filter(
              (newItem) => !prev.some((existingItem) => existingItem.id === newItem.id)
            );
            return [...prev, ...uniqueResults];
          });
          setPaginationInfo(respdata);
          setHasMore(!!respdata.next);
          setIsLoading(false);
        },
        `${resturls.getTeamList}${BuisnessId.BuisnessId}?page=${page}`,
        {},
        'GET'
      );
    }
  };

  const deleteBuisness = () => {
    GlobalService.generalSelect(
      (respdata) => {
        setBusinessData(respdata);
        navigate('/businessList');
      },
      `${resturls.createBusiness}${BuisnessId.BuisnessId}/`,
      {},
      'DELETE'
    );
  };

  useEffect(() => {
    if (activeMenu === 'files') {
      setmemberList([]);
      FetchFilesSpecificBuisness();
    }
    if (activeMenu === 'member') {
      setFilesList([]);

      GetMembers(page);
    }
  }, [activeMenu]);

  useEffect(() => {
    if (!BuisnessId?.BuisnessId) return;
    const formatPhoneNumber = (phone) => {
      if (phone.startsWith('91')) {
        return `+${phone}`;
      } else if (!phone.startsWith('91')) {
        return `+91${phone}`;
      }
      return phone;
    };
    GlobalService.generalSelect(
      (respdata) => {
        setBusinessData(respdata);
        const obj = {
          name: respdata?.name,
          gst: respdata?.gst,
          address: respdata?.address,
          google_map_link: respdata?.google_map_link,
          business_type: respdata?.business_type?.id,
          phone: formatPhoneNumber(respdata?.phone),
          email: respdata?.email,
          business_superuser_id: respdata?.business_superuser?.user_id,
          primary_contact_name: respdata?.primary_contact_name,
        };
        // setEditFormData(obj);

        editFormik.setValues(obj);
      },
      `${resturls.createBusiness}${BuisnessId.BuisnessId}/`,
      {},
      'GET'
    );
    FetchFilesSpecificBuisness();
    GetMembers(page);
  }, [BuisnessId?.BuisnessId]);

  useEffect(() => {
    fetchallbuisnessUsers();
  }, [businessData]);

  const options = FetchUserList.map((user) => ({
    key: user.full_name,
    value: user.user_id,
    text: `${user.full_name} - ${user?.email}`,
  }));

  const navigate = useNavigate();

  const goBack = () => {
    navigate(-1);
  };

  const handleChange = (e, { value }) => {
    setSelectedUsers(value);
    formik.setFieldValue('users', value);
  };
  const formik = useFormik({
    initialValues: {
      server: '',
      email: '',
      password: '',
    },
    // // validationSchema,
    onSubmit: (values) => {},
  });
  const validationSchema = Yup.object({
    name: Yup.string().required('Name is required').max(50, 'Name must be 50 characters or less'),

    gst: Yup.string()
      .required('GST number is required')
      .matches(/^[A-Za-z0-9]{15}$/, 'GST number must be exactly 15 characters'),

    email: Yup.string().required('Email is required').email('Invalid email address'),

    phone: Yup.string()
      .required('Phone number is required')
      .matches(
        /^\+?[1-9]\d{1,3}[\s-]?\(?\d{1,4}?\)?[\s-]?\d{1,4}[\s-]?\d{1,4}[\s-]?\d{1,4}$/,
        'Enter a valid phone number'
      )
      .test('is-valid-phone', 'Phone number must be valid', (value) => {
        const phoneNumber = value; // Remove non-digit and non-plus characters

        // Case 1: If the number has a country code (+91) and 12 digits total
        if (phoneNumber.startsWith('+') && phoneNumber.length === 13) {
          // Ensure the number starts with a valid digit (6-9) after the country code
          return /^[6-9]/.test(phoneNumber.slice(3));
        }

        // Case 2: If it's a 10-digit number starting with a valid digit (6-9)
        return phoneNumber.length === 10 && /^[6-9]/.test(phoneNumber);
      }),

    address: Yup.string().required('Address is required').max(200, 'Address must be 200 characters or less'),

    business_type: Yup.string().required('Business type is required'),

    primary_contact_name: Yup.string().required('Primary contact is required'),
    // .matches(/^[0-9]{10}$/, "Primary contact must be exactly 10 digits"),
  });

  const editFormik = useFormik({
    initialValues: {
      name: '',
      gst: '',
      address: '',
      google_map_link: '',
      business_type: '',
      phone: '',
      email: '',
      business_superuser_id: '',
      primary_contact_name: '',
    },
    validationSchema,
  });

  const businessDeatil = {
    logo: businessData.image ? businessData.image : null,
    name: businessData.name,
    status: businessData.is_active ? 'Active' : 'Inactive',
  };
  const renderLogo = (logo) => {
    if (logo) {
      return <Image src={logo} />;
    }
    return orgIcon();
  };

  const statusOptions = [
    {
      key: 'active',
      value: 'Active',
      text: 'Active',
      className: style.verifiedStatus,
    },
    {
      key: 'inActive',
      value: 'Inactive',
      text: 'Inactive',
      className: style.closedStatus,
    },
  ];

  const getStatusClass = (status) => {
    switch (status) {
      case 'Inactive':
        return style.closedStatus; // Class for closed status
      case 'Active':
        return style.verifiedStatus; // Class for verified status
      default:
        return ''; // No status class if undefined or empty
    }
  };

  const renderMenu = () => {
    const list = [
      { name: 'Details', key: 'detail' },
      { name: 'Members', key: 'member', count: memberCount },
      { name: 'Files', key: 'files' },
    ];
    return list?.map((info) => {
      return (
        <div
          onClickCapture={() => setActiveMenu(info?.key)}
          className={`${info?.key === activeMenu ? style.activeMenu : ''} ${style.menuItem}`}
        >
          {info?.name}
          {info?.count !== undefined && info?.count !== null && <span>{info?.count}</span>}
        </div>
      );
    });
  };

  const handleStatusUpdate = (value) => {
    GlobalService.generalSelect(
      (respdata) => {
        // navigate('/')
        GetMembers();
        setBusinessData(respdata);
      },
      `${resturls.createBusiness}${BuisnessId.BuisnessId}/`,
      { is_active: value === 'Active' ? 'true' : 'false' },
      'PATCH'
    );
  };

  const renderPopupContent = (info) => {
    const list = [
      {
        name: 'Remove from this organization',
        icon: removeUser(),
        key: 'removeUser',
      },
    ];
    return (
      <div className={style.popupList}>
        {list?.map((item) => {
          return (
            <div key={item.key} className={style.popupItem} onClickCapture={() => handleActiveModal(item?.key, info)}>
              {item?.icon} <p>{item?.name}</p>
            </div>
          );
        })}
      </div>
    );
  };

  const handleRemoveUser = (user, role) => {
    if (role === 'business_superuser') {
      alert('You cannot remove the business superuser.');
      return;
    }
    if (role === 'business_user') {
      GlobalService.generalSelect(
        (respdata) => {
          alert('User removed successfully');
          window.location.reload();
          GetMembers(page);
        },
        `${resturls.createBusiness}${BuisnessId.BuisnessId}/`,
        { remove_business_users: [user] },
        'PATCH'
      );
    } else {
      alert('You cannot remove this user.');
    }
  };

  const handleActiveModal = (key, info) => {
    switch (key) {
      case 'removeUser':
        // Handle remove member
        handleRemoveUser(info.id, info.role);
        break;
      default:
        break;
    }
    setActivePopup(false);
  };

  const details = {
    gstNumber: businessData.gst,
    email: businessData.email,
    associatedSP: null,
    createdOn: businessData.created_at,
    address: businessData.address,
    accountant: businessData.accountant?.user_full_name,
    businessSuperUser: businessData.business_superuser?.user_full_name,
    manager: businessData.manager?.user_full_name,
    business_type: businessData.business_type?.name,
    supportEmail: businessData.email,
    account_manager: businessData?.account_manager?.user_full_name,
  };

  const renderDetails = () => {
    return (
      <>
        <div className={style.detailPart}>
          <p className={style.heading}>Overview</p>
          <div className={style.detail}>
            <div>
              <label>GST No.</label>
              <p>
                {details?.gstNumber}
                <span onClick={() => copyToClipboard(details?.gstNumber, setGstCopied)} style={{ cursor: 'pointer' }}>
                  {gstCopied ? tickIcon() : copyIcon()}
                </span>
              </p>
            </div>
            <div>
              <label>Email</label>
              <p>
                {details?.email}
                <span onClick={() => copyToClipboard(details?.email, setEmailCopied)} style={{ cursor: 'pointer' }}>
                  {emailCopied ? tickIcon() : copyIcon()}
                </span>
              </p>
            </div>

            {details?.business_type && (
              <div>
                <label>Business superuser</label>
                <p>{details?.businessSuperUser}</p>
              </div>
            )}
            {details?.business_type && (
              <div>
                <label>Business Type</label>
                {console.log(details?.business_type, 'businessData')}
                <p>{details?.business_type}</p>
              </div>
            )}

            <div>
              <label>Created on</label>
              <p>{details?.createdOn ? formatDate(details.createdOn) : 'N/A'}</p>
            </div>
            <div>
              <label>Address</label>
              <p>{details?.address}</p>
            </div>
            <div>
              <label>Account manager</label>
              <p>{details?.account_manager || '-'}</p>
            </div>
          </div>
        </div>
        <div className={style.detailPart}>
          <p className={style.heading}>ArthTattva Team Associated</p>
          <div className={style.detail}>
            {details?.manager && (
              <div>
                <label>Manager</label>
                <p>{details?.manager}</p>
              </div>
            )}
            <div>
              <label>Accountant (Default)</label>
              <p>{details?.accountant}</p>
            </div>
          </div>
        </div>
        <div className={style.detailPart}>
          <p className={style.heading}>
            Security Settings <span onClickCapture={() => setActiveModal('reconfig')}>Reconfigure {'>'}</span>
          </p>
          <div className={style.detail}>
            <div>
              <label>Email Server</label>
              <p>
                {selectEmailConf[0]?.email_host || '-'}{' '}
                {selectEmailConf[0]?.email_host && (
                  <span
                    onClick={() => copyToClipboard(selectEmailConf[0]?.email_host, setIsSmtpEmailCopied)}
                    style={{ cursor: 'pointer' }}
                  >
                    {isSmtpEmailCopied ? tickIcon() : copyIcon()}
                  </span>
                )}
              </p>
            </div>
            <div>
              <label>Support Email ID </label>
              <p>{selectEmailConf[0]?.email_host_user || '-'}</p>
            </div>
            <div>
              <label>Ports:</label>
              <p>
                {selectEmailConf?.[0]?.imap_port || selectEmailConf?.[0]?.smtp_port
                  ? `IMAP: ${selectEmailConf?.[0]?.imap_port || '-'} | SMTP: ${selectEmailConf?.[0]?.smtp_port || '-'}`
                  : '-'}
              </p>
            </div>
            <div>
              <label>Password</label>
              <div style={{ display: 'flex', alignItems: 'center' }}>
                <p
                  style={{
                    margin: 0,
                    marginRight: '8px',
                    fontFamily: 'monospace',
                  }}
                >
                  {selectEmailConf[0]?.email_host_password
                    ? showPassword
                      ? selectEmailConf[0]?.email_host_password
                      : '•'.repeat(selectEmailConf[0]?.email_host_password.length)
                    : '-'}
                </p>
                {selectEmailConf[0]?.email_host_password && (
                  <button
                    type="button"
                    onClick={togglePasswordVisibility}
                    style={{
                      background: 'none',
                      border: '1px solid transparent',
                      cursor: 'pointer',
                      padding: '4px',
                      display: 'flex',
                      alignItems: 'center',
                    }}
                    aria-label={showPassword ? 'Hide password' : 'Show password'}
                  >
                    {showPassword ? (
                      <AiOutlineEyeInvisible style={{ color: 'black' }} size={20} />
                    ) : (
                      <AiOutlineEye style={{ color: 'black' }} size={20} />
                    )}
                  </button>
                )}
              </div>
            </div>
          </div>
        </div>
      </>
    );
  };

  const renderMembersList = () => {
    const itemsPerPage = 10;
    const totalPages = Math.ceil(paginationInfo?.count / itemsPerPage);
    const handlePaginationChange = (e, { activePage }) => {
      setActivePage(activePage);
      // const selected = paginationInfo?.next || paginationInfo?.previous;
      // const bisnsId = selected?.split("api/get-tickets/")[1]?.split("/")[0]?.split("?")[0];
      const endpoint = `${resturls.obtainUsersList}${BuisnessId?.BuisnessId}?page=${activePage}`;
      if (BuisnessId?.BuisnessId) {
        GlobalService.generalSelect(
          (respdata) => {
            if (respdata && respdata.results) {
              setmemberList(respdata.results);
              setPaginationInfo(respdata);
            } else {
              console.warn('No results found in response:', respdata);
              setmemberList([]);
            }
          },
          endpoint,
          {},
          'GET'
        );
      }
    };
    return (
      <>
        <div className={style.tableWrapper}>
          <Table basic="very">
            <Table.Header>
              <Table.Row>
                {/* <Table.HeaderCell><div className='customCheckBox '><Checkbox indeterminate className={`${style.checkbox}`}/></div></Table.HeaderCell> */}
                <Table.HeaderCell className={style.subjectheaderRow}>User Name</Table.HeaderCell>
                <Table.HeaderCell>User ID</Table.HeaderCell>
                <Table.HeaderCell>Role</Table.HeaderCell>
                <Table.HeaderCell>Contact Info</Table.HeaderCell>
                <Table.HeaderCell></Table.HeaderCell>
              </Table.Row>
            </Table.Header>

            <Table.Body>
              {memberList?.map((data, index) => (
                <Table.Row key={data.id} ref={memberList.length === index + 1 ? lastElementRef : null}>
                  {/* <Table.Cell>
                      <div className="customCheckBox">
                        <Checkbox className={`${style.checkbox}`} />
                      </div>
                    </Table.Cell> */}
                  <Table.Cell className={style.subjectRow}>
                    <div className={style.logo}>{renderLogo(data?.logo)}</div>
                    <div className={style.businessName}>
                      <p>{data?.full_name}</p>
                    </div>
                  </Table.Cell>
                  <Table.Cell>{data?.id}</Table.Cell>
                  <Table.Cell>{data?.role}</Table.Cell>
                  <Table.Cell>
                    <div className={style.contactInfo}>
                      <div className={style.iconWrapper}>
                        <Popup
                          inverted
                          className={style.popup}
                          trigger={<span>{phoneIcon()}</span>}
                          content={
                            <div className={style.popupContent}>
                              <p className={style.label}>Phone Number</p>
                              <p>{data?.whatsapp ? data.whatsapp : 'N/A'}</p>
                            </div>
                          }
                          position="top right"
                          hoverable
                        />
                        <Popup
                          inverted
                          className={style.popup}
                          trigger={<span>{mailIcon()}</span>}
                          content={
                            <div className={style.popupContent}>
                              <p className={style.label}>Email</p>
                              <p>{data?.email}</p>
                            </div>
                          }
                          position="top right"
                          hoverable
                        />
                      </div>
                    </div>
                  </Table.Cell>
                  <Table.Cell>
                    <div className={style.dotContainer}>
                      <Popup
                        content={renderPopupContent(data)}
                        on="click"
                        pinned
                        position="bottom right"
                        trigger={threeDotIcon()}
                        onOpen={() => setActivePopup(data?.id)}
                        onClose={() => setActivePopup(false)}
                        className={style.popup}
                        open={activePopup === data?.id}
                      />
                    </div>
                  </Table.Cell>
                </Table.Row>
              ))}
            </Table.Body>
          </Table>
        </div>
        <div className={style.paginationWrapper}>
          {paginationInfo?.count > 10 && memberList?.length > 0 && (
            <Pagination activePage={activePage} totalPages={totalPages} onPageChange={handlePaginationChange} />
          )}
        </div>
      </>
    );
  };
  const extractFileName = (url) => {
    return url.split('/').pop();
  };

  const renderFilesList = () => {
    return filesList?.length > 0 ? (
      <Table basic="very">
        <Table.Header>
          <Table.Row>
            {/* <Table.HeaderCell><div className='customCheckBox '><Checkbox indeterminate className={`${style.checkbox}`}/></div></Table.HeaderCell> */}
            <Table.HeaderCell className={style.subjectheaderRow}>File Name</Table.HeaderCell>
            <Table.HeaderCell>Ticket ID</Table.HeaderCell>
            <Table.HeaderCell>Category</Table.HeaderCell>
            <Table.HeaderCell>Uploaded by</Table.HeaderCell>
            <Table.HeaderCell>Uploaded on</Table.HeaderCell>
            <Table.HeaderCell></Table.HeaderCell>
          </Table.Row>
        </Table.Header>
        <Table.Body>
          {filesList.map((data) => (
            <Table.Row key={data.id}>
              {/* <Table.Cell><div className='customCheckBox'><Checkbox className={`${style.checkbox}`} /></div></Table.Cell> */}
              <Table.Cell className={style.subjectRow}>
                <div className={style.fileWrapper}>
                  {(() => {
                    const fileName = extractFileName(data?.file);
                    const fileExt = fileName.split('.').pop()?.toLowerCase();
                    const imageExtensions = ['png', 'jpg', 'jpeg', 'gif', 'svg', 'webp', 'bmp'];

                    // SVG Icons
                    const getSvgIcon = (type) => {
                      switch (type) {
                        case 'pdf':
                          return (
                            <svg
                              xmlns="http://www.w3.org/2000/svg"
                              width="50"
                              height="50"
                              viewBox="0 0 24 24"
                              fill="none"
                              stroke="red"
                              strokeWidth="1.5"
                              className="text-red-500"
                            >
                              <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
                              <polyline points="14 2 14 8 20 8"></polyline>
                              <line x1="12" y1="10" x2="12" y2="16"></line>
                              <line x1="9" y1="13" x2="15" y2="13"></line>
                            </svg>
                          );
                        case 'xlsx':
                        case 'xls':
                          return (
                            <svg
                              xmlns="http://www.w3.org/2000/svg"
                              width="50"
                              height="50"
                              viewBox="0 0 24 24"
                              fill="none"
                              stroke="green"
                              strokeWidth="1.5"
                              className="text-green-500"
                            >
                              <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
                              <polyline points="14 2 14 8 20 8"></polyline>
                              <path d="M10.5 15.5L8 12.5l2.5-3" />
                              <path d="M16 12.5h-6" />
                            </svg>
                          );
                        case 'doc':
                        case 'docx':
                          return (
                            <svg
                              xmlns="http://www.w3.org/2000/svg"
                              width="50"
                              height="50"
                              viewBox="0 0 24 24"
                              fill="none"
                              stroke="blue"
                              strokeWidth="1.5"
                              className="text-blue-500"
                            >
                              <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
                              <polyline points="14 2 14 8 20 8"></polyline>
                              <line x1="9" y1="14" x2="15" y2="14"></line>
                              <line x1="9" y1="17" x2="15" y2="17"></line>
                            </svg>
                          );
                        default:
                          return (
                            <svg
                              xmlns="http://www.w3.org/2000/svg"
                              width="50"
                              height="50"
                              viewBox="0 0 24 24"
                              fill="none"
                              stroke="gray"
                              strokeWidth="1.5"
                              className="text-gray-500"
                            >
                              <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
                              <polyline points="14 2 14 8 20 8"></polyline>
                              <line x1="12" y1="12" x2="12" y2="16"></line>
                              <line x1="12" y1="16" x2="12.01" y2="16"></line>
                            </svg>
                          );
                      }
                    };

                    if (imageExtensions.includes(fileExt)) {
                      return (
                        <div className={style.fileWrapper}>
                          <Image
                            className="preview_files"
                            src={data?.file || sampleImg}
                            alt={fileName}
                            width={50}
                            height={50}
                            style={{ objectFit: 'cover' }}
                          />
                          <a
                            href={data?.file}
                            target="_blank"
                            rel="noopener noreferrer"
                            download={fileName}
                            className={style.fileName}
                          >
                            {fileName}
                          </a>
                        </div>
                      );
                    }

                    return (
                      <div className={style.fileWrapper}>
                        {getSvgIcon(fileExt)}
                        <a
                          href={data?.file}
                          target="_blank"
                          rel="noopener noreferrer"
                          download={fileName}
                          className={style.fileName}
                        >
                          {fileName}
                        </a>
                      </div>
                    );
                  })()}
                </div>
              </Table.Cell>
              <Table.Cell className="centerAlign">
                <p className={style.id}>{data?.ticket_id}</p>
              </Table.Cell>
              <Table.Cell>
                <p className={data?.ticket_category ? style.category : ''}>
                  {data?.ticket_category ? data?.ticket_category : <center>-</center>}
                </p>
              </Table.Cell>
              <Table.Cell>
                <p className={style.uploadInfo}>{data?.uploaded_by?.full_name}</p>
                <p className={style.uploadRole}>{data?.uploaded_by?.role}</p>
              </Table.Cell>
              <Table.Cell>
                <p>{formatDate(data?.created_at)}</p>
              </Table.Cell>
              <Table.Cell>
                <div className={style.dotContainer}>{threeDotIcon()}</div>
              </Table.Cell>
            </Table.Row>
          ))}
        </Table.Body>
      </Table>
    ) : (
      <div
        style={{
          fontSize: '18px',
          color: '#999',
          textAlign: 'center',
          marginTop: '20px',
          padding: '20px',
          // backgroundColor: '#f8f8f8',
          borderRadius: '25px',
          // border: '1px solid #ddd',
          maxWidth: '500px',
          margin: '20px auto',
        }}
      >
        No files in this business: <strong style={{ color: '#333', fontWeight: 'bold' }}>{businessDeatil?.name}</strong>
      </div>
    );
  };

  const renderContent = () => {
    if (activeMenu === 'member') {
      return (
        <div className={style.memebersList}>
          <div className={style.inputWrapper}>
            <div className={style.addBtn} onClick={() => setActiveModal('addMember')}>
              Add
            </div>
          </div>
          <div>{renderMembersList()}</div>
        </div>
      );
    }
    if (activeMenu === 'files') {
      return (
        <div className={style.memebersList}>
          <div className={style.inputWrapper}></div>
          <div className={style.tableWrapper}>{renderFilesList()}</div>
        </div>
      );
    }
    return <div className={style.detailView}>{renderDetails()}</div>;
  };

  const sslTlsOptions = [
    { key: 'tls', text: 'TLS', value: 'tls' },
    { key: 'ssl', text: 'SSL', value: 'ssl' },
  ];

  const RenderReconfigForm = () => {
    const handleSaveDetails = () => {
      const serverInfoPayload = {
        password: formik.values.server_password,
        server_name: formik.values.server_name,
        email_host: formik.values.email_host,
        smtp_port: formik.values.smtp_port,
        imap_host: formik.values.imap_host,
        imap_port: formik.values.imap_port,
        email_host_user: formik.values.email_host_user,
        email_use_tls: formik.values.email_use_tls,
        email_use_ssl: formik.values.email_use_ssl,
        email_host_password: formik.values.email_host_password,
        business: BuisnessId.BuisnessId,
        is_active: true,
      };

      const url = `${resturls.emailserver}?business_id=${BuisnessId.BuisnessId}/`;

      if (emailConfOptionsAll.length > 0) {
        GlobalService.generalSelect(
          (response) => {
            setActiveModal(false);
            fetchEmailConf();
          },
          `${resturls.emailserver}${formik.values.emailConfig}/`,
          serverInfoPayload,
          'PATCH'
        );
      } else {
        GlobalService.generalSelect(
          (response) => {
            setActiveModal(false);
            fetchEmailConf();
          },
          url,
          serverInfoPayload,
          'POST'
        );
      }
    };

    return (
      <form>
        <div className={style.formContainer}>
          <Form.Field className={style.formField}>
            <label>Server Name</label>
            <Form.Input
              id="server_name"
              name="server_name"
              placeholder="Enter Server Name:"
              onChange={formik.handleChange}
              onBlur={formik.handleBlur}
              value={formik.values.server_name}
              error={
                formik.touched.server_name && formik.errors.server_name
                  ? { content: formik.errors.server_name, pointing: 'below' }
                  : null
              }
            />
          </Form.Field>

          <Form.Field className={style.formField}>
            <label>SMTP HOST</label>
            <Form.Input
              id="email_host"
              name="email_host"
              placeholder="Enter server email"
              onChange={formik.handleChange}
              onBlur={formik.handleBlur}
              value={formik.values.email_host}
              error={
                formik.touched.email_host && formik.errors.email_host
                  ? { content: formik.errors.email_host, pointing: 'below' }
                  : null
              }
            />
          </Form.Field>

          <Form.Field className={style.formField}>
            <label>User Email </label>
            <Form.Input
              id="email_host_user"
              name="email_host_user"
              placeholder="Enter User Email "
              onChange={formik.handleChange}
              onBlur={formik.handleBlur}
              value={formik.values.email_host_user}
              error={
                formik.touched.email_host_user && formik.errors.email_host_user
                  ? {
                      content: formik.errors.email_host_user,
                      pointing: 'below',
                    }
                  : null
              }
            />
          </Form.Field>

          <Form.Field className={style.formField}>
            <label>User Password</label>
            <div style={{ position: 'relative' }}>
              <Form.Input
                id="email_host_password"
                name="email_host_password"
                type={showPasswordInt ? 'text' : 'password'} // If showPassword is true, set type to "text"
                placeholder="Enter server password"
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
                value={formik.values.email_host_password}
                error={
                  formik.touched.email_host_password && formik.errors.email_host_password
                    ? {
                        content: formik.errors.email_host_password,
                        pointing: 'below',
                      }
                    : null
                }
              />
              <Icon
                name={showPasswordInt ? 'eye slash' : 'eye'} // Toggling between open and closed eye
                link
                onClick={togglePasswordVisibilityInt} // On click, toggle the password visibility
                style={{
                  position: 'absolute',
                  top: '50%',
                  right: '10px',
                  transform: 'translateY(-50%)',
                  cursor: 'pointer',
                  zIndex: 10,
                  color: '#000',
                }}
              />
            </div>
          </Form.Field>

          <Form.Field className={style.formField}>
            <label>SMTP PORT </label>
            <Form.Input
              id="smtp_port"
              name="smtp_port"
              placeholder="Enter SMTP prt"
              onChange={formik.handleChange}
              onBlur={formik.handleBlur}
              value={formik.values.smtp_port}
              error={
                formik.touched.smtp_port && formik.errors.smtp_port
                  ? { content: formik.errors.smtp_port, pointing: 'below' }
                  : null
              }
            />
          </Form.Field>

          <Form.Field className={style.formField}>
            <label>IMAP HOST </label>
            <Form.Input
              id="imap_host"
              name="imap_host"
              placeholder="Enter IMAP HOST: "
              onChange={formik.handleChange}
              onBlur={formik.handleBlur}
              value={formik.values.imap_host}
              error={
                formik.touched.imap_host && formik.errors.imap_host
                  ? { content: formik.errors.imap_host, pointing: 'below' }
                  : null
              }
            />
          </Form.Field>

          <Form.Field className={style.formField}>
            <label>IMAP PORT </label>
            <Form.Input
              id="imap_port"
              name="imap_port"
              placeholder="Enter IMAP PORT: "
              onChange={formik.handleChange}
              onBlur={formik.handleBlur}
              value={formik.values.imap_port}
              error={
                formik.touched.imap_port && formik.errors.imap_port
                  ? { content: formik.errors.imap_port, pointing: 'below' }
                  : null
              }
            />
          </Form.Field>

          <Form.Field className={style.formField}>
            <label>Email Encryption</label>
            <Dropdown
              id="email_encryption"
              name="email_encryption"
              placeholder="Select Encryption Type"
              fluid
              className="customDropdown4"
              selection
              options={sslTlsOptions}
              value={formik.values.email_encryption}
              onChange={(e, { value }) => {
                formik.setFieldValue('email_encryption', value); // Update Formik's field
                // Update TLS and SSL fields based on encryption type
                formik.setFieldValue('email_use_tls', value === 'tls');
                formik.setFieldValue('email_use_ssl', value === 'ssl');
              }}
              onBlur={() => formik.setFieldTouched('email_encryption')} // Handle blur for Formik
              error={
                formik.touched.email_encryption && formik.errors.email_encryption
                  ? {
                      content: formik.errors.email_encryption,
                      pointing: 'below',
                    }
                  : null
              }
            />
          </Form.Field>
        </div>

        <div className={style.buttonWrapper}>
          <Button type="button" className={style.cancel} onClick={() => setActiveModal(false)}>
            Cancel
          </Button>
          <Button type="button" className={style.nextBtn} onClick={handleSaveDetails} disabled={!formik.isValid}>
            Save Details
          </Button>
        </div>
      </form>
    );
  };

  const handleRemoveUserFromList = (userId) => {
    const updatedUsers = selectedUsers.filter((user) => user !== userId);
    setSelectedUsers(updatedUsers);
    formik.setFieldValue('users', updatedUsers);
  };

  const renderMemberAddForm = () => (
    <div className={style.formContainer}>
      <Form.Field className={style.formField}>
        <label>Users</label>
        <Dropdown
          id="users"
          name="users"
          placeholder="Select users"
          className="customDropdown4"
          fluid
          multiple
          search
          selection
          icon={<DropdownIcon />}
          options={options}
          onChange={handleChange}
          onBlur={formik.handleBlur}
          renderLabel={() => null}
          error={
            formik.touched.users && formik.errors.users ? { content: formik.errors.users, pointing: 'below' } : null
          }
        />
      </Form.Field>
      <div className={style.selctedUsers}>
        {selectedUsers?.map((userId) => {
          const user = FetchUserList.find((u) => u.user_id === userId);
          return (
            <p key={userId} className={style.user}>
              {user?.full_name}
              <span onClick={() => handleRemoveUserFromList(userId)}>{closeIcon()} </span>
            </p>
          );
        })}
      </div>
    </div>
  );

  const renderButtons = () => {
    return (
      <div className={style.buttonWrapper}>
        <Button type="button" className={style.cancel} onClick={() => setActiveModal(false)}>
          Cancel
        </Button>
        <Button type="submit" className={style.nextBtn}>
          Save Details
        </Button>
      </div>
    );
  };

  const renderEditButtons = () => {
    const keys1 = ['name', 'gst', 'address'];
    const step1Validation = keys1.some((key) => Object.keys(editFormik.errors).includes(key));
    const formatPhoneNumber = (phone) => {
      if (phone.startsWith('+91')) {
        return phone.slice(1);
      } else if (!phone.startsWith('91')) {
        return `91${phone}`;
      }
      return phone;
    };
    if (step?.active === 'step1') {
      return (
        <div className={style.buttonWrapper}>
          <Button type="button" className={style.cancel} onClick={() => setActiveModal(false)}>
            Cancel
          </Button>
          <Button
            type="button"
            className={style.nextBtn}
            onClick={() => setStep({ ...step, active: 'step2', step2: true })}
            disabled={step1Validation}
          >
            Next
          </Button>
        </div>
      );
    }
    return (
      <div className={style.buttonWrapper}>
        <Button className={style.cancel} type="button" onClick={() => setActiveModal(false)}>
          Cancel
        </Button>
        <div className={style.subBtnWrapper}>
          <Button type="button" className={style.backBtn} onClick={() => setStep({ ...step, active: 'step1' })}>
            Back
          </Button>
          {console.log(editFormik.errors, ';editFormik.isValid')}
          <Button
            type="submit"
            onClick={() => {
              const updatedValues = {
                ...editFormik.values,
                business_superuser_id: parseInt(editFormik.values.business_superuser_id, 10),
                business_type_id: parseInt(editFormik.values.business_type, 10),
                phone: formatPhoneNumber(editFormik.values.phone),
              };
              delete updatedValues.business_type;

              GlobalService.generalSelect(
                (respdata) => {
                  setBusinessData(respdata);
                  setActiveModal(false);
                  window.location.reload();
                },
                `${resturls.createBusiness}${BuisnessId.BuisnessId}/`,
                updatedValues,
                'PATCH'
              );
            }}
            className={style.nextBtn}
            disabled={!editFormik.isValid}
          >
            Save
          </Button>
        </div>
      </div>
    );
  };

  const renderCreateForm = () => {
    if (step?.active === 'step1') {
      return (
        <div className={style.formContainer}>
          <Form.Field className={style.formField}>
            <label>Business Name</label>
            <Form.Input
              id="name"
              name="name"
              placeholder="Enter business name"
              onChange={editFormik.handleChange}
              onBlur={editFormik.handleBlur}
              value={editFormik.values.name}
              error={
                editFormik.touched.name && editFormik.errors.name
                  ? { content: editFormik.errors.name, pointing: 'below' }
                  : null
              }
            />
          </Form.Field>

          <Form.Field className={style.formField}>
            <label>GST Number</label>
            <Form.Input
              id="gst"
              name="gst"
              placeholder="Enter GST"
              onChange={editFormik.handleChange}
              onBlur={editFormik.handleBlur}
              value={editFormik.values.gst}
              error={
                editFormik.touched.gst && editFormik.errors.gst
                  ? { content: editFormik.errors.gst, pointing: 'below' }
                  : null
              }
            />
          </Form.Field>

          <Form.Field className={style.formField}>
            <label>Address</label>
            <Form.Input
              id="address"
              name="address"
              placeholder="Enter address"
              onChange={editFormik.handleChange}
              onBlur={editFormik.handleBlur}
              value={editFormik.values.address}
              error={
                editFormik.touched.address && editFormik.errors.address
                  ? { content: editFormik.errors.address, pointing: 'below' }
                  : null
              }
            />
          </Form.Field>

          <Form.Field className={style.formField}>
            <label>Google Map Link</label>
            <Form.Input
              id="google_map_link"
              name="google_map_link"
              placeholder="Enter Google Map Link"
              onChange={editFormik.handleChange}
              onBlur={editFormik.handleBlur}
              value={editFormik.values.google_map_link}
              error={
                editFormik.touched.google_map_link && editFormik.errors.google_map_link
                  ? {
                      content: editFormik.errors.google_map_link,
                      pointing: 'below',
                    }
                  : null
              }
            />
          </Form.Field>

          <Form.Field className={style.formField}>
            <label>Business Type</label>
            <Dropdown
              ref={dropdownRef}
              placeholder="Select a business type"
              fluid
              search
              selection
              options={dropdownOptions}
              onChange={handleDropdownChange}
              value={editFormik.values.business_type}
              className="customDropdown4withLoadMore"
              onBlur={(e) => {
                e.preventDefault();
                return false;
              }}
              onClick={(e) => {
                if (e.target.classList.contains('load-more-option')) {
                  e.preventDefault();
                  e.stopPropagation();
                }
              }}
            />
          </Form.Field>
        </div>
      );
    }
    return (
      <div className={style.formContainer}>
        <Form.Field className={style.formField}>
          <label>Primary Contact Name</label>
          <Form.Input
            id="primary_contact_name"
            name="primary_contact_name"
            placeholder="Enter Primary Contact Name"
            onChange={editFormik.handleChange}
            onBlur={editFormik.handleBlur}
            value={editFormik.values.primary_contact_name}
            error={
              editFormik.touched.primary_contact_name && editFormik.errors.primary_contact_name
                ? {
                    content: editFormik.errors.primary_contact_name,
                    pointing: 'below',
                  }
                : null
            }
          />
        </Form.Field>

        <Form.Field className={style.formField}>
          <label>Phone Number</label>
          <Form.Input
            id="phone"
            name="phone"
            placeholder="Enter Phone Number"
            onChange={editFormik.handleChange}
            onBlur={editFormik.handleBlur}
            value={editFormik.values.phone}
            error={
              editFormik.touched.phone && editFormik.errors.phone
                ? { content: editFormik.errors.phone, pointing: 'below' }
                : null
            }
          />
        </Form.Field>

        <Form.Field className={style.formField}>
          <label>Email</label>
          <Form.Input
            id="email"
            name="email"
            placeholder="Enter email"
            onChange={editFormik.handleChange}
            onBlur={editFormik.handleBlur}
            value={editFormik.values.email}
            error={
              editFormik.touched.email && editFormik.errors.email
                ? { content: editFormik.errors.email, pointing: 'below' }
                : null
            }
          />
        </Form.Field>

        <Form.Field className={style.formField}>
          <label>Business Superuser</label>
          <Dropdown
            placeholder="Select a Superuser"
            className="customDropdown4"
            fluid
            search
            selection
            options={Superuseroptions}
            onChange={(e, { value }) => editFormik.setFieldValue('business_superuser_id', value)}
            value={editFormik.values.business_superuser_id}
          />
          {editFormik.errors.business_superuser_id && <div>{editFormik.errors.business_superuser_id}</div>}
        </Form.Field>
      </div>
    );
  };

  const AddUser = () => {
    const selectedUsersIntegers = selectedUsers.map((user) => parseInt(user, 10));
    const payload = { add_business_users: selectedUsersIntegers };
    setIsLoading(true);
    GlobalService.generalSelect(
      (respdata) => {
        setActiveModal(false);
        GetMembers(page);
        setIsLoading(false);
      },
      `${resturls.createBusiness}${BuisnessId.BuisnessId}/`,
      payload,
      'PATCH'
    );
  };

  const lastElementRef = useCallback(
    (node) => {
      if (isLoading) return;
      if (observer.current) observer.current.disconnect();

      observer.current = new IntersectionObserver((entries) => {
        if (entries[0].isIntersecting && hasMore) {
          setPage((prevPage) => prevPage + 1);
        }
      });

      if (node) observer.current.observe(node);
    },
    [isLoading, hasMore]
  );

  const renderModalContent = () => {
    if (activeModal === 'reconfig') {
      return (
        <div className={style.modalContentdelete}>
          <h5>Reconfigure</h5>
          <Form className={style.formWrapper} onSubmit={formik.handleSubmit}>
            {RenderReconfigForm()}
          </Form>
        </div>
      );
    }

    if (activeModal === 'addMember') {
      return (
        <div className={`${style.memberModal} ${style.modalContentdelete}`}>
          <h5>Add User</h5>
          <Form className={style.formWrapper} onSubmit={() => AddUser()}>
            {renderMemberAddForm()}
            {renderButtons()}
          </Form>
        </div>
      );
    }

    if (activeModal === 'deleteOrganisation') {
      return (
        <div className={`${style.autoHeight} ${style.modalContentdelete}`}>
          <div className={style.imgWrapper}>
            <Image src={infoIcon} />
          </div>
          <div className={style.descContent}>
            <h5>Are you sure you want to delete Buisness: {businessData?.name}</h5>
            <p>.</p>
          </div>
          <div className={style.buttonWrapper}>
            <Button type="button" className={style.cancel} onClick={() => setActiveModal(false)}>
              Cancel
            </Button>
            <Button type="submit" className={style.redBtn} onClick={() => deleteBuisness()}>
              {' '}
              Delete
            </Button>
          </div>
        </div>
      );
    }

    if (activeModal === 'editOrganisation') {
      return (
        <div className={style.modalContent}>
          <h5>Edit Business</h5>
          <div className={style.progressWrapper}>
            <div className={style.progress}>
              <span
                className={`${step?.step1 ? style.enableDot : ''} ${
                  step?.active === 'step1' ? style.activeDot : ''
                } ${style.dot}`}
              />
              <span className={style.line} />
              <span
                className={`${step?.step2 ? style.enableDot : ''} ${
                  step?.active === 'step2' ? style.activeDot : ''
                } ${style.dot}`}
              />
            </div>
            <div className={style.labelWrapper}>
              <div className={style.label}>
                <span className={style.firstLable}>Basic Info</span>
                <span>Primary Contact</span>
              </div>
            </div>
          </div>
          <Form className={style.formWrapper} onSubmit={formik.handleSubmit}>
            {renderCreateForm()}
            {renderEditButtons()}
          </Form>
        </div>
      );
    }
  };

  return (
    <>
      <Header />
      <div className={style.businessDetails}>
        <div className={style.backIconWrapper} onClick={goBack}>
          {backIcon()}
        </div>
        <div className={style.headerContainer}>
          <div className={style.leftContent}>
            <div className={style.logo}>{renderLogo(businessDeatil?.logo)}</div>
            <div>
              <h5>{businessDeatil?.name}</h5>
              <Dropdown
                placeholder="Status"
                options={statusOptions}
                onChange={(e, { value }) => handleStatusUpdate(value)}
                value={businessDeatil?.status}
                className={`${style.dropdown} ${
                  style.statusDropdown
                } custom-dropdown2 ${getStatusClass(businessDeatil?.status)}`}
                icon={<DropdownIcon />}
              />
            </div>
          </div>
          <div className={style.rightContent}>
            <p
              onClickCapture={() => {
                setActiveModal('editOrganisation');
                setStep({ ...step, step2: true });
              }}
            >
              {editIcon()}Edit Details
            </p>
            <p onClickCapture={() => setActiveModal('deleteOrganisation')}>{deleteIcon()}Delete Org</p>
          </div>
        </div>

        <div className={style.contentWrapper}>
          {/* Conditionally render loader and content */}
          {isLoading ? (
            <div
              style={{
                position: 'absolute',
                top: 0,
                left: 0,
                right: 0,
                bottom: 0,
                display: 'flex',
                justifyContent: 'center',
                alignItems: 'center',
                backgroundColor: 'rgba(255, 255, 255, 0.8)', // Optional overlay
                zIndex: 1000,
              }}
            >
              <Loader active inline="centered" size="medium" />
            </div>
          ) : (
            <>
              <div className={style.menuList}>{renderMenu()}</div>
              {renderContent()}
            </>
          )}
        </div>
      </div>

      <Modal basic size="small" open={activeModal} onClose={() => setActiveModal(false)}>
        {renderModalContent()}
      </Modal>
    </>
  );
};

export default BusinessDetailed;

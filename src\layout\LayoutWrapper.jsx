import React from 'react';
import Header from './Header';
import Sidebar from './Sidebar';
import { Outlet } from 'react-router-dom';
import { SuspenseWrapper } from '../components/utils/JSXUtils';

function LayoutWrapper() {
  return (
    <div className="flex flex-col h-screen overflow-hidden">
      <Header />
      <div className="flex flex-1 overflow-hidden">
        <Sidebar />
        <div className="flex-1 overflow-y-auto relative">
          <SuspenseWrapper>
            <Outlet />
          </SuspenseWrapper>
        </div>
      </div>
    </div>
  );
}

export default LayoutWrapper;

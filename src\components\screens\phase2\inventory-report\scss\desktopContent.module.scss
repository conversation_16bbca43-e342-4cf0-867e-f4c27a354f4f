@import '../../../../../assets/scss/main.scss';

.contentWrapper {
  display: flex;
  gap: 2em;
  width: 100%;

  .chartWrapper {
    width: 50%;
    // height: 85%;
    padding: 1em;
    background-color: $white;
    border-radius: 15px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    gap: 2.5em;

    canvas {
      max-width: 100% !important;
    }
  }

  .detailedWrapper {
    width: 100%;

    .overAllInfo {
      display: flex;
      justify-content: space-between;
      // grid-template-columns: 2fr 1fr;
      background: #eaecf5;
      border-radius: 1em;
      padding: 0em;
      align-items: last baseline;
      font-weight: bolder;
      width: 100%;

      .leftInfo {
        h4 {
          display: flex;
          gap: 1em;
          align-items: center;
          font-size: 1.3em !important;
          margin: 0;

          svg {
            width: 30px;
            height: 30px;
          }
        }

        p {
          color: #252b37;
          font-size: 1.1em !important;
        }
      }

      .rightInfo {
        h4 {
          font-size: 1.3em !important;
          margin: 0;
        }
      }
    }

    .detailsListContainer {
      padding: 2em 0;

      h5 {
        margin: 0;
        font-size: 1.3em !important;
      }

      .detailsList {
        display: flex;
        flex-direction: column;
        gap: 1.5em;
        margin: 1em 0;

        div {
          display: flex;
          align-items: center;
          justify-content: space-between;
          padding: 1em;
          background-color: $white;
          border-radius: 10px;

          p {
            margin: 0;
          }
        }
      }
    }
  }
}

.titleIcon {
  h4 {
    display: flex;
    gap: 1em;
    align-items: center;
    font-size: 0.9em !important;
    margin: 0;

    svg {
      width: 30px;
      height: 30px;
    }
  }
}

.titlePart {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1em 0;

  h4 {
    margin: 0 !important;
    font-size: 1.4em !important;
    -webkit-text-stroke: 0.3px;
  }

  .filterWrapper {
    width: 45%;
  }

  @include for_media(mobileScreen) {
    padding: 1.5em 0 !important;
  }
}

.loader {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 65vh;
  width: 100%;
  position: relative;
}

.emptyMsg {
  display: flex;
  justify-content: center;
  flex-direction: column;
  align-items: center;
  gap: 2em;
  height: 45vh;
  width: 100%;
}

.filterBtnWrapper {
  display: flex;
  gap: 0.5em;

  @include for_media(mobileScreen) {
    padding: 2em 1em 0em 1em;
  }

  button {
    width: 7.5em !important;
    height: 3em !important;
    border-radius: 35px !important;

    @include for_media(mobileScreen) {
      width: 48% !important;
      height: 3.3em !important;
      font-size: 1.15em !important;
    }
  }

  .clearBtn {
    border: 1px solid #f9e699;
    background-color: #fdf7dd !important;
    color: #7e6607 !important;
  }

  .applybtn {
    background-color: #f6d659 !important;
    color: #7e6607 !important;
  }

  .applybtn:hover {
    background-color: #e7c746 !important;
  }
}

.paginationWrapper {
  padding: 1em 0;
  display: flex;
  justify-content: flex-end;

  @include for_media(mobileScreen) {
    justify-content: center;
  }
}

.reportContainer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 10px;
}

.sortButton {
  cursor: pointer;
  word-wrap: break-word;
  line-height: 1em;
  white-space: normal;
  outline: 0;
  transform: rotateZ(0);
  min-width: 7em !important;
  min-height: 2.714em !important;
  background: #fff;
  display: inline-block;
  padding: 0.78571429em 2.1em 0.78571429em 1em;
  color: rgba(0, 0, 0, 0.87);
  box-shadow: none;
  border: 1px solid rgba(34, 36, 38, 0.15);
  border-radius: 1.286rem !important;
  transition:
    box-shadow 0.1s ease,
    width 0.1s ease;
}

.searchInput {
  border-radius: 10px;
  // padding: 8px 12px;
  // border: 1px solid #ddd;
  // background-color: #fff;
}

.gridContainer {
  display: flex;
  flex-wrap: wrap;
  height: 50vh;
  overflow-y: auto;
  gap: 1em;
  overflow-x: hidden;
}

.card {
  background: #fff;
  border-radius: 1.286rem !important;
  border: none;
  padding: 16px;
  width: 100% !important;
  margin: 1em 0 !important;
  box-shadow: none !important;
  width: 32% !important;
  max-height: 35%;
}

.cardHeader {
  font-size: 1.2rem;
  font-weight: 100;
  text-align: left;
}

.cardHeaderRow {
  display: flex;
  justify-content: space-between;
  font-size: 1rem;
  margin-top: 8px;
  // border-bottom: 1px solid #ccc;
  padding-bottom: 4px;
  color: #535862;

  span {
    width: 33%;
    font-size: 1.1em !important;
    // text-align: center;
  }
}

.cardContent {
  display: flex;
  justify-content: space-between;
  font-size: 1rem;
  margin-top: 8px;
  padding-bottom: 4px;

  span {
    width: 33%;
    font-size: 1.2em !important;
    overflow-wrap: break-word;
    word-wrap: break-word;
  }
}

.mobileHeaderContent {
  justify-content: space-between;
  display: inline-flex;
  background: #eaecf5;
  border-radius: 1em;
  padding: 1em;
  align-items: last baseline;
  width: 100%;

  .overAllInfo {
    display: flex;
    justify-content: space-between;
    align-items: center;
    // grid-template-columns: 2fr 1fr;
    background: #eaecf5;
    border-radius: 1em;
    padding: 0em;
    // align-items: last baseline;
    font-weight: bolder;
    width: 100%;

    .leftInfo {
      display: flex;
      gap: 0.2em;
      align-items: center;
      font-size: 1.3em !important;
      margin: 0;

      svg {
        width: 30px;
        height: 30px;
      }
    }

    .rightInfo {
      font-size: 1.3em !important;
      margin: 0;
    }
  }
}

.reportHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 1.5em;
}

.sortButtonMb {
  // background: #D5D7DA;
  border: 1px solid lightgray;
  border-radius: 1em;
  width: 7em;
  height: 3em;
  display: flex;
  justify-content: space-around;
  align-items: center;
  background: white;
}

.filterWrapper {
  display: flex;
  margin: 1em 0;
  gap: 1em;

  .customInput {
    display: flex;
    align-items: center;
    gap: 1em;
    width: 80%;
    height: 3.7em;
    background-color: #f5f5f5;
    padding: 1em;
    border-radius: 13px;
    border: 1px solid #d5d7da;

    svg {
      width: 25px;
      height: 25px;
    }

    input {
      width: 85%;
      border: none !important;
      height: 2.5em;
      color: black !important;
      background-color: unset !important;
    }

    input:focus {
      outline: none !important;
      border: none !important;
    }

    input::placeholder {
      font-size: 1.2em !important;
    }

    @include for_media(mobileScreen) {
      width: 100%;
    }
  }
}

.contentWrapper {
  display: flex;
  justify-content: space-between;
  font-size: 1.5em;
  padding: 1em;
  background-color: #eaecf5;
  border-radius: 10px;
  // gap: 2em;
  width: 100%;
  font-weight: 900;
}

.iconSize {
  height: 2em;
  width: 50px;
}

.backIcon {
  padding: 1em 0;

  svg {
    height: 35px;
    width: 35px;
  }
}

.sortFilter {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20%;
  min-width: 8em;
  background-color: #f5f5f5;
  border-radius: 13px;
  border: 1px solid #d5d7da;
  gap: 1em;
  cursor: pointer;

  svg {
    width: 22px;
    height: 22px;
  }

  p {
    font-size: 1.2em !important;
  }

  @include for_media(mobileScreen) {
    height: 3.2em;
  }
}

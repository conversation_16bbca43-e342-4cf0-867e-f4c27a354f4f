import React, { useEffect, useState, useRef } from 'react';
import style from './scss/costReport.module.scss';
import ls from 'local-storage';
import { mediaBreakpoint } from '../../../global/MediaBreakPointes';
import GlobalService from '../../../services/GlobalServices';
import { resturls } from '../../../utils/apiurls';
import { useNavigate } from 'react-router-dom';
import { timelineDropdownOptions } from '../../../utils/constants';
import { getDateRange, getPreviousTrendlineDates, formatTimezoneDate, getGranularity } from '../../../utils/dateUtils';
import { debugLog } from '../../../utils/debuggerUtils';
import { BackIcon, DownloadIcon } from '../../../../assets/svgs';
import RenderOverallContent from './components/RenderOverallContent';

const CostReport = () => {
  // Component State
  const navigate = useNavigate();
  const [selectedTimeline, setTimeline] = useState(timelineDropdownOptions[0]);
  const [openDropdown, setOpenDropdown] = useState(false);
  const [downloadModal, setDownloadModal] = useState(false);
  const isResponsive = mediaBreakpoint?.mobile > window.innerWidth;
  const [trendlineData, setTrendLineData] = useState({});
  const [costDetails, setCostDetails] = useState({});
  const [isLoading, setIsLoading] = useState(false);
  const [isDetailedReportLoading, setIsDetailedReportLoading] = useState(false);
  const [error, setError] = useState(null);
  const [btnLoader, setbtnLoader] = useState(false);
  const [dateData, setDateData] = useState({});
  const [activePage, setActivePage] = useState(1);
  const [customDate, setCustomDate] = useState(false);
  const [changes, setChanges] = useState(0);
  const [details, setDetails] = useState({});
  const [activeInput, setActiveInput] = useState({
    startDate: false,
    endDate: false,
  });
  const [activeDetailsPage, setActiveDetailsPage] = useState(1);
  const popupRef = useRef(null);
  const today = new Date().toISOString().split('T')[0];
  const business_id = ls.get('selectedBusiness')?.business_id;

  // Fetch Data Functions
  const fetchDataForTrendline = async (page, obj) => {
    if (!obj) return;
    const { previousStartDate, previousEndDate } = obj;
    let granularity = getGranularity(previousStartDate, previousEndDate);
    setIsLoading(true);
    setError(null);
    const queryParams = new URLSearchParams();
    queryParams.append('start_date', previousStartDate);
    queryParams.append('end_date', previousEndDate);
    queryParams.append('granularity', granularity);
    queryParams.append('page_size', 30);
    queryParams.append('business_id', ls.get('selectedBusiness')?.business_id);
    if (page) {
      queryParams.append('page', page);
    }

    GlobalService.generalSelect(
      async (response) => {
        if (response?.data) {
          const data = response;
          const { expiry_at } = data;
          const dataToStore = {
            data,
            expiry_at,
          };
          ls.set('CostDetailsTrendlineData', dataToStore);
          setTrendLineData(data);
        }
        setIsLoading(false);
      },
      `${resturls.getCostReportData}?${queryParams}`,
      {},
      'GET'
    );
  };

  const fetchData = async (page, obj) => {
    if (!obj) {
      debugLog('fetchData called with invalid obj', { page, obj }, 'Cost Report Page');
      return;
    }
    const { startDate, endDate } = obj;
    if (startDate && endDate) {
      debugLog('Fetching data with params', { startDate, endDate, page }, 'Cost Report Page');
      setOpenDropdown(false);
      let granularity = getGranularity(startDate, endDate);
      setIsLoading(true);
      setError(null);
      const queryParams = new URLSearchParams();
      queryParams.append('start_date', startDate);
      queryParams.append('end_date', endDate);
      queryParams.append('granularity', granularity);
      queryParams.append('page_size', 30);
      queryParams.append('business_id', ls.get('selectedBusiness')?.business_id);
      if (page) {
        queryParams.append('page', page); // Make sure to pass a correct page value if needed
      } else {
        setActivePage(1);
      }
      debugLog('Sending API request with queryParams', queryParams.toString(), 'Cost Report Page');
      GlobalService.generalSelect(
        async (response) => {
          if (response?.data) {
            debugLog('Received API response', response, 'Cost Report Page');
            const data = response;
            const { expiry_at } = data;
            const dataToStore = {
              data,
              expiry_at,
              localTimeline: selectedTimeline,
              activePage: page || 1,
            };
            try {
              ls.set('CostReportPayload', dataToStore);
              debugLog('Stored data in localStorage', dataToStore, 'Cost Report Page');
            } catch (err) {
              console.error('Error storing data in localStorage:', err);
              setError(err);
            }
            setCostDetails(data);
            handleDetailsPagination(activePage);
          }
          setIsLoading(false);
        },
        `${resturls.getCostReportData}?${queryParams}`,
        {},
        'GET'
      );
    }
  };

  // Details Pagination and Obtain Cost Report Details
  const detailsPagination = async (page, obj) => {
    if (!obj) return;
    const { startDate, endDate } = obj;
    if (startDate && endDate) {
      setOpenDropdown(false);
      let granularity = 'day';
      setIsDetailedReportLoading(true);
      setError(null);
      const queryParams = new URLSearchParams();
      queryParams.append('start_date', startDate);
      queryParams.append('end_date', endDate);
      queryParams.append('granularity', granularity);
      queryParams.append('business_id', ls.get('selectedBusiness')?.business_id);
      queryParams.append('page_size', 31);
      if (page) {
        queryParams.append('page', page);
      } else {
        setActivePage(1);
      }
      GlobalService.generalSelect(
        async (response) => {
          if (response?.data) {
            const data = response;
            const { expiry_at } = data;
            const dataToStore = {
              data,
              expiry_at,
              localTimeline: selectedTimeline,
              activePage: page,
            };
            ls.set('CostDetailedReport', dataToStore);
            setDetails(data);
          }
          setIsDetailedReportLoading(false);
        },
        `${resturls.getCostReportData}?${queryParams}`,
        {},
        'GET'
      );
    }
  };
  const obtainCostReportDetails = () => {
    debugLog('Obtaining cost report details', 'Cost Report Page');
    const storedData = ls.get('CostReportPayload');
    const detailsData = ls.get('CostDetailedReport');
    const storedTrendlineData = ls.get('CostDetailsTrendlineData');
    debugLog('Retrieved stored data', { storedData, detailsData, storedTrendlineData }, 'Cost Report Page');
    setIsLoading(true);
    let trendDateObj = {};
    let dateObj = {};
    let timeLine = {};
    if (storedData) {
      const { data, expiry_at } = storedData;
      const expiryDate = new Date(expiry_at);
      const currentDate = new Date();
      let localTimeline = storedData?.localTimeline;
      debugLog('Checking data expiry', { expiryDate, currentDate, localTimeline }, 'Cost Report Page');
      trendDateObj = getPreviousTrendlineDates(localTimeline?.value);
      dateObj = getDateRange(localTimeline?.value);
      timeLine = localTimeline;
      if (expiryDate < currentDate) {
        debugLog('Data expired, fetching fresh data', { expiryDate, currentDate }, 'Cost Report Page');
        ls.remove('CostReportPayload');
        ls.remove('CostDetailsTrendlineData');
        ls.remove('details');
        fetchData(false, dateObj);
        fetchDataForTrendline(false, trendDateObj);
      } else {
        debugLog('Using stored data', { data }, 'Cost Report Page');
        setCostDetails(data);
        setDetails(detailsData?.data);
        setTrendLineData(storedTrendlineData?.data);
        setActivePage(storedData?.activePage || 1);
        setActiveDetailsPage(detailsData?.activePage || 1);
      }
    } else {
      debugLog('No stored data found, fetching fresh data', 'Cost Report Page');
      trendDateObj = getPreviousTrendlineDates(timelineDropdownOptions[0].value);
      dateObj = getDateRange(timelineDropdownOptions[0].value);
      timeLine = timelineDropdownOptions[0];
      fetchData(false, dateObj);
      fetchDataForTrendline(false, trendDateObj);
    }
    if (timeLine?.value === 'customDate') {
      let startDate = formatTimezoneDate(storedData?.data?.start_time);
      let endDate = formatTimezoneDate(storedData?.data?.end_time);
      dateObj = { startDate: startDate, endDate: endDate };
    }
    setDateData(dateObj);
    setTimeline(timeLine);
    setIsLoading(false);
  };

  // useEffect Hooks
  useEffect(() => {
    obtainCostReportDetails();
  }, []);

  const handleClickOutside = (event) => {
    if (!isResponsive && popupRef.current && !popupRef.current.contains(event.target)) {
      if (selectedTimeline?.value === 'customDate') {
        if (dateData?.startDate && dateData?.endDate) {
          setOpenDropdown(false);
          return;
        }
        if (dateData?.startDate || dateData?.endDate) {
          return; // Prevent closing if at least one date is selected
        }
        setTimeline(timelineDropdownOptions[0]);
        let trendDateObj = getPreviousTrendlineDates(timelineDropdownOptions[0]);
        let dateObj = getDateRange('last7days');
        console.log(dateObj, 'dateObj', timelineDropdownOptions[0]);
        fetchData(false, dateObj);
        fetchDataForTrendline(false, trendDateObj);
        detailsPagination(false, dateObj);
        setActiveInput({
          startDate: false,
          endDate: false,
        });
      }
      setOpenDropdown(false);
    }
  };

  useEffect(() => {
    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [selectedTimeline, dateData]);

  // Add monitoring for state changes
  useEffect(() => {
    debugLog('costDetails state changed', costDetails);
  }, [costDetails]);

  useEffect(() => {
    debugLog('dateData state changed', dateData);
  }, [dateData]);

  const isDateRangeMatching = (obj1, obj2) => {
    const formatDate = (dateString) => new Date(dateString).toISOString().split('T')[0];
    return formatDate(obj1.start_time) === obj2.startDate && formatDate(obj1.end_time) === obj2.endDate;
  };

  useEffect(() => {
    const storedData = ls.get('CostReportPayload');
    const localTimeline = storedData?.localTimeline;
    if (
      changes &&
      (selectedTimeline?.value !== localTimeline?.value ||
        (selectedTimeline.value === 'customDate' && !isDateRangeMatching(storedData?.data, dateData)))
    ) {
      let trendDateObj = getPreviousTrendlineDates(selectedTimeline?.value);
      let dateObj = getDateRange(selectedTimeline?.value);

      if (selectedTimeline?.value !== 'customDate') {
        fetchData(false, dateObj);
        fetchDataForTrendline(false, trendDateObj);
        detailsPagination(false, dateObj);
      }
      if (selectedTimeline?.value === 'customDate') {
        let dateObj = {
          startDate: dateData.startDate,
          endDate: dateData.endDate,
        };
        fetchData(false, dateObj);
        detailsPagination(false, dateObj);
      }
    }
  }, [changes]);

  useEffect(() => {
    if (customDate) {
      setDateData({});
    }
  }, [customDate]);

  // Helper Functions and Event Handlers

  const downloadCostReport = async (transaction) => {
    const { startDate, endDate } = dateData;
    const queryParams = new URLSearchParams();
    queryParams.append('start_date', startDate);
    queryParams.append('end_date', endDate);
    queryParams.append('transaction', transaction);
    queryParams.append('business_id', business_id);
    setDownloadModal(false);
    setbtnLoader(true);

    GlobalService.generalSelect(
      async (response) => {
        if (response) {
          const blob = new Blob([response], {
            type: 'application/octet-stream',
          });
          const link = document.createElement('a');
          link.href = URL.createObjectURL(blob);
          link.download = `cost_report_${startDate}_${endDate}.csv`;

          link.click();
          URL.revokeObjectURL(link.href);
          setDownloadModal(false);
        }
        setbtnLoader(false);
      },
      `${resturls.downloadCostReport}?${queryParams}`,
      {},
      'GET'
    );
  };

  const handleSelectDropdown = (list) => {
    setTimeline(list);
    if (list?.value !== 'customDate') {
      setDateData(getDateRange(list?.value));
      setCustomDate(false);
    }
    if (list?.value !== 'customDate' && !isResponsive) {
      setChanges(changes + 1);
      setOpenDropdown(false);
    }
    if (list.value === 'customDate') {
      setCustomDate(true);
    }
  };

  const handleChange = (date, type) => {
    setDateData((prev) => {
      const updatedDate = { ...prev, [type]: date };
      if (type === 'startDate' && !updatedDate.endDate) {
        updatedDate.endDate = new Date().toISOString().split('T')[0];
      }
      return updatedDate;
    });
  };

  const refreshData = () => {
    if (selectedTimeline?.value !== 'customDate') {
      let trendDateObj = getPreviousTrendlineDates(selectedTimeline?.value);
      let dateObj = getDateRange(selectedTimeline?.value);
      fetchData(false, dateObj);
      fetchDataForTrendline(false, trendDateObj);
      detailsPagination(false, dateObj);
    } else {
      let dateObj = {
        startDate: dateData.startDate,
        endDate: dateData.endDate,
      };
      fetchData(false, dateObj);
      detailsPagination(false, dateObj);
    }
  };

  const handleDetailsPagination = (page) => {
    setActiveDetailsPage(page);
    if (selectedTimeline?.value !== 'customDate') {
      let dateObj = getDateRange(selectedTimeline?.value);
      detailsPagination(page, dateObj);
    } else {
      let dateObj = {
        startDate: dateData.startDate,
        endDate: dateData.endDate,
      };
      detailsPagination(page, dateObj);
    }
  };

  const commonProps = {
    costDetails,
    refreshData,
    setDownloadModal,
    btnLoader,
    setOpenDropdown,
    selectedTimeline,
    handleSelectDropdown,
    activeInput,
    dateData,
    handleChange,
    setActiveInput,
    setChanges,
    changes,
    popupRef,
    openDropdown,
    isLoading,
    details,
    activeDetailsPage,
    handleDetailsPagination,
    isDetailedReportLoading,
    downloadModal,
    downloadCostReport,
    trendlineData,
    setDateData,
  };

  if (isResponsive) {
    return (
      <div className={style.mobileViewContainer}>
        <div className={style.backIcon} onClick={() => navigate('/reportsMenu')}>
          {<BackIcon />}
        </div>
        <div className={style.rightContentWrapper}>
          <RenderOverallContent {...commonProps} />
        </div>
        <div className={style.downloadBtnWrapper}>
          <button
            className={style.downloadBtn}
            onClick={() => {
              setDownloadModal(true);
            }}
          >
            <DownloadIcon />
            Download Report
          </button>
        </div>
      </div>
    );
  }

  return (
    <>
      <RenderOverallContent {...commonProps} />
    </>
  );
};

export default CostReport;

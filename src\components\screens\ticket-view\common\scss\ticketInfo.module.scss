@import '../../../../../assets/scss/main.scss';

.ticketInfoWrapper {
  display: flex;
  justify-content: space-between;
  flex-direction: column;
  width: 100%;
  height: auto;
  padding: 1em;
  line-height: 1.7;
  border-radius: 15px;
  background-color: $white;
  border-top: none;
  box-shadow: 0 0 1px 1px $borderColor;
  .ticketInfoTitle {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    gap: 0.5em;
    h4 {
      font-size: 1.4em;
    }
  }
  .flag {
    height: 2em;
    padding: 0 1em;
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: #fee4e2;
    border-radius: 15px;
    svg {
      width: 25px;
      height: 25px;
      path {
        fill: #b42318;
      }
    }
  }

  .status {
    padding: 0.5em 1em;
    border-radius: 35px;
    font-weight: 500;
    text-transform: capitalize;

    &.pending {
      background-color: #fff3dc;
      color: #ffb020;
    }

    &.active {
      background-color: #e8f7ed;
      color: #4caf50;
    }

    &.resolved {
      background-color: #e8eaf6;
      color: #3f51b5;
    }

    &.closed {
      background-color: #feebee;
      color: #f44336;
    }
  }

  .statusContainer {
    margin: 1em 0 0 0;
    display: flex;
    gap: 1em;
  }

  .commentTicketInfo {
    display: flex;
    gap: 1em;
    align-items: center;
    margin: 1em 0;
    flex-wrap: wrap;

    p {
      font-size: 1.2em;
      margin: 0;
    }

    .category {
      height: 1.8em;
      padding: 0 0.5em;
      background-color: #e9eaebb2;
      color: #535862;
      display: flex;
      align-items: center;
      border-radius: 7px;
    }
  }

  .assignInfo {
    color: #535862;
    font-size: 1.1em;
  }
}

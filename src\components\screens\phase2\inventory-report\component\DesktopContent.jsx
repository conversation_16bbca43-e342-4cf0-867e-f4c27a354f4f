import React from 'react';
import { Card, Popup } from 'semantic-ui-react';
import { EmptyBox, SearchIcon, SortIcon, TotalInventoryIcon } from '../../../../../assets/svgs';
import ReportHeader from '../../../../global/components/ReportHeader';
import style from '../scss/desktopContent.module.scss';
import { LoadingWrapper } from '../../../../global/components';
import { formatAmount } from '../../../../utils/dateUtils';
import { useAuth } from '../../../../../contexts/AuthContext';

const DesktopContent = ({
  filteredItems,
  inventoryDetails,
  handleRefresh,
  setDownloadModal,
  isLoading,
  handleDropdownList,
  renderPopupContent,
  openDropdown,
  setOpenDropdown,
  handleSearch,
  searchTerm,
}) => {
  const updatedList = filteredItems?.data?.length > 0 ? filteredItems?.data || [] : inventoryDetails?.data || [];

  const { isMobileScreen } = useAuth();

  return (
    <>
      <ReportHeader
        title="Inventory Report"
        data={inventoryDetails}
        handleRefresh={handleRefresh}
        setDownloadModal={setDownloadModal}
      />
      <>
        <div className={style.contentWrapper}>
          <div className={style.titleIcon}>
            <h4>{<TotalInventoryIcon />} Total Inventory Value</h4>
          </div>
          <div>{formatAmount(inventoryDetails?.total_inventory_value)}</div>
        </div>

        <div className={style.titlePart}>
          <h4>Detailed Report</h4>
          <div className={style.filterWrapper}>
            <Popup
              className={style.popup}
              trigger={
                <div className={style.sortFilter} onClick={() => handleDropdownList()}>
                  {' '}
                  <SortIcon />
                  <p>Sort</p>
                </div>
              }
              content={renderPopupContent}
              position="bottom left"
              on="click"
              hoverable
              basic
              open={!isMobileScreen && openDropdown}
              onClose={() => {
                setOpenDropdown(false);
              }}
            />
            <div className={style.customInput}>
              <SearchIcon />
              <input
                type="text"
                placeholder="Search by Inventory Name or Unit..."
                onChange={handleSearch}
                value={searchTerm}
              />
            </div>
          </div>
        </div>
        <LoadingWrapper loading={isLoading} minHeight={true}>
          {updatedList?.length === 0 ? (
            <div className={style.emptyMsg}>
              <EmptyBox />
              <p>Inventory details are currently unavailable.</p>
            </div>
          ) : (
            <div className={style.gridContainer}>
              {updatedList?.map((item, index) => (
                <Card kalignRightey={index} className={style.card}>
                  <Card.Content>
                    <h2 className={style.cardHeader}>{item.item_name}</h2>
                    <div className={style.cardHeaderRow}>
                      <span>Qty & Unit</span>
                      <span></span>
                      <span>Amount</span>
                    </div>
                    <div className={style.cardContent}>
                      <span>
                        {item.total_quantity} {item.unit ? item.unit : 'kg'}
                      </span>
                      {/* <span className={style.alignRight}></span> */}
                      <span>{formatAmount(item.total_value)}</span>
                    </div>
                  </Card.Content>
                </Card>
              ))}
            </div>
          )}
        </LoadingWrapper>
      </>
    </>
  );
};

export default DesktopContent;

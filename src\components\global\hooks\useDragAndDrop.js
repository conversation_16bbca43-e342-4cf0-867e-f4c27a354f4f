import { useCallback, useState, useRef } from 'react';

const preventDefaults = (e) => {
  e.preventDefault();
  e.stopPropagation();
};

function useDragAndDrop(onDropFiles = () => {}) {
  const [isDragActive, setIsDragActive] = useState(false);
  const dragCounter = useRef(0);

  const handleDragEnter = useCallback((e) => {
    preventDefaults(e);
    dragCounter.current += 1;
    setIsDragActive(true);
  }, []);

  const handleDragLeave = useCallback((e) => {
    preventDefaults(e);
    dragCounter.current -= 1;
    if (dragCounter.current === 0) {
      setIsDragActive(false);
    }
  }, []);

  const handleDragOver = useCallback(preventDefaults, []);

  const handleDrop = useCallback(
    (e) => {
      preventDefaults(e);
      dragCounter.current = 0;
      setIsDragActive(false);

      const droppedFiles = Array.from(e.dataTransfer?.files || []);
      if (droppedFiles.length > 0) {
        onDropFiles(droppedFiles);
      }
    },
    [onDropFiles]
  );

  return {
    isDragActive,
    eventHandlers: {
      onDragEnter: handleDragEnter,
      onDragLeave: handleDragLeave,
      onDragOver: handleDragOver,
      onDrop: handleDrop,
    },
  };
}

export default useDragAndDrop;

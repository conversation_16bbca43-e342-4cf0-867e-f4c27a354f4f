import React from 'react';
import PublishedWithChangesIcon from '@mui/icons-material/PublishedWithChanges';
import SyncIcon from '@mui/icons-material/Sync';
import TaskAltIcon from '@mui/icons-material/TaskAlt';

function ZohoSubmitBtn({ enable_auto_sync = false, handleSubmit, isDisabled, handleSync, status }) {
  return (
    <>
      {(status === '8' || (!enable_auto_sync && status === '3')) && (
        <button id="validate-button" type="button" className="btn-submit" onClick={() => handleSync({})}>
          <span className="flex items-center gap-2 flex-shrink-0">
            <SyncIcon />
            Sync
          </span>
        </button>
      )}
      {(status === '1' || status === '2') && enable_auto_sync && (
        <button
          id="validate-button"
          type="button"
          className="btn-submit"
          onClick={() => handleSubmit()}
          disabled={isDisabled}
        >
          <span className="flex items-center gap-2 flex-shrink-0">
            <PublishedWithChangesIcon />
            Validate & Sync
          </span>
        </button>
      )}
      {(status === '1' || status === '2') && !enable_auto_sync && (
        <button
          id="validate-button"
          type="button"
          className="btn-submit"
          onClick={() => handleSubmit()}
          disabled={isDisabled}
        >
          <span className="flex items-center gap-2 flex-shrink-0">
            <TaskAltIcon />
            Validate
          </span>
        </button>
      )}
    </>
  );
}

export default ZohoSubmitBtn;

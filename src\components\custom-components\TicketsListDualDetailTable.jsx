import React from 'react';
import { useNavigate } from 'react-router-dom';
import { Table } from 'semantic-ui-react';
import style from './scss/ticketsListDualDetailTable.module.scss';
import { useAuth } from '../../contexts/AuthContext';
import { DocumentWithRupeeIcon } from '../../assets/svgs';
import { convertDateTimeFormat, getStatusClass, renderOrganizationLogo } from '../utils';
import TicketsListCards from './TicketsListCards';

function TicketsListDualDetailTable({ tickets = [] }) {
  const navigate = useNavigate();
  const { isMobileScreen } = useAuth();

  if (isMobileScreen) {
    return <TicketsListCards tickets={tickets} />;
  }

  return (
    <div className={style.tableWrapper}>
      <Table basic="very">
        <Table.Header>
          <Table.Row>
            <Table.HeaderCell>Ticket Subject & ID</Table.HeaderCell>
            <Table.HeaderCell>Category</Table.HeaderCell>
            <Table.HeaderCell>Organization</Table.HeaderCell>
            <Table.HeaderCell>Created by and Date</Table.HeaderCell>
            <Table.HeaderCell>Requested by</Table.HeaderCell>
            <Table.HeaderCell>Status</Table.HeaderCell>
            <Table.HeaderCell></Table.HeaderCell>
          </Table.Row>
        </Table.Header>

        <Table.Body>
          {tickets?.map((data) => (
            <Table.Row onClickCapture={() => navigate(`/ticket-view/${data?.id}`)}>
              <Table.Cell>
                <div className={style.ticketSubject}>
                  <div className={style.logo}>
                    <DocumentWithRupeeIcon />
                  </div>
                  <div>
                    <p>{data?.subject}</p>
                    <span className={style.id_status}>#{data?.id}</span>
                  </div>
                </div>
              </Table.Cell>

              <Table.Cell>
                <span className={`${data?.category?.name && style.category}`}>{data?.category?.name || '-'}</span>
              </Table.Cell>

              <Table.Cell>
                <div className={style.businessInfo}>
                  <div className={style.logo}>{renderOrganizationLogo(data?.business_image)}</div>
                  {data?.business?.business_name}
                </div>
              </Table.Cell>

              <Table.Cell>
                <div className={style.clientInfo}>
                  <p>{data?.created_by || '-'}</p>
                  <span className={style.id_status}>{data?.created_at && convertDateTimeFormat(data?.created_at)}</span>
                </div>
              </Table.Cell>

              <Table.Cell>
                <div className={style.clientInfo}>
                  <p>{data?.requested_by?.user_full_name || '-'}</p>
                </div>
              </Table.Cell>

              <Table.Cell>
                <span className={`${style.status} ${getStatusClass(data?.status)}`}>{data?.status || '-'}</span>
              </Table.Cell>
            </Table.Row>
          ))}
        </Table.Body>
      </Table>
    </div>
  );
}

export default TicketsListDualDetailTable;

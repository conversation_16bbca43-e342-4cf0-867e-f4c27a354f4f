import React, { useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import style from './scss/notification.module.scss';
import { useAuth } from '../../contexts/AuthContext';
import NotificationItem from './NotificationItems';
import { mediaBreakpoint } from '../global/MediaBreakPointes';
import { closeIcon, noNotificaticon } from '../global/Icons';

const Notification = ({ setNoticationActive }) => {
  const navigate = useNavigate();
  const { notifications, setHasNewNotification, setNotifications } = useAuth();

  console.log('set-notifications', setNoticationActive);

  useEffect(() => {
    setHasNewNotification(false);
  }, []);

  const isResponsive = mediaBreakpoint?.mobile > window.innerWidth;

  const renderHeaderPart = () => {
    if (isResponsive) {
      return (
        <div className={style.header}>
          <svg
            width="25"
            height="24"
            onClickCapture={() => navigate(-1)}
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <g clipPath="url(#clip0)">
              <path
                d="M15.0156 6L9.01562 12L15.0156 18"
                stroke="black"
                strokeWidth="1.5"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
            </g>
            <defs>
              <clipPath id="clip0">
                <rect width="24" height="24" fill="white" transform="translate(0.015625)" />
              </clipPath>
            </defs>
          </svg>
          <p>Notifications</p>
        </div>
      );
    }
    return <></>;
  };

  return (
    <div className={style.notificationContainer}>
      {renderHeaderPart()}
      <div className={style.notificationList}>
        {notifications.length > 0 ? (
          notifications.map((info, index) => (
            <NotificationItem
              key={index}
              info={info}
              setNotifications={setNotifications}
              setNoticationActive={setNoticationActive}
            />
          ))
        ) : (
          <div className={style.noNotifications}>
            <span>{noNotificaticon()}</span>
            <p>No notifications yet</p>
            <p className={style.desc}>We’ll keep you updated as soon as there’s something new.</p>
          </div>
        )}
      </div>
    </div>
  );
};

export default Notification;

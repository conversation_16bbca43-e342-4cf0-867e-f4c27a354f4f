@import '../../../../../assets/scss/main.scss';

.contentWrapper {
  display: flex;
  gap: 2em;
  width: 100%;
  max-height: 100%;
  min-height: 80%;

  .chartWrapper {
    width: 50%;
    //   height: 85%;
    padding: 1em;
    background-color: $white;
    border-radius: 15px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    gap: 2.5em;

    canvas {
      max-width: 100% !important;
    }

    .spinner-wrapper {
      display: flex;
      align-items: center;
      justify-content: center;
      height: 100%;
      /* Takes the full height of parent container */
      width: 100%;
      /* Takes the full width of parent container */
    }
  }

  .detailedWrapper {
    width: 50%;

    .overAllInfo {
      padding: 1em;
      background-color: #eaecf5;
      display: flex;
      justify-content: space-between;
      border-radius: 15px;

      .leftInfo {
        p {
          margin: 0.5em 0;
        }

        h4 {
          display: flex;
          gap: 1em;
          align-items: center;
          font-size: 1.3em !important;
          margin: 0;
          @include for_media(mobileScreen) {
            font-size: 1.1em !important;
          }
          svg {
            width: 30px;
            height: 30px;
          }
        }

        p {
          color: #252b37;
          font-size: 1.1em !important;
        }
      }

      .rightInfo {
        p {
          margin: 0.5em 0;
          font-weight: 900;
          @include for_media(mobileScreen) {
            font-size: 1.1em !important;
          }
        }

        h4 {
          font-size: 1.3em !important;
          margin: 0;
          @include for_media(mobileScreen) {
            font-size: 1.1em !important;
          }
        }
      }
    }

    .detailsListContainer {
      padding: 2em 0 0 0;

      // height: 65%;
      // overflow-y: auto;
      h5 {
        margin: 0;
        font-size: 1.3em !important;
      }

      .detailsList {
        height: 50vh;
        overflow-y: auto;
        padding-right: 1em;
        margin-top: 1em;

        .Lists {
          display: flex;
          justify-content: space-between;
          list-style: none;
          font-size: 13px;
          margin-top: 1.2rem;

          li {
            width: 24%;
          }
        }

        .rowContainer {
          display: flex;
          flex-direction: column;
          background-color: $white;
          border-radius: 10px;
          margin: 1em 0;
          padding: 1em;

          p {
            width: 23%;
          }

          @include for_media(mobileScreen) {
            .closingBalance {
              width: auto;
            }
          }
        }

        /* Desktop: Show 4 columns */

        /* Mobile: Show Date above Receivables */
        .dateColumn {
          font-size: 1em !important;
          display: none;
          /* Hide in desktop view */
          margin: 0 !important;
          color: #717680;
          width: 7em !important;
          overflow: hidden;
          text-overflow: ellipsis;
        }

        .rowData {
          display: flex;
          justify-content: space-between;
          width: 100%;
          min-height: 25px;

          p {
            word-wrap: break-word;
            white-space: normal;
            @include for_media(mobileScreen) {
              font-size: 0.95em !important;
            }
          }
        }

        /* Mobile View: Hide "Date" heading and Show Date above Receivables */
        @media (max-width: 600px) {
          .MobileDate {
            display: none;
          }

          .dateColumn {
            display: block;
            /* Show Date above Receivables */
          }

          .titleMenuList {
            display: flex;
            justify-content: space-between;
            font-size: 13px;
            padding: 0 !important;
            margin-top: 18px;
            margin-bottom: 18px;
          }
        }
      }
    }
  }
}

.emptyMsg {
  height: 60vh;
  display: flex;
  flex-direction: column;
  justify-content: center;
  gap: 2em;
  align-items: center;

  @include for_media(mobileScreen) {
    height: 30vh;
  }
}

.negative {
  margin-top: 13px;
  display: flex;
  align-items: center;

  span {
    margin-left: 12px;
    font-size: 17px;
    @include for_media(mobileScreen) {
      font-size: 1em !important;
    }
  }
}

.contentLoader {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 45vh;
}

@include for_media(mobileScreen) {
  .contentWrapper {
    flex-direction: column;
    padding: 1em 0;
  }

  .chartWrapper {
    width: 100% !important;
    background-color: unset;
    padding: 0;
  }

  .detailedWrapper {
    width: 100% !important;

    .detailsList {
      gap: 1em !important;
      display: block !important;
      height: 45vh !important;
      overflow-y: auto;
      padding-bottom: 7em;

      div {
        margin: 1em 0;

        p {
          font-size: 1.1em !important;
        }
      }
    }
  }
}

@media (max-width: 400px) {
  .loader {
    display: none;
  }
}

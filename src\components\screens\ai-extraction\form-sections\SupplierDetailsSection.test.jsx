import React from 'react';
import { render, screen, waitFor } from '@testing-library/react';
import SupplierDetailsSection from './SupplierDetailsSection';
import userEvent from '@testing-library/user-event';
import { sectionValidation } from '../../../services/aiServices';
import { removeObjectsFromJson } from '../../../utils/jsonUtils';

jest.mock('../../../services/aiServices', () => ({
  sectionValidation: jest.fn(),
}));

jest.mock('react-router-dom', () => ({
  ...jest.requireActual('react-router-dom'),
  useParams: () => ({
    fileId: 'mock-file-id',
    businessId: 'mock-business-id',
  }),
}));

const mockFormData = {
  supplier_details: {
    supplier_name: 'XYZ_SUPPLIER',
    supplier_gst_no: 'ABCDEFG123',
    supplier_pan_no: 'ABCDPAN123',
    supplier_address: '1st Floor, No.12',
    supplier_state_code: '29',
    supplier_state_name: 'Karnataka',
  },
};

let latestFormData = null;

const FormWrapper = ({ initialFormData, ...restProps }) => {
  const [formData, setFormData] = React.useState(initialFormData);
  latestFormData = formData;
  const formAction = React.useCallback((action, section, field, value) => {
    let updatedSection;

    setFormData((prevFormData) => {
      const prevSection = prevFormData[section] || {};
      switch (action) {
        case 'FIELD_CHANGE':
          updatedSection = { ...prevSection, [field]: value };
          break;

        case 'UPDATE_SECTION':
          updatedSection = { ...prevSection, ...value };
          break;

        case 'HARD_UPDATE_SECTION':
          updatedSection = value;
          break;

        default:
          return prevFormData;
      }
      return {
        ...prevFormData,
        [section]: updatedSection,
      };
    });

    return updatedSection;
  }, []);
  return (
    <SupplierDetailsSection
      formData={formData}
      setFormData={setFormData}
      isReadOnly={false}
      invoiceType="purchase"
      formAction={formAction}
      {...restProps}
    />
  );
};

describe('SupplierDetailsSection', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders all fields with correct initial values', () => {
    render(<FormWrapper initialFormData={mockFormData} />);

    expect(screen.getByLabelText('GST No.')).toHaveValue('ABCDEFG123');
    expect(screen.getByLabelText('Name')).toHaveValue('XYZ_SUPPLIER');
    expect(screen.getByLabelText('Address')).toHaveValue('1st Floor, No.12');
    expect(screen.getByLabelText('State Name')).toHaveValue('Karnataka');
    expect(screen.getByLabelText('State Code')).toHaveValue('29');
    expect(screen.getByLabelText('Supplier PAN')).toHaveValue('ABCDPAN123');
  });

  it('shows initial suggestions when available', () => {
    const formDataWithSuggestions = {
      supplier_details: {
        ...mockFormData.supplier_details,
        recommended_fields: [
          {
            supplier_name: 'yyyy111',
            supplier_gst_no: 'yyyy222',
          },
        ],
      },
    };

    render(<FormWrapper initialFormData={formDataWithSuggestions} />);
    expect(screen.getByTestId('copy-icon-supplier_name')).toBeInTheDocument();
    expect(screen.getByTestId('paste-icon-supplier_name')).toBeInTheDocument();
    expect(screen.getByTestId('copy-icon-supplier_gst_no')).toBeInTheDocument();
    expect(screen.getByTestId('paste-icon-supplier_gst_no')).toBeInTheDocument();
  });

  it('show initial error messages & indicators correctly', async () => {
    const formDataWithErrors = {
      supplier_details: {
        ...mockFormData.supplier_details,
        error: {
          supplier_gst_no: {
            short_message: 'Invalid GST number',
            long_message: 'Please enter a valid GST number',
          },
          supplier_name: {
            short_message: 'Invalid name',
            long_message: 'Please enter a valid supplier name',
          },
        },
      },
    };

    render(<FormWrapper initialFormData={formDataWithErrors} />);

    // Check for error icons
    const errorIcons = screen.getAllByTestId('errorIcon');
    expect(errorIcons).toHaveLength(2);

    // Check for tooltip content
    const gstTooltip = await screen.findByTestId('tooltip-supplier_gst_no');
    await userEvent.hover(gstTooltip);
    const tooltip = await screen.findByText('Invalid GST number');
    expect(tooltip).toBeInTheDocument();

    const nameTooltip = screen.getByTestId('tooltip-supplier_name');
    await userEvent.hover(nameTooltip);
    const nameTooltipContent = await screen.findByText('Invalid name');
    expect(nameTooltipContent).toBeInTheDocument();
  });

  it('shows initial warning messages & indicators correctly', async () => {
    const formDataWithWarnings = {
      supplier_details: {
        ...mockFormData.supplier_details,
        warning: {
          supplier_gst_no: {
            short_message: 'GST number might be incorrect',
            long_message: 'Please verify the GST number',
          },
          supplier_address: {
            short_message: 'Address format might be incorrect',
            long_message: 'Please verify the address format',
          },
        },
      },
    };

    render(<FormWrapper initialFormData={formDataWithWarnings} />);

    // Check for warning icons
    const warningIcons = screen.getAllByTestId('warningIcon');
    expect(warningIcons).toHaveLength(2);

    // Check for tooltip content
    const gstTooltip = await screen.findByTestId('tooltip-supplier_gst_no');
    await userEvent.hover(gstTooltip);
    const gstTooltipContent = await screen.findByText('GST number might be incorrect');
    expect(gstTooltipContent).toBeInTheDocument();

    const addressTooltip = await screen.findByTestId('tooltip-supplier_address');
    await userEvent.hover(addressTooltip);
    const addressTooltipContent = await screen.findByText('Address format might be incorrect');
    expect(addressTooltipContent).toBeInTheDocument();
  });

  it('show initial exact match indicators when present', async () => {
    const formDataWithSuccess = {
      supplier_details: {
        ...mockFormData.supplier_details,
        exact_match: {
          supplier_gst_no: true,
          supplier_name: true,
          supplier_pan_no: true,
          supplier_address: true,
          supplier_state_code: true,
          supplier_state_name: true,
        },
      },
    };

    render(<FormWrapper initialFormData={formDataWithSuccess} />);

    const successIcons = screen.getAllByTestId('successIcon');
    expect(successIcons).toHaveLength(6);
  });

  it('handles suggestion paste functionality correctly', async () => {
    const formDataWithSuggestions = {
      supplier_details: {
        ...mockFormData.supplier_details,
        recommended_fields: [
          {
            supplier_name: 'Updated Supplier Name',
            supplier_gst_no: 'Updated GST Number',
          },
        ],
      },
    };

    // Step 1: Spy to capture final formData

    render(<FormWrapper initialFormData={formDataWithSuggestions} />);

    const supplierNamePasteIcon = screen.getByTestId('paste-icon-supplier_name');
    const supplierGstNoPasteIcon = screen.getByTestId('paste-icon-supplier_gst_no');
    expect(supplierNamePasteIcon).toBeInTheDocument();
    expect(supplierGstNoPasteIcon).toBeInTheDocument();

    await userEvent.click(supplierNamePasteIcon);
    await waitFor(() => {
      expect(screen.getByLabelText('Name')).toHaveValue('Updated Supplier Name');
    });

    await userEvent.click(supplierGstNoPasteIcon);
    await waitFor(() => {
      expect(screen.getByLabelText('GST No.')).toHaveValue('Updated GST Number');
    });

    // Step 2: Assert on final formData
    expect(latestFormData.supplier_details.supplier_name).toBe('Updated Supplier Name');
    expect(latestFormData.supplier_details.supplier_gst_no).toBe('Updated GST Number');
  });

  it('calls handleValidate with updated field and processes response correctly', async () => {
    const updatedValue = 'Another Suggestion';
    const sectionName = 'supplier_details';

    const initialFormData = {
      supplier_details: {
        ...mockFormData.supplier_details,
        recommended_fields: [
          {
            supplier_name: 'Another Suggestion',
          },
        ],
      },
    };

    render(<FormWrapper initialFormData={initialFormData} />);

    const mockResponse = {
      supplier_details: {
        ...initialFormData.supplier_details,
        supplier_name: updatedValue,
        error: {
          supplier_name: {
            short_message: 'Name issue',
            long_message: 'Fix the name',
          },
        },
        recommended_fields: null,
      },
    };

    sectionValidation.mockResolvedValue(mockResponse);

    const pasteIcon = screen.getByTestId('paste-icon-supplier_name');
    await userEvent.click(pasteIcon);

    //Ensure field is updated
    await waitFor(() => {
      expect(screen.getByLabelText('Name')).toHaveValue(updatedValue);
    });

    //Check if API was called with correct updated payload
    const expectedPayload = {
      supplier_details: {
        ...removeObjectsFromJson(initialFormData.supplier_details, [
          'error',
          'warning',
          'exact_match',
          'recommended_fields',
        ]),
        supplier_name: updatedValue, // only this field is changed
      },
    };
    expect(sectionValidation).toHaveBeenCalledWith(
      'mock-file-id',
      'mock-business-id',
      sectionName,
      'purchase',
      expectedPayload
    );

    // Wait for error icon to appear
    await waitFor(() => {
      expect(screen.getByTestId('errorIcon')).toBeInTheDocument();
    });

    // Wait for tooltip to appear
    await waitFor(() => {
      expect(screen.getByTestId('tooltip-supplier_name')).toBeInTheDocument();
    });
  });

  it('validates field on input change and blur', async () => {
    const sectionName = 'supplier_details';
    const newValue = 'New Supplier Name';

    render(<FormWrapper initialFormData={mockFormData} />);

    const mockResponse = {
      supplier_details: {
        ...mockFormData.supplier_details,
        supplier_name: newValue,
        error: {
          supplier_name: {
            short_message: 'Invalid supplier name',
            long_message: 'Please enter a valid supplier name',
          },
        },
      },
    };

    sectionValidation.mockResolvedValue(mockResponse);

    // Find and update the input field
    const nameInput = screen.getByLabelText('Name');
    await userEvent.clear(nameInput);
    await userEvent.type(nameInput, newValue);

    // Trigger blur event
    await userEvent.tab();

    // Ensure field is updated
    expect(nameInput).toHaveValue(newValue);

    // Check if API was called with correct payload
    const expectedPayload = {
      supplier_details: {
        ...removeObjectsFromJson(mockFormData.supplier_details, [
          'error',
          'warning',
          'exact_match',
          'recommended_fields',
        ]),
        supplier_name: newValue,
      },
    };

    expect(sectionValidation).toHaveBeenCalledWith(
      'mock-file-id',
      'mock-business-id',
      sectionName,
      'purchase',
      expectedPayload
    );

    // Wait for error icon to appear
    await waitFor(() => {
      expect(screen.getByTestId('errorIcon')).toBeInTheDocument();
    });

    // Wait for tooltip to appear
    await waitFor(() => {
      expect(screen.getByTestId('tooltip-supplier_name')).toBeInTheDocument();
    });
  });
});

import React, { useState, useEffect, useRef, useCallback, useMemo } from 'react';
import { Button, Input } from 'semantic-ui-react';
import style from './scss/dateRangeFilter.module.scss';
import { format } from 'date-fns';
import Option from '../ui-components/fields/Option';
import { CalenderIcon } from '../../assets/svgs';
import { ChevronDown, ChevronUp } from 'lucide-react';

const DATE_OPTIONS = [
  { key: 'all', text: 'All', value: 'all' },
  { key: 'last7Days', text: 'Last 7 days', value: 'last7Days' },
  { key: 'last30Days', text: 'Last 30 days', value: 'last30Days' },
  { key: 'currentQuarter', text: 'Current Quarter', value: 'currentQuarter' },
  { key: 'currentYear', text: 'Current Year', value: 'currentYear' },
  { key: 'customRange', text: 'Custom Range', value: 'customRange' },
];

const DEFAULT_OPTION = 'all';
const today = new Date();
const todayDate = today.toISOString().split('T')[0];

const getButtonText = (selectedOption, startDate, endDate) => {
  if (selectedOption === 'customRange' && startDate) {
    const formattedStart = format(new Date(startDate), 'MMM dd, yyyy');
    const formattedEnd = endDate ? format(new Date(endDate), 'MMM dd, yyyy') : 'Today';
    return `${formattedStart} - ${formattedEnd}`;
  }

  return DATE_OPTIONS.find((option) => option.value === selectedOption)?.text || 'Custom Range';
};

const getDateRange = (option, tempStartDate = '', tempEndDate = '') => {
  const result = {
    startDate: '',
    endDate: todayDate,
  };

  switch (option) {
    case 'all': {
      result.startDate = '';
      result.endDate = '';
      break;
    }
    case 'last7Days': {
      const last7Days = new Date(today);
      last7Days.setDate(today.getDate() - 7);
      result.startDate = last7Days.toISOString().split('T')[0];
      break;
    }
    case 'last30Days': {
      const last30Days = new Date(today);
      last30Days.setDate(today.getDate() - 30);
      result.startDate = last30Days.toISOString().split('T')[0];
      break;
    }
    case 'currentQuarter': {
      const quarter = Math.floor(today.getMonth() / 3);
      const startQuarter = new Date(today.getFullYear(), quarter * 3, 1);
      result.startDate = startQuarter.toISOString().split('T')[0];
      break;
    }
    case 'currentYear': {
      const startYear = new Date(today.getFullYear(), 0, 1);
      result.startDate = startYear.toISOString().split('T')[0];
      break;
    }
    case 'customRange': {
      result.startDate = tempStartDate;
      result.endDate = tempEndDate || todayDate;
      break;
    }
    default:
      break;
  }

  return result;
};

const DateRangeFilter = ({ onApply = null, initialStartDate = '', initialEndDate = '' }) => {
  const [isOpen, setIsOpen] = useState(false);
  const [selectedOption, setSelectedOption] = useState(DEFAULT_OPTION);
  const [startDate, setStartDate] = useState(initialStartDate);
  const [endDate, setEndDate] = useState(initialEndDate);
  const [tempOption, setTempOption] = useState(DEFAULT_OPTION);
  const [tempStartDate, setTempStartDate] = useState(initialStartDate);
  const [tempEndDate, setTempEndDate] = useState(initialEndDate);
  const dropdownRef = useRef(null);

  const handleClickOutside = useCallback((event) => {
    if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
      setIsOpen(false);
    }
  }, []);

  useEffect(() => {
    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, [handleClickOutside]);

  useEffect(() => {
    if (!isOpen) return;
    setTempOption(selectedOption);
    setTempStartDate(startDate);
    setTempEndDate(endDate);
  }, [isOpen, selectedOption, startDate, endDate]);

  const handleStartDateChange = useCallback(
    (e) => {
      const newStartDate = e.target.value;
      setTempStartDate(newStartDate);
      if (tempEndDate && tempEndDate < newStartDate) {
        setTempEndDate('');
      }
    },
    [tempEndDate]
  );

  const handleApply = useCallback(() => {
    let newStartDate, newEndDate;

    if (tempOption === 'customRange') {
      newStartDate = tempStartDate;
      newEndDate = tempEndDate || todayDate;
    } else {
      const dateRange = getDateRange(tempOption);
      newStartDate = dateRange.startDate;
      newEndDate = dateRange.endDate;
    }

    setSelectedOption(tempOption);
    setStartDate(newStartDate);
    setEndDate(newEndDate);
    setIsOpen(false);

    onApply?.(newStartDate, newEndDate);
  }, [tempOption, tempStartDate, tempEndDate]);

  const handleClear = useCallback(() => {
    setTempStartDate('');
    setTempEndDate('');
  }, []);

  const isApplyDisabled = useMemo(() => {
    if (tempOption === 'customRange' && !tempStartDate) return true;
    if (tempOption !== selectedOption) return false;
    if (tempOption === 'customRange') {
      const currentEndDate = tempEndDate || todayDate;
      return tempStartDate === startDate && currentEndDate === endDate;
    }
    return true;
  }, [tempOption, selectedOption, tempStartDate, tempEndDate, startDate, endDate]);

  return (
    <div className="relative inline-block" ref={dropdownRef}>
      <Button className={style.dateFilterBtn} onClick={() => setIsOpen(!isOpen)}>
        <CalenderIcon className="w-7 h-7" />
        <span className="text-lg text-nowrap">{getButtonText(selectedOption, startDate, endDate)}</span>
        {isOpen ? <ChevronUp className="w-6 h-6" /> : <ChevronDown className="w-6 h-6" />}
      </Button>

      {isOpen && (
        <div className="absolute top-[calc(100%+0.5em)] left-0 w-80 bg-white rounded-lg shadow-lg z-[1000] p-6 overflow-hidden">
          <div className="flex flex-col space-y-3 mb-6">
            {DATE_OPTIONS.map((option) => (
              <Option
                key={option.key}
                label={option.text}
                isChecked={tempOption === option.value}
                onClick={() => setTempOption(option.value)}
                isLabelNRadioClose={true}
                className="text-lg"
              />
            ))}
          </div>

          {tempOption === 'customRange' && (
            <>
              <div className="block mb-2 text-lg text-gray-700">Start Date</div>
              <div className="relative mb-4">
                <Input
                  type="date"
                  value={tempStartDate}
                  onChange={handleStartDateChange}
                  className={style.dateInput}
                  max={todayDate}
                />
              </div>

              <div className="block mb-2 text-lg text-gray-700">End Date</div>
              <div className="relative mb-4">
                <Input
                  type="date"
                  value={tempEndDate}
                  onChange={(e) => setTempEndDate(e.target.value)}
                  className={style.dateInput}
                  min={tempStartDate}
                  max={todayDate}
                  disabled={!tempStartDate}
                  placeholder="Today (if not selected)"
                />
                {tempStartDate && !tempEndDate && (
                  <div className="text-xs text-gray-500 mt-1 italic">Today (if left empty)</div>
                )}
              </div>
            </>
          )}

          <div className="flex justify-between mt-4 pt-4 border-t border-gray-200">
            <button
              className="flex-1 mr-2 py-3 px-3 bg-white border border-gray-200 rounded-md text-gray-700 text-base font-normal hover:bg-gray-50 transition-all duration-200 cursor-pointer"
              onClick={handleClear}
            >
              Clear
            </button>
            <button
              className="flex-1 ml-2 py-3 px-3 bg-[#ffd557] rounded-md text-gray-700 text-base font-normal hover:bg-[#ffcb29] transition-all duration-200 cursor-pointer disabled:opacity-50 disabled:cursor-not-allowed"
              onClick={handleApply}
              disabled={isApplyDisabled}
            >
              Apply
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

export default DateRangeFilter;

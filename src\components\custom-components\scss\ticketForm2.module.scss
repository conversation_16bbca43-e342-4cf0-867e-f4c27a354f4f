@import '../../../assets/scss/main.scss';

.formWrapper {
  width: 100%;
  height: 100%;
  background-color: $white;
  padding: 1.2em;
  margin-bottom: 2em;
  border-radius: 15px;
  overflow-y: auto;
  @include hide-scrollbar;
}

.formBtnContainer {
  margin: 3em 0 0 0;
  display: flex;
  justify-content: flex-end;

  .submitBtn,
  .resetBtn {
    display: flex;
    justify-content: center;
    align-items: center;
    flex-grow: 1;
    height: 3em;
    border-radius: 35px !important;
    @include clickable;
  }
  .submitBtn {
    background-color: $accentColor1 !important;
    border: 1px solid $accentBorder1;
    color: $white !important;
    &:hover {
      background-color: rgba($accentColor1, 0.8) !important;
    }
  }
  .resetBtn {
    background-color: $accentBgColor1 !important;
    border: 1px solid $accentBorder1;
    color: $primaryColor !important;
    &:hover {
      background-color: $accentHover1 !important;
    }
  }
}

.formField {
  width: 100% !important;
  display: flex;
  flex-direction: column;
  label {
    margin: 1em 0 !important;
    font-size: 1.17em !important;
  }
  input,
  textarea {
    background-color: #f5f5f5 !important;
    color: $black !important;
    border: 1px solid #e9eaeb;
    border-radius: 10px !important;
  }
  input {
    height: 3.5em;
  }
  textarea {
    height: 7em;
    padding: 1em;
  }
}

.priorityBtnContainer {
  display: flex;
  gap: 1em;
  span {
    @include flex-center;
    @include clickable;
    padding: 0.5em 1em;
    border-radius: 35px;
    border: 2px solid $accentBorder1;
    background-color: $accentBgColor1;
  }
  .active {
    background-color: $accentColor2;
    border: none;
    color: $white;
  }
}

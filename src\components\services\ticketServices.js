import apiClient from './apiClient';
import { resturls } from '../utils/apiurls';

const ticketServices = {
  getTickets: () => apiClient.get(`${resturls.ticketList}`),
  getAssinedTickts: (userId) => apiClient.get(`${resturls.ticketList}?assign_to=${userId}`),
  getTicktsByStatus: (status) => apiClient.get(`${resturls.ticketList}?status=${status}`),
  getAssinedTicktsByStatus: (userId, status) =>
    apiClient.get(`${resturls.ticketList}?assign_to=${userId}&status=${status}`),
  getCategories: () => apiClient.get(resturls.obtainCategortList),
  getTicketById: (id) => apiClient.get(`${resturls.ticketList}?id=${id}`),
  updateTicket: (id, data) => apiClient.patch(`${resturls.ticketList}${id}/`, data),
  getAllAccountantUsers: () => apiClient.get(`${resturls.obtainCategoryWiseUser}?user_type=accountants`),
};

export const createTicket = (data) => {
  return apiClient.post(`${resturls.createTicket}`, data);
};

export const syncMail = (businessId) => {
  return apiClient.post(`${resturls.syncMail}${businessId}`);
};

export default ticketServices;

import React from 'react';
import { Modal } from 'semantic-ui-react';
import style from '../../global/components/scss/reportTimelineDropdown.module.scss';
import { CloseIcon } from '../../../assets/svgs';
import DownloadModalContent from './DownloadModalContent';
import { useAuth } from '../../../contexts/AuthContext';

/**
 * Reusable Modal Component
 *
 * @param {boolean} isOpen - Modal visibility state.
 * @param {Function} onClose - Function to close the modal.
 * @param {string} content - The message to display inside the modal.
 * @param {Function} downloadFunction - The function executed on download.
 *
 * @returns {JSX.Element} - A reusable modal component.
 */
const RenderDownloadModal = ({ isOpen, onClose, checkBoxDisable, content, downloadFunction }) => {
  const { isMobileScreen } = useAuth();

  if (isMobileScreen && isOpen) {
    return (
      <div className={isOpen ? 'dimmer' : ''}>
        <div className={`${isOpen ? style.openDropdown : ''} ${style.logListPopup}`}>
          <div className={style.listCloseIcon} onClick={onClose}>
            <CloseIcon />
          </div>
          <DownloadModalContent
            content={content}
            downloadFunction={downloadFunction}
            checkBoxDisable={checkBoxDisable}
          />
        </div>
      </div>
    );
  }
  return (
    <Modal size="small" open={isOpen} onClose={onClose}>
      <Modal.Content className={style.modalContent}>
        <DownloadModalContent content={content} downloadFunction={downloadFunction} checkBoxDisable={checkBoxDisable} />
      </Modal.Content>
    </Modal>
  );
};

export default RenderDownloadModal;

import React, { useState, useRef, useEffect } from 'react';
import './scss/SearchableDropdown.scss';
import { ChevronDown, ChevronUp, Plus } from 'lucide-react';

function SearchableDropdown({
  options = [],
  value,
  onChange,
  onAddNew,
  placeholder = 'Select an option',
  disabled = false,
  className = '',
}) {
  const [isOpen, setIsOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [matchCount, setMatchCount] = useState(0);
  const [internalOptions, setInternalOptions] = useState(options);
  const dropdownRef = useRef(null);
  const inputRef = useRef(null);

  // Update internal options when external options change
  useEffect(() => {
    setInternalOptions(options);
  }, []);

  // Filter options based on search term
  const filteredOptions = internalOptions.filter((option) =>
    option.label.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // Update match count when filtered options change
  useEffect(() => {
    setMatchCount(filteredOptions.length);
  }, [filteredOptions]);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // Focus input when dropdown opens
  useEffect(() => {
    if (isOpen && inputRef.current) {
      inputRef.current.focus();
    }
  }, [isOpen]);

  const handleToggleDropdown = () => {
    if (!disabled) {
      setIsOpen(!isOpen);
      if (!isOpen) {
        setSearchTerm('');
      }
    }
  };

  const handleOptionSelect = (option) => {
    onChange(option);
    setIsOpen(false);
    setSearchTerm('');
  };

  // Find the selected option label
  const selectedLabel = value ? internalOptions.find((option) => option.value === value)?.label || value : '';

  return (
    <div className={`searchable-dropdown ${className}`} ref={dropdownRef}>
      <div
        className={`dropdown-field ${isOpen ? 'active' : ''} ${disabled ? 'disabled' : ''}`}
        onClick={handleToggleDropdown}
      >
        {!isOpen ? (
          <div className="selected-value">{selectedLabel || placeholder}</div>
        ) : (
          <input
            ref={inputRef}
            type="text"
            className="search-input"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            placeholder="Search..."
            onClick={(e) => e.stopPropagation()}
            style={{ background: 'transparent' }}
          />
        )}

        <div className="dropdown-icon">{isOpen ? <ChevronUp size={20} /> : <ChevronDown size={20} />}</div>
      </div>

      {isOpen && (
        <div className="dropdown-menu">
          {filteredOptions.length > 0 && (
            <div className="match-count">
              {matchCount} {matchCount === 1 ? 'match' : 'matches'}
            </div>
          )}

          <div className="options-list">
            {filteredOptions.map((opt) => (
              <div
                key={opt.key}
                className={`option-item ${value === opt.value ? 'selected' : ''}`}
                onClick={() => handleOptionSelect(opt)}
              >
                {opt.label}
              </div>
            ))}

            {searchTerm &&
              searchTerm.trim() !== '' &&
              !filteredOptions.some((opt) => opt.value.toLowerCase() === searchTerm.trim().toLowerCase()) && (
                <>
                  <div className="divider"></div>
                  <div className="create-new">
                    <Plus size={16} />
                    <span>Create New: "{searchTerm}"</span>
                  </div>
                </>
              )}

            {filteredOptions.length === 0 && !searchTerm && <div className="no-options">No options available</div>}
          </div>
        </div>
      )}
    </div>
  );
}

export default SearchableDropdown;

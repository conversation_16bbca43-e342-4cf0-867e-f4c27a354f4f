import React, { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { rightArrowIcon } from '../global/Icons';
import { Card } from 'semantic-ui-react';
import style from './scss/reportsMenu.module.scss';
import Header from '../global/Header';
import { mediaBreakpoint } from '../global/MediaBreakPointes';
import { REPORTS_MENU_DATA } from '../utils/reportUtils/reportData';

const ReportsMenu = () => {
  const [isMobile, setIsMobile] = useState(window.innerWidth <= mediaBreakpoint.mobile);
  const navigate = useNavigate();

  useEffect(() => {
    const checkScreenSize = () => setIsMobile(window.innerWidth <= mediaBreakpoint.mobile);

    checkScreenSize();
    window.addEventListener('resize', checkScreenSize);

    return () => window.removeEventListener('resize', checkScreenSize);
  }, []);

  useEffect(() => {
    if (!isMobile) {
      console.log('Redirecting to home screen');
      navigate('/');
    }
  }, [isMobile, navigate]);

  if (!isMobile) return null;

  const handleCardClick = (path) => {
    navigate(path);
  };

  return (
    <div>
      <Header />
      <div className={style.cardMargin}>
        {REPORTS_MENU_DATA.map((report, index) => (
          <Card fluid key={index} onClick={() => handleCardClick(report.redirectTo)}>
            <Card.Content className={style.cardContentAlign}>
              <div>{report.icon()}</div>
              <div className={style.reportTitle}>{report.title}</div>
              <div>{rightArrowIcon()}</div>
            </Card.Content>
          </Card>
        ))}
      </div>
    </div>
  );
};

export default ReportsMenu;

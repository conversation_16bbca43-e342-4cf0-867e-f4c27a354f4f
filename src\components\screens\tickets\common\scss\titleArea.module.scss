@import '../../../../../assets/scss/main.scss';

.titlePart {
  display: flex;
  justify-content: space-between;
  gap: 0.5em;
  max-width: 100%;
  padding: 0 1em;
  a {
    display: flex;
    align-items: center;
    gap: 1em;
  }
  @include for_media(mobileScreen) {
    flex-direction: column;
    align-items: flex-start;
    margin: 0 1em;
  }
}

.actionWrapper {
  display: flex;
  align-items: center;
  gap: 1em;
  margin: 0;
  @include for_media(mobileScreen) {
    justify-content: space-between;
    width: 100%;
    gap: 0.5em;
    margin: 0;
    margin-top: 1em;
  }
  .logSeachWrapper {
    display: flex;
    gap: 1em;
  }
  .plusIcon {
    height: 2.2em;
    width: 2.2em;
    background-color: $accentColor2;
    border-radius: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    flex-shrink: 0;
    svg {
      width: 25px;
      height: 25px;
    }
  }
}

.filterContainer {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}

import { useState, useEffect, useCallback, useMemo } from 'react';
import debounce from 'lodash/debounce';
import apiClient from '../../services/apiClient';

/**
 * A custom hook for handling search functionality
 * @param {string|null} url - The API URL to fetch results from
 * @param {string} queryParam - The query parameter name to use for API requests
 * @param {number} debounceDelay - Delay in ms for debouncing search requests
 * @param {string|null} externalQuery - External query state (optional)
 * @param {Function|null} setExternalQuery - Function to update external query state (optional)
 * @param {boolean} initialFetch - Whether to perform an initial fetch without query parameters (optional)
 * @returns {Object} Search state and functions
 */
function useSearch(
  url,
  queryParam,
  debounceDelay = 600,
  externalQuery = null,
  setExternalQuery = null,
  initialFetch = false
) {
  const [internalQuery, setInternalQuery] = useState('');
  const query = externalQuery || internalQuery;
  const setQuery = setExternalQuery || setInternalQuery;

  const [results, setResults] = useState([]);
  const [loading, setLoading] = useState(!!url && initialFetch);
  const [error, setError] = useState(null);

  const searchFunction = useCallback(
    async (searchTerm, signal, skipQueryParam = false) => {
      if ((!searchTerm && !skipQueryParam) || !url) {
        return;
      }

      setLoading(true);
      setError(null);

      apiClient
        .get(url, {
          params: skipQueryParam ? {} : { [queryParam]: searchTerm },
          signal: signal,
        })
        .then((data) => {
          setResults(data.results || data);
        })
        .catch((err) => {
          if (!signal.aborted) {
            setError(err.message);
          }
        })
        .finally(() => {
          setLoading(false);
        });
    },
    [url]
  );

  const search = useMemo(() => debounce(searchFunction, debounceDelay), [searchFunction, debounceDelay]);

  useEffect(() => {
    if (!url) return;
    const controller = new AbortController();

    if (initialFetch && !query) {
      search('', controller.signal, true);
    } else {
      search(query, controller.signal);
    }

    return () => {
      controller.abort();
    };
  }, [query, search, initialFetch, url]);

  return { query, setQuery, results, loading, error };
}

export default useSearch;

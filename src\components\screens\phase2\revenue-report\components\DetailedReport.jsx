import PaginationContent from '../../../../utils/reportUtils/PaginationContent';
import { DocumentIcon } from '../../../../../assets/svgs';
import style from '../scss/detailedReport.module.scss';
import { LoadingWrapper } from '../../../../global/components';
import { formatAmount, formatDateRange } from '../../../../utils/dateUtils';

const DetailedReport = ({
  detailedViewLoading,
  detailedReportData,
  revenueDetails,
  handleDetailsPagination,
  activeDetailsPage,
}) => (
  <div className={style.detailedWrapper}>
    <div className={style.overAllInfo}>
      <div className={style.leftInfo}>
        <h4>
          <DocumentIcon />
          Total Revenue
        </h4>
        <p>{formatDateRange(revenueDetails?.start_time, revenueDetails?.end_time)}</p>
      </div>
      <div className={style.rightInfo}>
        <h4>{formatAmount(revenueDetails?.total_revenue)}</h4>
      </div>
    </div>
    <div className={style.detailsListContainer}>
      <h5>Detailed Report</h5>
      <LoadingWrapper loading={detailedViewLoading} minHeight={true}>
        <div className={style.detailsList}>
          {detailedReportData?.data?.map((info) => (
            <div key={info?.label}>
              <p>{formatDateRange(info?.label, '', true)}</p>
              <p>{formatAmount(info?.amount)}</p>
            </div>
          ))}
          {detailedReportData?.pagination?.total_pages > 1 && (
            <div className={style.paginationWrapper}>
              <PaginationContent
                activePage={activeDetailsPage}
                totalPages={detailedReportData?.pagination?.total_pages}
                pageChangeFunction={handleDetailsPagination}
              />
            </div>
          )}
        </div>
      </LoadingWrapper>
    </div>
  </div>
);

export default DetailedReport;

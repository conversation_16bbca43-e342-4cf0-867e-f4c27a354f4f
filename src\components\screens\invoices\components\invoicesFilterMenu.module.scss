@import '../../../../assets/scss/main.scss';

.container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100vh;
  height: 100dvh;
  overflow: hidden;
  z-index: 1000;
  background-color: #00000072;
  -webkit-backdrop-filter: blur(0.6px);
  backdrop-filter: blur(0.6px);
}

.filtersContainer {
  width: 27%;
  height: 80vh;
  position: fixed;
  right: 1.2em;
  top: 15%;
  border-radius: 1em;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  background-color: $white;
  overflow-y: auto;
  scrollbar-gutter: stable;
  z-index: 1000;
  @include for_media(mobileScreen) {
    width: 90%;
    min-width: 90%;
    height: 100%;
    top: 50%;
    right: 50%;
    transform: translate(50%, -50%);
    @include hide-scrollbar;
  }
  .mainWrapper {
    padding: 2em;
  }
  .headerWrapper {
    display: flex;
    justify-content: space-between;
    -webkit-user-select: none;
    user-select: none;
    .closeIconWrapper {
      background-color: rgb(189, 189, 189);
      border-radius: 5px;
      height: 30px;
      width: 30px;
      @include flex-center;
      @include clickable;
      svg {
        width: 15px;
        height: 15px;
      }
    }
  }

  .filterListContainer {
    margin: 1em 0;
    padding: 0 0.2em;
    overflow-y: auto;
    -webkit-user-select: none;
    user-select: none;
  }

  .btnWrapper {
    gap: 1em;
    position: sticky;
    bottom: 0;
    padding: 1em;
    background-color: $white;
    @include flex-center;
    button {
      width: 48%;
      height: 3.2em !important;
      border-radius: 35px !important;
    }
    .cancelBtn {
      border: 1px solid $accentBorder1 !important;
      background-color: $accentBgColor1 !important;
      color: $primaryColor !important;
      &:hover {
        background-color: $accentHover1 !important;
      }
    }
    .applyBtn {
      background-color: $accentColor2 !important;
      color: $primaryBgColor;
      &:hover {
        background-color: $accentHover2 !important;
      }
    }
  }
}

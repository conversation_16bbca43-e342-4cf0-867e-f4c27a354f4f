import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { Table } from 'semantic-ui-react';
import style from './invoiceTable.module.scss';
import { Link, useNavigate, useSearchParams } from 'react-router-dom';
import InvoiceCards from './InvoiceCards';
import { FileText } from 'lucide-react';
import { TableSortLabel } from '@mui/material';
import SlicedText from '../../../generic-components/SlicedText';
import Checkbox from '../../../ui-components/fields/Checkbox';
import { formatRelativeTime, getStatusClass, jsonKeyFormatLabel } from '../../../utils';
import { MsgIcon } from '../../../../assets/svgs';
import { mapAiInvoiceStatus } from '../../../utils/aiUtils';
import HorizontalScrollControls from '../../../custom-components/HorizontalScrollControls';
import { useAuth } from '../../../../contexts/AuthContext';
import { formatAmount } from '../../../utils/dateUtils';
import { useQuery } from '@tanstack/react-query';
import { getInvoiceStatuses } from '../../../services/invoiceServices';
import SortIndicator from '../../../ui-components/SortIndicator';

// Default columns for the invoice table
const defaultColumns = [
  'status',
  'invoice_date',
  'invoice_amount',
  'date_of_processing',
  'ticket_id',
  'supplier_name',
  'invoice_number',
  'file_name',
];

function getFormattedValue(invoice, columnKey) {
  switch (columnKey) {
    case 'status':
      const status = mapAiInvoiceStatus(invoice?.status);
      const isProcessing = Number(invoice?.status) === -1 || Number(invoice?.status) === 0;
      return (
        <p
          className={`${style.status} ${getStatusClass(status)} ${
            isProcessing ? 'animate-[fadePulse_2s_infinite]' : ''
          }`}
        >
          {status}
        </p>
      );

    case 'ticket_id':
      return (
        <Link to={`/ticket-view/${invoice?.ticket_id}`} onClick={(e) => e.stopPropagation()}>
          {invoice?.ticket_id}
        </Link>
      );

    case 'invoice_amount':
      return formatAmount(invoice?.invoice_amount);

    case 'date_of_processing':
      return formatRelativeTime(invoice?.date_of_processing, true);

    default:
      return invoice[columnKey] || '--';
  }
}

const InvoiceTable = ({
  data: invoices = [],
  selectedInvoices,
  setSelectedInvoices,
  invoiceDateSort,
  setInvoiceDateSort,
  isShowHeaderRow = true,
  isShowStickyCell = false,
  onSelectInvoices = null,
  onStatusUpdate = null,
  ...columnVisibility
}) => {
  const navigate = useNavigate();
  const { isMobileScreen, globSelectedBusiness } = useAuth();
  const [searchParams] = useSearchParams();
  const [hasInitialized, setHasInitialized] = useState(false);
  const [isSelectedAll, setSelectedAll] = useState(false);

  // Get file IDs of invoices with status -1 (In process) for polling
  const processingInvoiceIds = useMemo(() => {
    return invoices
      .filter((invoice) => Number(invoice?.status) === -1 || Number(invoice?.status) === 0)
      .map((invoice) => invoice?.file_id);
  }, [invoices]);

  const { data: statusUpdates } = useQuery({
    queryKey: ['invoiceStatuses', globSelectedBusiness?.business_id, processingInvoiceIds],
    queryFn: () => getInvoiceStatuses(globSelectedBusiness.business_id, processingInvoiceIds),
    enabled: !!globSelectedBusiness?.business_id && processingInvoiceIds.length > 0,
    refetchInterval: 5000,
    refetchIntervalInBackground: true,
    staleTime: 0,
  });

  const totalLengthOfSelectableInvoices = useMemo(
    () => invoices.filter((invoice) => invoice?.status === '3' || invoice?.status === '4').length,
    [invoices]
  );

  // validated invoices selection at first render
  useEffect(() => {
    if (!hasInitialized) {
      const initialSelected = invoices.filter((invoice) => invoice?.status === '3').map((invoice) => invoice.file_id);
      setSelectedInvoices(initialSelected);
      setHasInitialized(true);
    }
  }, [invoices, hasInitialized]);

  useEffect(() => {
    onSelectInvoices && onSelectInvoices(selectedInvoices);
  }, [selectedInvoices]);

  // Update invoice statuses when polling returns new data
  useEffect(() => {
    if (statusUpdates?.data && Array.isArray(statusUpdates.data) && onStatusUpdate) {
      const newInvoiceMap = {};
      statusUpdates.data.forEach((item) => {
        newInvoiceMap[item.file_id] = item;
      });
      onStatusUpdate(newInvoiceMap);
    }
  }, [statusUpdates]);

  const handleSelectAll = useCallback(
    (checked) => {
      const isIndeterminate =
        selectedInvoices?.length > 0 && selectedInvoices?.length < totalLengthOfSelectableInvoices;
      if (checked && !isIndeterminate) {
        setSelectedInvoices(
          invoices
            .filter((invoice) => invoice?.status === '3' || invoice?.status === '4')
            .map((invoice) => invoice.file_id)
        );
        setSelectedAll(true);
      } else {
        setSelectedAll(false);
        setSelectedInvoices([]);
      }
    },
    [selectedInvoices, invoices, totalLengthOfSelectableInvoices]
  );

  const handleSelectInvoice = useCallback((checked, fileId, status) => {
    if (status !== '3' && status !== '4') {
      return;
    }

    if (checked) {
      setSelectedInvoices((prev) => [...prev, fileId]);
    } else {
      setSelectedInvoices((prev) => prev.filter((id) => id !== fileId));
    }
  }, []);

  const visibleColumns = useMemo(
    () => [...defaultColumns, ...Object.keys(columnVisibility).filter((key) => columnVisibility[key])],
    [columnVisibility]
  );

  const handleViewInvoice = useCallback((e, invoice) => {
    e.stopPropagation();
    if (Number(invoice?.status) === 0 || Number(invoice?.status) === -2 || Number(invoice?.status) === -1) return;

    navigate(`/ai-extraction/f/${invoice.file_id}/b/${invoice?.business_id}/p/${searchParams.get('page') || 1}`);
  }, []);

  if (isMobileScreen) {
    return <InvoiceCards invoices={invoices} />;
  }
  return (
    <Table basic="very" className={style.invoiceTable}>
      {isShowHeaderRow && (
        <Table.Header className={style.tableHeaderRow}>
          <Table.Row>
            <Table.HeaderCell className={`${style.stickyCellLeft} ${style.stickyHeader}`}>
              <Checkbox
                checked={
                  (isSelectedAll || selectedInvoices?.length === totalLengthOfSelectableInvoices) &&
                  totalLengthOfSelectableInvoices > 0 &&
                  selectedInvoices?.length > 0
                }
                indeterminate={
                  selectedInvoices?.length > 0 && selectedInvoices?.length < totalLengthOfSelectableInvoices
                }
                onChange={handleSelectAll}
              />
            </Table.HeaderCell>
            {visibleColumns.map((columnKey) => (
              <Table.HeaderCell key={columnKey} className={`${columnKey} ${style.stickyHeader}`}>
                {columnKey === 'invoice_date' ? (
                  <div
                    className="flex items-center gap-2 cursor-pointer select-none"
                    onClick={() =>
                      setInvoiceDateSort((prev) => {
                        if (prev === 'desc') return 'asc';
                        if (prev === 'asc') return '';
                        return 'desc';
                      })
                    }
                  >
                    Invoice Date
                    <SortIndicator direction={invoiceDateSort} />
                  </div>
                ) : columnKey === 'date_of_processing' ? (
                  <div className="flex items-center gap-2">
                    Updated At
                    <SortIndicator direction={invoiceDateSort ? null : 'desc'} />
                  </div>
                ) : (
                  jsonKeyFormatLabel(columnKey)
                )}
              </Table.HeaderCell>
            ))}
            <Table.HeaderCell className={`${style.stickyCellRight} ${style.stickyHeader}`}>
              <HorizontalScrollControls containerId="invoices-table-container" />
            </Table.HeaderCell>
          </Table.Row>
        </Table.Header>
      )}

      <Table.Body>
        {invoices.map((invoice) => {
          return (
            <Table.Row
              key={invoice.id}
              className={`${style.tableRow} ${
                (Number(invoice?.status) === 0 || Number(invoice?.status) === -2 || Number(invoice?.status) === -1) &&
                style.disabledRow
              }`}
              onClick={(e) => handleViewInvoice(e, invoice)}
            >
              {/* Checkbox cell (sticky) */}
              <Table.Cell onClick={(e) => e.stopPropagation()} className={style.stickyCellLeft}>
                <Checkbox
                  checked={selectedInvoices?.includes(invoice.file_id)}
                  onChange={(checked) => handleSelectInvoice(checked, invoice.file_id, invoice?.status)}
                  disabled={invoice?.status !== '3' && invoice?.status !== '4'}
                />
              </Table.Cell>
              {visibleColumns.map((columnKey) => {
                if (columnKey === 'file_name') {
                  return (
                    <Table.Cell
                      key={columnKey}
                      className={style.subject}
                      onClick={(e) => {
                        e.stopPropagation();
                        window.open(invoice?.file_url, '_blank');
                      }}
                    >
                      <div className={style.fileNameWrapper}>
                        <FileText />
                        <SlicedText text={invoice?.file_name || 'No name'} sliceTill={35} />
                      </div>
                    </Table.Cell>
                  );
                }
                return <Table.Cell key={columnKey}>{getFormattedValue(invoice, columnKey)}</Table.Cell>;
              })}

              {/* View btn cell (sticky) */}
              <Table.Cell className={style.stickyCellRight}>
                <div className={style.messageWrapper} onClick={(e) => handleViewInvoice(e, invoice)}>
                  <div className={style.msgWrapper}>
                    <MsgIcon /> View
                  </div>
                </div>
              </Table.Cell>
            </Table.Row>
          );
        })}
      </Table.Body>
    </Table>
  );
};

export default InvoiceTable;

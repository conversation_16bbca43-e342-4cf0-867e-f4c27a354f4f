import React, { useState } from 'react';
import { Autocomplete, TextField, CircularProgress, Button } from '@mui/material';
import { useAuth } from '../../contexts/AuthContext';
import useQueryLoadMoreSearch from '../global/hooks/useQueryLoadMoreSearch';

export default function SearchAutocomplete({
  url,
  searchParam = 'search',
  onSelect,
  transformOptions = { key: 'id', label: 'name', value: 'id' },
  ...props
}) {
  const { globSelectedBusiness } = useAuth();
  const [inputValue, setInputValue] = useState('');

  const { options, isFetchingNextPage, isLoading, loadMore } = useQueryLoadMoreSearch({
    url,
    searchParam,
    searchValue: inputValue,
    transformOptions,
    enabled: !!globSelectedBusiness?.business_id,
  });

  return (
    <Autocomplete
      options={options}
      getOptionLabel={(option) => (option.loadMore ? '' : typeof option === 'string' ? option : option.label || '')}
      loading={isLoading}
      autoHighlight
      openOnFocus
      noOptionsText="No options available"
      onInputChange={(e, value) => setInputValue(value)}
      onChange={(e, value) => {
        if (value?.loadMore) {
          // Prevent selecting the fake loadMore option
          return;
        }
        onSelect?.(value);
      }}
      renderInput={(params) => (
        <TextField
          {...params}
          {...props}
          slotProps={{
            inputAdornment: {
              endAdornment: (
                <>
                  {(isLoading || isFetchingNextPage) && <CircularProgress color="inherit" size={20} />}
                  {params.InputProps?.endAdornment}
                </>
              ),
            },
          }}
        />
      )}
      renderOption={(props, option) => {
        if (option.loadMore) {
          return (
            <li {...props} key={option.key} className="flex justify-center">
              <Button
                size="small"
                onClick={(e) => {
                  e.stopPropagation();
                  loadMore();
                }}
                disabled={isFetchingNextPage}
              >
                {isFetchingNextPage ? 'Loading...' : 'Load More'}
              </Button>
            </li>
          );
        }
        return <li {...props}>{option.label}</li>;
      }}
    />
  );
}

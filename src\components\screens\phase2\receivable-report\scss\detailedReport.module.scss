@import '../../../../../assets/scss/main.scss';

.contentWrapper {
  display: flex;
  gap: 2em;
  width: 100%;
  max-height: 100%;
  min-height: 80%;

  .chartWrapper {
    width: 50%;
    // height: 85%;
    padding: 1em;
    background-color: $white;
    border-radius: 15px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    gap: 2.5em;

    canvas {
      max-width: 100% !important;
    }
  }

  .detailedWrapper {
    width: 50%;

    .overAllInfo {
      padding: 1em;
      background-color: #eaecf5;
      display: flex;
      justify-content: space-between;
      border-radius: 15px;

      .leftInfo {
        h4 {
          display: flex;
          gap: 1em;
          align-items: center;
          font-size: 1.3em !important;
          margin: 0;
          @include for_media(mobileScreen) {
            font-size: 1.1em !important;
          }
          svg {
            width: 30px;
            height: 30px;
          }
        }

        p {
          color: #252b37;
          font-size: 1.1em !important;
        }
      }

      .rightInfo {
        h4 {
          font-size: 1.3em !important;
          margin: 0;
          @include for_media(mobileScreen) {
            font-size: 1.1em !important;
          }
        }
      }
    }

    .detailsListContainer {
      padding: 0;

      // height: 65%;
      // overflow-y: auto;
      h5 {
        margin: 0;
        font-size: 1.3em !important;
      }
    }

    .accordWrapper {
      max-height: 48vh;
      overflow-y: auto;
      padding-right: 1em;

      @include for_media(mobileScreen) {
        padding-bottom: 20%;
      }
    }
  }
}

.actionBtnWrappers {
  display: flex;
  margin: 1.5em 0;
  padding: 0.2em;
  align-items: center;
  justify-content: space-between;
  background-color: #e9eaeb;
  border-radius: 13px;

  p {
    width: 50%;
    margin: 0;
    text-align: center;
    padding: 0.5em;
    border-radius: 10px;
    cursor: pointer;
    @include for_media(mobileScreen) {
      font-size: 1.1em !important;
    }
  }

  .activeTab {
    background-color: black;
    color: white;
    margin: 0.2em;
  }
}

.accordionTitle {
  display: flex;
}

.invoiceTab {
  max-height: 52vh !important;
}

.detailTitleContainer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 1em 0;

  .sortFilter {
    height: 3.5em;
    cursor: pointer;

    @include for_media(mobileScreen) {
      width: 27%;
      height: 3em;
      background-color: $white;
    }
  }
}

.filterWrapper {
  display: flex;
  margin: 1em 0;
  gap: 1em;

  .customInput {
    display: flex;
    align-items: center;
    gap: 1em;
    width: 80%;
    height: 3.7em;
    background-color: #f5f5f5;
    padding: 1em;
    border-radius: 13px;
    border: 1px solid #d5d7da;

    svg {
      width: 25px;
      height: 25px;
    }

    input {
      width: 85%;
      border: none !important;
      height: 2.5em;
      color: black !important;
      background-color: unset !important;
    }

    input:focus {
      outline: none !important;
      border: none !important;
    }

    input::placeholder {
      font-size: 1.2em !important;
    }

    @include for_media(mobileScreen) {
      width: 100%;
    }
  }
}

.sortFilter {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20%;
  background-color: #f5f5f5;
  border-radius: 13px;
  border: 1px solid #d5d7da;
  gap: 1em;
  cursor: pointer;

  svg {
    width: 22px;
    height: 22px;
  }

  p {
    font-size: 1.2em !important;
  }
}

@include for_media(mobileScreen) {
  .contentWrapper {
    flex-direction: column;
    padding: 1em 0;
  }

  .chartWrapper {
    width: 100% !important;
    background-color: unset;
    padding: 0;
  }

  .detailedWrapper {
    width: 100% !important;
  }
}

.sortBtnfilter {
  background: #021739;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20%;
  height: 3.5em;
  border-radius: 13px;
  border: 1px solid #d5d7da;
  gap: 1em;

  svg {
    width: 22px;
    height: 22px;
    fill: white;
    stroke: white;
  }
}

.sortBtnfilter svg {
  fill: white;
  /* For solid icons */
  stroke: white;

  /* For outline icons */
  svg {
    width: 22px;
    height: 22px;
  }
}

.emptyMsg {
  height: 60vh;
  display: flex;
  flex-direction: column;
  justify-content: center;
  gap: 2em;
  align-items: center;

  @include for_media(mobileScreen) {
    text-align: center;
    height: 45vh;
  }
}
